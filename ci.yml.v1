
Global:
    tool: build_submitter

Default:
    profile: [xs_master]

Profiles:
    - profile:
      name: master
      env: DECK_CENTOS6U3_K3
      command: export JAVA_HOME=/home/<USER>/buildkit/java/jdk1.7.0_75/ && export MAVEN_HOME=/home/<USER>/buildkit/maven/apache-maven-3.3.9/ && export PATH=/home/<USER>/buildkit/nodejs/nodejs-0.10.24/bin/:\${JAVA_HOME}/bin:\${MAVEN_HOME}/bin:$PATH && sh webbuild.sh && sh build.sh -q
      release: true

    - profile:
      name: change
      env: DECK_CENTOS6U3_K3
      command: export JAVA_HOME=/home/<USER>/buildkit/java/jdk1.7.0_75/ && export MAVEN_HOME=/home/<USER>/buildkit/maven/apache-maven-3.3.9/ && export PATH=/home/<USER>/buildkit/nodejs/nodejs-0.10.24/bin/:\${JAVA_HOME}/bin:\${MAVEN_HOME}/bin:$PATH && sh webbuild_new.sh && sh build.sh -q
      release: true

    - profile:
      name: xs_master
      env: DECK_CENTOS6U3_K3
      command: export JAVA_HOME=/home/<USER>/buildkit/java/jdk1.7.0_75/ && export MAVEN_HOME=/home/<USER>/buildkit/maven/apache-maven-3.3.9/ && export PATH=/home/<USER>/buildkit/nodejs/nodejs-0.10.24/bin/:\${JAVA_HOME}/bin:\${MAVEN_HOME}/bin:$PATH && sh webbuild_new.sh && sh build.sh -q
      release: true