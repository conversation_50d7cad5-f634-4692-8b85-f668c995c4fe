# 百度云控制台前端示例模块

> 百度云控制台项目参考文档：[文档参考](http://sandbox.bce.console.baidu-int.com/bce-docs/fe/console-setup/fe-setup/fe-repo.html)

## 安装环境

- 安装依赖

```shell
npm install --legacy-peer-deps
```

- 修改`hosts`文件如下

```shell
127.0.0.1 localhost.qasandbox.bcetest.baidu.com  # 本地沙盒
127.0.0.1 localhost.console.bce.baidu.com  # 本地公有云
TODO:待补充 # 本地虚商
```

## scripts

```shell
# 本地联调公有云或沙盒环境
# 结合 bce-config.js 中的 proxyTarget 配置区分两个环境
npm run dev
# 本地联调虚商环境
npm run dev:xs-console

#流水线构建公有云或沙盒
npm run build
#流水线构建虚商
npm run build:xs-console
```

# 其他内容TODO:待补充

## 相关文件说明

- `package.json`: 必要文件, 其中`name`与`main`为必要属性
- `bce-config.js`: 产品相关配置，包含`service`、`pathname`、`flags`，以及开发辅助配置`mockup`、`debug`、`proxyTarget`、`dependencies`、`webpack`
