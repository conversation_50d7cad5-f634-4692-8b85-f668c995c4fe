/**
 * 这里应该是注册一些sidebar
 *
 * @file sidebar.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {AppSidebar} from '@baidu/sui-biz';
import _ from 'lodash';
@decorators.asSidebar()
export class DefaultSidebar extends Component {
    static template = html`
        <app-sidebar title="数据仓库 Palo" active-name="{= active =}">
            <app-sidebar-item title="存算一体集群" name="list" link-to="/palo/#/palo/list" />
            <app-sidebar-item visible="{{canDecouplesd}}" title="存算分离集群" name="decoupledlist" link-to="/palo/#/palo/decoupledlist" />
            <app-sidebar-item title="标签" name="tag" iconLink="link" link-to="/tag/#/tag/instance/list" />
        </app-sidebar>
    `;

    static components = {
        'app-sidebar': AppSidebar,
        'app-sidebar-item': AppSidebar.Item,
    };

    initData() {
        return {
            canDecouplesd: false,
            active: 'list',
        };
    }

    inited() {
        const region = window.$context.getCurrentRegion().id;
        const canDecouplesd = region === 'bj';
        this.data.set('canDecouplesd', canDecouplesd);
        this.watch('active', (name) => this.fire('active', {name}));
    }
}
