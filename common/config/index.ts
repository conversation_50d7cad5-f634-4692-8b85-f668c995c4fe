/**
 * index
 *
 * @file index.js
 * <AUTHOR>
 */
import Enum from '@baidu/enum';
import _ from 'lodash';
import {BackUpTaskState, BackUpTaskType} from './constant';

// 请求静默
export const CILENT_SILENT = {
    'X-silence': true,
    'x-silent': true
};

export const PAYTYPE = new Enum(
    {
        alias: 'PREPAY',
        text: '包年包月',
        value: 'prepay'
    },
    {
        alias: 'POSTPAY',
        text: '按量付费',
        value: 'postpay'
    }
);

export const PALO_TYPE_MAPPING = new Enum(
    {
        alias: 'LeaderNode',
        name: 'Leader Node',
        value: 'palofe'
    },
    {
        alias: 'ComputeNode',
        name: 'Compute Node',
        value: 'palobe'
    },
    {
        alias: 'StudioNode',
        name: 'Studio Node',
        value: 'palostudio'
    }
);
export const NODE_TYPE_ENUM = new Enum(
    {
        alias: 'LEADER',
        name: 'Leader Node',
        value: 'palofe'
    },
    {
        alias: 'COMPUTE',
        name: 'Compute Node',
        value: 'palobe'
    },
    {
        alias: 'STUDIO',
        name: 'Studio Node',
        value: 'palostudio'
    }
);


export const PayType = {
    prepay: '包年包月',
    postpay: '按量付费',
};

export const clusterStatus = {
    Starting: 'processing',
    Running: 'normal',
    partial_Running: 'normal',
    Initializing: 'processing',
    partial_Initializing: 'processing',
    Partial_Running: 'processing',
    Stopping: 'processing',
    Stopped: 'unavailable',
    Deleting: 'error',
    Deleted: 'error',
    Extending: 'processing',
    ResizingConf: 'processing',
    Audit_deleting: 'error',
    Audit_deleted: 'error',
    Audit_stopping: 'processing',
    Audit_stopped: 'unavailable',
    Rollbacking: 'warning',
    Unknown: 'warning',
    NOT_START: 'warning',
    RUNNING: 'normal',
    FINISH: 'unavailable',
    FAILED: 'error',
    AuditInstalling: 'processing',
    PaloRestarting: 'processing',
    Restarting: 'processing',
    Upgrading: 'processing'
};

export const baseOrderItem = {
    serviceType: 'PALO',
    serviceName: '配置详情',
    configName: '价格',
    count: 1,
    duration: 1,
    region: 'bj',
    type: 'NEW',
    timeUnit: 'MONTH',
    managePrice: false, // 前端不传相关配置询价，直接传价格
    productType: 'prepay',
    flavor: [],
    configDetail: []
};

export const RechargeType = [
    {
        alias: 'month',
        text: '按月',
        value: 'month'
    },
    {
        alias: 'year',
        text: '按年',
        value: 'year'
    }
];

export const RechargeMonth = [
    {
        alias: 'one',
        text: '1个月',
        value: 1
    },
    {
        alias: 'two',
        text: '2个月',
        value: 2
    },
    {
        alias: 'three',
        text: '3个月',
        value: 3
    },
    {
        alias: 'four',
        text: '4个月',
        value: 4
    },
    {
        alias: 'five',
        text: '5个月',
        value: 5
    },
    {
        alias: 'six',
        text: '6个月',
        value: 6
    },
    {
        alias: 'seven',
        text: '7个月',
        value: 7
    },
    {
        alias: 'eight',
        text: '8个月',
        value: 8
    },
    {
        alias: 'nine',
        text: '9个月',
        value: 9
    }
];

export const RechargeYear = [
    {
        alias: 'one',
        text: '1年',
        value: 12
    },
];

export const leaderNumConfig = {
    min: 1,
    step: 2,
    max: 5
};

export const computeNumConfig = {
    stepStrictly: true,
    min: 1,
    step: 1,
    max: 100
};

// 路由
export const ROUTE_PATH = {
    deployList: '#/palo/list',
    deployDetail: '#/palo/detail',
};


export const taskStatus = {
    NOT_START: '未开始',
    RUNNING: '运行中',
    FINISH: '结束',
    FAILED: '失败'
}

export const stageType = {
    INIT: '初始化',
    UPGRADE_CHECK: '升级前置检查',
    UPGRADE: '升级',
    RESIZE: '变配',
    RESTORE_SNAPSHOT: '数据恢复',
    BACKUP_SNAPSHOT: '数据备份',
    CLUSTER_HEALTH_CHECK: '升级前置检查',
    UPGRADE_BE: 'BE升级',
    UPGRADE_FE: 'FE升级',
    AUDIT_INSTALL: '审计日志安装',
    AUDIT_INSTALL_CHECK: '安装前置检查',
}

export enum taskType {
    UPGRADE = 'UPGRADE',
    RESIZE = 'RESIZE'
}

// 默认所有
export const AllEnum = new Enum(
    {alias: 'ALL', text: '全部', value: ''}
);

export const BackUpTaskTypeEnum = new Enum(
    {alias: BackUpTaskType.ONE_TIME, text: '单次备份', value: BackUpTaskType.ONE_TIME},
    {alias: BackUpTaskType.PERIODIC, text: '周期备份', value: BackUpTaskType.PERIODIC},
);

export const BackUpTaskPeriodicEnum = new Enum(
    {alias: 'DAILY', text: '每天', value: 'DAILY'},
    {alias: 'WEEKLY', text: '每周', value: 'WEEKLY'},
    {alias: 'MONTHLY', text: '每月', value: 'MONTHLY'},
    {alias: 'YEARLY', text: '每年', value: 'YEARLY'},
);
  
// week类型
export const BackUpTaskTimeWeekType = new Enum(
    {alias: 'SUN', text: '周日', value: 1},
    {alias: 'MON', text: '周一', value: 2},
    {alias: 'TUE', text: '周二', value: 3},
    {alias: 'WED', text: '周三', value: 4},
    {alias: 'THU', text: '周四', value: 5},
    {alias: 'FRI', text: '周五', value: 6},
    {alias: 'SAT', text: '周六', value: 7}
);
  
// day类型
export const BackUpTaskTimeDayType = new Enum(
    ...(() => _.times(31, (n) => ({alias: `DAY_${n + 1}`, text: `${n + 1}号`, value: n + 1})))()
);

// month类型
export const BackUpTaskTimeMonthType = new Enum(
    ...(() => _.times(12, (n) => ({alias: `MONTH_${n + 1}`, text: `${n + 1}月`, value: n + 1})))()
);

export const BackUpTaskStateEnum = new Enum(
    {alias: BackUpTaskState.NOT_START, text: '未开始', value: BackUpTaskState.NOT_START, klass: 'normal'},
    {alias: BackUpTaskState.RUNNING, text: '备份中', value: BackUpTaskState.RUNNING, klass: 'process'},
    {alias: BackUpTaskState.CANCELLING, text: '取消中', value: BackUpTaskState.CANCELLING, klass: 'success'},
    // {alias: BackUpTaskState.CANCELLED, text: '已取消', value: BackUpTaskState.CANCELLED, klass: 'success'},
    {alias: BackUpTaskState.FAILED, text: '备份失败', value: BackUpTaskState.FAILED, klass: 'danger'},
    {alias: BackUpTaskState.FINISHED, text: '备份成功', value: BackUpTaskState.FINISHED, klass: 'success'},
    {alias: BackUpTaskState.RESTORING, text: '备份恢复中', value: BackUpTaskState.RESTORING, klass: 'success'},
    {alias: BackUpTaskState.RESTORE_CANCELLING, text: '取消恢复', value: BackUpTaskState.RESTORE_CANCELLING, klass: 'success'},
    {alias: BackUpTaskState.RESTORE_FINISHED, text: '恢复成功', value: BackUpTaskState.RESTORE_FINISHED, klass: 'danger'},
    {alias: BackUpTaskState.RESTORE_FAILED, text: '恢复失败', value: BackUpTaskState.RESTORE_FAILED, klass: 'danger'},
);

export const taskTypeEnum = new Enum(
    {alias: 'UPGRADE', text: '集群升级', value: 'UPGRADE'},
    {alias: 'RESIZE', text: '集群变配', value: 'RESIZE' },
    {alias: 'BACKUP_SNAPSHOT', text: '数据备份', value: 'BACKUP_SNAPSHOT'},
    {alias: 'RESTORE_SNAPSHOT', text: '数据恢复', value: 'RESTORE_SNAPSHOT'},
    {alias: 'AUDIT_INSTALL', text: '审计日志安装', value: 'AUDIT_INSTALL'},
);

export const clusterStatusFilter = new Enum(
    {text: '创建中', value: 'Initializing', alias: 'Initializing', classname: 'processing'},
    {text: '部分创建中', value: 'partial_Initializing', alias: 'partial_Initializing', classname: 'processing'},
    {text: '运行中', value: 'Running', alias: 'Running', classname: 'normal'},
    {text: '部分运行中', value: 'partial_Running', alias: 'partial_Running', classname: 'processing'},
    {text: '重启中', value: 'PaloRestarting', alias: 'PaloRestarting', classname: 'processing'},
    {text: '部分重启中', value: 'partial_PaloRestarting', alias: 'partial_PaloRestarting', classname: 'processing'},
    {text: '正在变配', value: 'ResizingConf', alias: 'ResizingConf', classname: 'processing'},
    {text: '部分正在变配', value: 'partial_ResizingConf', alias: 'partial_ResizingConf', classname: 'processing'},
    {text: '正在删除', value: 'Deleting', alias: 'Deleting', classname: 'error'},
    {text: '部分正在删除', value: 'partial_Deleting', alias: 'partial_Deleting', classname: 'warning'},
    {text: '正在启动', value: 'Starting', alias: 'Starting', classname: 'processing'},
    {text: '部分正在启动', value: 'partial_Starting', alias: 'partial_Starting', classname: 'processing'},
    {text: '正在停止', value: 'Stopping', alias: 'Stopping', classname: 'processing'},
    {text: '部分正在停止', value: 'partial_Stopping', alias: 'partial_Stopping', classname: 'processing'},
    {text: '已停止', value: 'Stopped', alias: 'Stopped', classname: 'unavailable'},
    {text: '部分已停止', value: 'partial_Stopped', alias: 'partial_Stopped', classname: 'unavailable'},
    {text: '欠费正在停止', value: 'Audit_stopping', alias: 'Audit_stopping', classname: 'processing'},
    {text: '部分欠费正在停止', value: 'partial_Audit_stopping', alias: 'partial_Audit_stopping', classname: 'processing'},
    {text: '欠费已停止', value: 'Audit_stopped', alias: 'Audit_stopped', classname: 'unavailable'},
    {text: '部分欠费已停止', value: 'partial_Audit_stopped', alias: 'partial_Audit_stopped', classname: 'unavailable'}
);

export const computeGroupStatusEnum = new Enum(
    {text: '创建中', value: 'Initializing', alias: 'Initializing', classname: 'processing'},
    {text: '运行中', value: 'Running', alias: 'Running', classname: 'normal'},
    {text: '正在变配', value: 'ResizingConf', alias: 'ResizingConf', classname: 'processing'},
    {text: '重启中', value: 'PaloRestarting', alias: 'PaloRestarting', classname: 'processing'},
    {text: '正在删除', value: 'Deleting', alias: 'Deleting', classname: 'error'},
    {text: '正在启动', value: 'Starting', alias: 'Starting', classname: 'processing'},
    {text: '正在停止', value: 'Stopping', alias: 'Stopping', classname: 'processing'},
    {text: '已停止', value: 'Stopped', alias: 'Stopped', classname: 'unavailable'},
    {text: '欠费正在停止', value: 'Audit_stopping', alias: 'Audit_stopping', classname: 'processing'},
    {text: '欠费已停止', value: 'Audit_stopped', alias: 'Audit_stopped', classname: 'unavailable'},
);

// 颜色相关
export const COLOR_CONF = {
    defaultColor: '#2468f2',
    disableColor: '#b8babf'
};