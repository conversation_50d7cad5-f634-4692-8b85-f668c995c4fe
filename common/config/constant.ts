//  备份任务状态
export const  BackUpTaskState = {
    NOT_START: 'NOT_START',
    RUNNING: 'RUNNING',
    CANCELLING: 'CANCELLING',
    FAILED: 'FAILED',
    FINISHED: 'FINISHED',
    CANCELLED: 'CANCELLED',
    // 正在备份恢复
    RESTORING: 'RESTORING',
    // 恢复正在被取消
    RESTORE_CANCELLING: 'RESTORE_CANCELLING',
    // 备份恢复成功
    RESTORE_FINISHED: 'RESTORE_FINISHED',
    // 备份恢复失败
    RESTORE_FAILED: 'RESTORE_FAILED',
}

export const backupStatus = {
    NOT_START: 'unavailable',
    RUNNING: 'processing',
    CANCELLING: 'processing',
    FAILED: 'error',
    FINISHED: 'normal',
    CANCELLED: 'unavailable',
    // 正在备份恢复
    RESTORING: 'processing',
    // 恢复正在被取消
    RESTORE_CANCELLING: 'processing',
    // 备份恢复成功
    RESTORE_FINISHED: 'normal',
    // 备份恢复失败
    RESTORE_FAILED: 'error',
};

export const clusterStatusMap = {
    Starting: 'Starting',
    ResizingConf: 'ResizingConf',
    Stopped: 'Stopped',
    FINISH: 'FINISH',
    Running: 'Running',
    Initializing:  'Initializing',
}

export const BackUpTaskType = {
    ONE_TIME: 'ONE_TIME',
    PERIODIC: 'PERIODIC',
    MIGRATION: 'MIGRATION'
}

// 监控相关
export const SCOPE = 'BCE_PALO';

export const BOXER1_MAX_ID = 1073741823;


export const paloStatusConfig = {
    // 显示或者禁用启动&停止&删除按钮
    allowStart: ['Stopped'],
    allowStop: ['Running'],
    allowDelete: ['Running', 'Stopped'],
    allowStretch: ['Running']
};

export const taskType = {
    UPGRADE: '版本升级',
    RESIZE: '集群变配',
    BACKUP_SNAPSHOT: '数据备份',
    RESTORE_SNAPSHOT: '数据恢复',
    AUDIT_INSTALL: '审计日志安装'
}

export const PAGER = {
    page: 1,
    total: 0,
    pageSize: 10,
    pageSizes: [10, 20, 50]
}