/**
 * 校验规则
 */

// 必填
export const required = {
    required: true,
    message: '此项不能为空'
};

export function isValidPassword(str: string) {
    const hasLower = /[a-z]/.test(str);
    const hasUpper = /[A-Z]/.test(str);
    const hasDigit = /\d/.test(str);
    const hasSpecial = /[\W_]/.test(str);
    
    // 计算满足的条件个数
    const count = [hasLower, hasUpper, hasDigit, hasSpecial].filter(Boolean).length;
    
    return count >= 3;
}


export function passwordValidator (rule: any, value: string, callback: (arg0: string | undefined) => void) {
    // 验证密码是否规范
    const message = '支持小写字母(a-z)、大写字母(A-Z)、数字(0-9)、!@#$%^&*()_+-=[]{};:,<.>/?| ,并且至少包含其中三种, 长度8~16个字符';
    const reg2 = /^[A-Za-z\d!@#$%^&*()_+\-=\[\]{};:,<.>/?\|]+$/;
    if (!isValidPassword(value) || !reg2.test(value) || value.length < 8 || value.length > 16) {
        return callback(message);
    }
    callback();
}

//名称校验：支持字母(a-z及A-Z)、数字(0-9),长度小于20个字符
export function nameValidator (rule: any, value: string, callback: (arg0: string | undefined) => void) {
    const message = '支持字母(a-z及A-Z)、数字(0-9),长度小于20个字符';
    const reg = /^[a-zA-Z0-9]{1,20}$/i;
    if (!reg.test(value)) {
        return callback(message);
    }
    callback();
}

//名称校验：支持字母(a-z及A-Z)、数字(0-9),（-）、(_)、（.）长度小于20个字符
export function taskNameValidator (rule: any, value: string, callback: (arg0: string | undefined) => void) {
    const message = '支持字母(a-z及A-Z)、数字(0-9),（-）、(_), 6-20个字符';
    const reg = /^[\w-]{6,20}$/i;
    if (!reg.test(value)) {
        return callback(message);
    }
    callback();
}

export function retentionDaysValidator (rule: any, value: string, callback: (arg0: string | undefined) => void) {
    const message = '最小为1天, 最大为3650天';
    if (value === '') {
        return callback();
    }
    if (!Number.isInteger(Number(value))) {
        return callback('请输入整数');
    }
    if (Number(value) < 1 || Number(value) > 3650) {
        return callback(message);
    }
    callback();
}
