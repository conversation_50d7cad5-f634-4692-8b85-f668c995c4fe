/**
 * @file common输出
 * <AUTHOR>
 */
import {
    html,
    ServiceFactory,
    FlagService as FlagServiceDef,
    ContextService as ContextServiceDef,
} from '@baiducloud/runtime';

export { html };

export * from './sidebar';
export * from './client';
export * from './docs';
export { default as decorators } from './decorators';
export const FlagService = ServiceFactory.resolve('$flag') as FlagServiceDef;
export const ContextService = ServiceFactory.resolve('$context') as ContextServiceDef;
