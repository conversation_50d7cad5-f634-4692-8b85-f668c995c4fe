/**
 * client.js
 *
 * @file client.js
 * <AUTHOR>
 */

import {decorators, ServiceFactory} from '@baiducloud/runtime';
import HttpClient from '@baiducloud/httpclient';
import {API} from './api/index';

@decorators.asService('$http')
class PaloClient extends HttpClient {
    /**
     * RestClient的delete方法不支持body传参，单独封装
     *
     * @param {*} url 请求url
     * @param {*} data 请求数据
     * @param {*} config 额外配置
     */
    constructor() {
        super({}, ServiceFactory.resolve('$context'));
    }

    /**
     * 封装Post
     * 
     * @param apiName api/index中API的属性名
     * @param data 传递的参数
     * @returns 
     */
    paloPost(apiName: string, payload: any): any {
        return this.post(API[apiName], payload);
    }

    paloDelete(apiName: string, payload: any): any {
        return this.delete(API[apiName], payload);
    }

}
export default new PaloClient();
