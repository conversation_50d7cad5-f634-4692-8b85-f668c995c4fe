/**
 * index
 *
 * @file index.js
 * <AUTHOR>
 */

export const API = {
    // 集群列表
    paloDeployList: '/api/palo/deploy/list',
    getSearchTagList: '/api/tag/list',
    paloSeperateList: '/api/palo/deploy/decoupled/list',
    paloSeperateEditName: '/api/palo/deploy/decoupled/editDeployName',
    // 套餐升级后创建集群时候的节点设置信息
    paloListWithSlot: '/api/palo/module/listWithSlot',
    // 新建集群
    paloOrderBccConfirm: '/api/palo/order/v2/bcc/confirm?orderType=NEW',

    // 存算分离创建集群提交订单
    paloSeperateConfirm: '/api/palo/order/v2/decoupled/bcc/confirm?orderType=NEW',

    // 创建集群时后端验证
    paloClusterCreateCheckV2: '/api/palo/deploy/v2/create/check',

    // 存算分离创建集群提交订单验证接口
    paloSeperationCreateCheckV2: '/api/palo/deploy/v2/decoupled/create/check',

    // 集群详情，boxer2更新接口url为/api/palo/deploy/detail/new
    paloDeployDetail: '/api/palo/deploy/detail/new',

    // 存算分离详情接口
    paloSeperateDetail: '/api/palo/deploy/decoupled/detail/new',

    paloComputeGroupList: '/api/palo/deploy/decoupled/compute_group/list',

    paloEditGroupName: '/api/palo/deploy/decoupled/editComputeGroupName',

    paloGroupDelete: '/api/palo/deploy/decoupled/compute_group/delete',

    // 启动集群
    paloDeployStart: '/api/palo/deploy/start',

    // 停止集群
    paloDeployStop: '/api/palo/deploy/stop',

    // 删除集群
    paloDeployDelete: '/api/palo/deploy/delete',

    paloSeperateDeployDelete: '/api/palo/deploy/decoupled/delete',

    // 集群伸缩
    paloResizeStretch: '/api/palo/resize/confirm?orderType=RESIZE',

    // 监控
    bcmSummary: '/api/bcm/alarm/state/summary',

    // 监控页拉取计算组
    paloMonitorComputeGroup: '/api/palo/deploy/decoupled/monitor/cg_info',

    // 集群伸缩时后端验证
    // paloSubmitStretchCheck: '/api/palo/deploy/resize/check/new',

    // 集群伸缩check
    paloResizeCheck: '/api/palo/resize/check',

    // 取消伸缩
    paloCancelStretch: '/api/palo/order/cancel_resize',

    // 配置修改
    paloSubmitConfiguration: '/api/palo/module/config/update',

    // 重置密码
    paloSubmitPassword: '/api/palo/deploy/password/reset',

    // 新套餐获取总价，询价
    paloOrderGetNewPackagePrice: '/api/palo/order/getNewPackagePrice',

    // 存算分离询价
    paloOrderSeperationPrice: '/api/palo/order/decoupled/getNewPackagePrice',

    // 预付费集群伸缩时获取价格
    paloOrderStretchPrepayPrice: '/api/palo/order/stretch-prepay-price',

    // 确认续费
    paloRenewConfirm: '/api/palo/renew/confirm?orderType=RENEW',

    // 节点网络
    paloVpcs: '/api/network/v1/vpcs',

    // 用户可用区信息
    paloZoneList: '/api/zone/list/v2',

    // 节点子网
    paloSubnets: '/api/network/v1/subnets',

    // 获取网络安全组列表
    paloSecurityList: '/api/network/v1/security/list_select',

    // 资源详情
    paloSourceDetail: '/api/palo/deploy/detail/check',

    // Eip相关
    paloEipInstanceList: '/api/palo/deploy/eip/list/unused',
    paloEipBind: '/api/palo/deploy/eip/bind',
    paloEipUnbind: '/api/palo/deploy/eip/unbind',

    // 取消计费变更
    cancelAlterProductType: '/api/palo/change/confirm?orderType=CANCEL_TO_POSTPAY',
    // 启动，停止实例
    startInstance: '/api/palo/deploy/instance/start',
    stopInstance: '/api/palo/deploy/instance/stop',
    // 删除实例
    deleteInstance: '/api/palo/deploy/instance/delete',

    // 服务开通检测
    paloCheckActivate: '/api/palo/activate/check',
    // 开通服务
    paloServiceActivate: '/api/palo/activate',
    // 编辑单集群标签
    paloUpdateTags: '/api/palo/tag/updateTags',
    // 多集群新增标签
    paloBatchInsertTags: '/api/palo/tag/batchInsertTags',
    paloPackageStock: '/api/palo/module/packageStock',
    paloAppStock: '/api/palo/module/appStock',
    // 服务发布点
    paloServicePointCreate: '/api/palo/deploy/createPublishingPoint',
    paloServicePointCheck: '/api/palo/deploy/service/publishingPoint/check',

    palolistWithSlotandStock: '/api/palo/module/resizebcc/listWithSlotandStock',

    paloTaskList: '/api/palo/deploy/clusterTasks',

    // 备份列表
    paloBackupTaskList: '/api/palo/backup/task/list',
    // 创建备份数据库列表
    paloBackupDatabaseList: '/api/palo/backup/schema/database/get',
    // 创建备份数据库表信息
    paloBackupTableList: '/api/palo/backup/schema/table/get',
    // 创建备份
    paloBackupCreate: '/api/palo/backup/task/create',
    // 查询当前指定存储桶
    paloCurrentBucket: '/api/palo/backup/bucket/current',
    // 删除备份任务
    paloBackupTaskDelete: '/api/palo/backup/task/delete',

    // 备份详情
    paloBackuoDetail: '/api/palo/backup/task/detail',

    // 备份快照列表
    paloSnapshotList: '/api/palo/backup/snapshot/list',

    // 存储桶下拉列表数据
    paloBucketList: '/api/palo/backup/bucket/list',

    // 指定存储桶
    paloBucketDesignate: '/api/palo/backup/bucket/designate',

    // 修改备份任务
    paloTaskUpdate: '/api/palo/backup/task/update',

    paloSnapShotCancel: '/api/palo/backup/snapshot/cancel',

    paloSnapShotDelete: '/api/palo/backup/snapshot/delete',

    paloSnapShotDetail: '/api/palo/backup/snapshot/detail',
    // 数据恢复
    paloSnapShotRestore: '/api/palo/backup/snapshot/restore',
    // 取消数据恢复
    paloSnapShotRestoreCancel: '/api/palo/backup/snapshot/restore/cancel',

    paloVersionList: '/api/palo/module/getPaloCreateVersion',

    paloSeperateVersionList: '/api/palo/module/decoupled/getPaloCreateVersion',

    upgradeVersion: '/api/palo/upgrade/confirm',

    getUpgradeList: '/api/palo/upgrade/upgradeList',

    // 备份恢复查询权限
    backupCheckAuth: '/api/palo/backup/auth/check',

    // 备份恢复授权
    backupAuthprize: '/api/palo/backup/authorize',

    // 查询分析列表接口
    slowAndLargeQuery: `/api/palo/audit/query/slowAndLargeQuery`,

    // 查询分析导出数据接口
    exportAudit: '/api/palo/audit/export/exportData',
    // 存量集群安装插件
    auditLoader: '/api/palo/audit/installAudit',

    // 修改缓存配置
    kernelUpdate: '/api/palo/kernel/conf/upsert',

    restartInstance: '/api/palo/deploy/restart/instance',

    deletePoint: '/api/palo/deploy/deletePublishingPoint',
    listCuSlot: '/api/palo/module/decoupled/listWithSlot',

    // 计算组变配弹窗使用

    getCdsInfo: '/api/palo/resize/decoupled/getCdsInfo',

    upgradeCuConfirm: '/api/palo/resize/decoupled/confirm?orderType=RESIZE',

    upgradeCuCheck: '/api/palo/resize/decoupled/check',

    getUpgradePrice: '/api/palo/order/decoupled/stretch-prepay-price',

    consumerGroupPrice: '/api/palo/order/decoupled/compute_group/getNewPackagePrice',

    // 分时弹性相关
    scheduleCreate: '/api/palo/deploy/decoupled/compute_group/schedule_strategy/update',
    scheduleList: '/api/palo/deploy/decoupled/compute_group/schedule_strategy/list',
    scheduleEnable: '/api/palo/deploy/decoupled/compute_group/schedule_strategy/enable',

    computeGroupRestart: '/api/palo/deploy/decoupled/compute_group/restart',
    getCreatePrice: '/api/palo/order/decoupled/compute_group/getNewPackagePrice',

    createCuCheck: '/api/palo/deploy/v2/decoupled/compute_group/create/check',

    createCuConfirm: '/api/palo/order/v2/decoupled/compute_group/confirm?orderType=NEW',

    // engine 相关接口
    engineAuthUserList: '/api/palo/engine/auth/user/list',
    engineAuthUserEdit: '/api/palo/engine/auth/user/alter',
    engineAuthUserDelete: '/api/palo/engine/auth//user/delete',
    engineAuthUserCreate: '/api/palo/engine/auth/user/create',
    engineAuthUserRestPwd: '/api/palo/engine/auth/user/resetPassword',
    engineAuthRoleList: '/api/palo/engine/auth/role/list',
    engineAuthRoleDelete: '/api/palo/engine/auth/role/delete',
    engineAuthRoleCreate: '/api/palo/engine/auth/role/create',
    engineAuthRoleEdit: '/api/palo/engine/auth/role/alter',

    // 用户详情
    engineAuthUserDetail: '/api/palo/engine/auth/user/detail',

    // 资源列表
    listResourceAuth: '/api/palo/engine/auth/user/listResourceAuth',

    // Global 权限
    engineAuthGlobalList: '/api/palo/engine/auth/priv/global',
    // Catalog 权限
    engineAuthCatalogList: '/api/palo/engine/auth/priv/catalog',
    // Database 权限
    engineAuthDatabaseList: '/api/palo/engine/auth/priv/database',
    // Table 权限
    engineAuthTableList: '/api/palo/engine/auth/priv/table',
    // 行列权限
    engineAuthRowColumnList: '/api/palo/engine/auth/priv/colAndRow',
};
