/**
 * 装饰器
 *
 * @file common/decorators.js
 * <AUTHOR>
 */

import _ from 'lodash';
import { SanComponent } from 'san';
import { decorators, isSanCmpt } from '@baiducloud/runtime';
import * as SUI from '@baidu/sui';
import * as SUIBIZ from '@baidu/sui-biz';

const { asComponent, invokeComp } = decorators;
const uiComponents: { [key: string]: SanComponent<any> } = {};
const comps: string[] = [];

// 加载SUI
_.each(SUI, (Com: any, name) => {
    setComponents(Com, name as string);
    const subComKeys: string[] = Object.keys(Com);
    if (subComKeys.length > 0) {
        subComKeys.forEach((key) => {
            setComponents(Com[key], `${name}-${key}`);
        });
    }
});

// 加载SUI业务组件
_.each(SUIBIZ, (Com: any, name) => {
    if (isSanCmpt(Com)) {
        const invokeName = `@biz-${_.kebabCase(name)}`;
        asComponent(invokeName)(Com);
        Com[Symbol.for('ComponentCategory')] = false;
        uiComponents[`biz-${_.kebabCase(name)}`] = Com;
        comps.push(invokeName);
    }
});

function setComponents(Com: any, name: string) {
    if (isSanCmpt(Com)) {
        const invokeName = `@sui-${_.kebabCase(name)}`;

        asComponent(invokeName)(Com);
        Com[Symbol.for('ComponentCategory')] = false;
        uiComponents[`sui-${_.kebabCase(name)}`] = Com;
        comps.push(invokeName);
    }
}

export default {
    ...decorators,

    onRegionChange: (url: string = '#'): ClassDecorator => (Klass) => {
        Klass.prototype.onRegionChange = function () {
            location.hash = url;
            location.reload();
        };
        return Klass;
    },

    invokeUI: invokeComp.apply(this, comps),
};

export const debounce = (time: number = 200) => {
    return (_target: any, _properKey: string, descriptor: PropertyDescriptor) => {
        const fn: Function = descriptor.value;
        let timer: number | null;
        descriptor.value = function(...args: any[]) {
            if (!timer) {
                timer = window.setTimeout(() => {
                    fn.apply(this, args);
                    timer && window.clearTimeout(timer);
                    timer = null;
                }, time);
            }
        };
    };
};

// 节流装饰器
export const throttle = (time: number = 200) => {
    return (_target: any, _properKey: string, descriptor: PropertyDescriptor) => {
        const fn: Function = descriptor.value;
        let timer: number | null;
        descriptor.value = function (...args: any[]) {
            if (!timer) {
                fn.apply(this, args);
                timer = window.setTimeout(() => {
                    timer && window.clearTimeout(timer);
                    timer = null;
                }, time);
            }
        };
    };
};
