/**
 * 检测是否激活
 *
 * @file check.ts
 * <AUTHOR>
 */

import {router} from 'san-router';
import HttpClient from '@baiducloud/httpclient';

const api = new HttpClient({}, {
    getCsrfToken() {
        return this.$cookie.get('bce-user-info');
    },
    getCurrentRegion() {
        return window.$context.getCurrentRegion();
    }
});

const checkActive = async () => {
    const result = await api.post('/api/palo/activate/check');
    if (!result) {
        router.locator.redirect('/palo/active');
        return;
    }
    else {
        return;
    }
};

export default checkActive;