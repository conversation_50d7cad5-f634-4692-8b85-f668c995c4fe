/**
 * 文档链接
 *
 * @file common/docs.js
 * <AUTHOR>
 */

import { ServiceFactory } from '@baiducloud/runtime';

// 这里配置path即可！！！！！！！！！！！！
// 私有化交付时，文档服务的域名会不同，下面的DocService会为业务动态渲染环境中的文档服务域名
const DOCLINKS = {
    /** 产品首页文档 */
    overview: '/doc/CDN/index.html',
    charging: '/doc/CDN/s/qjwvyfhxq',
};

ServiceFactory.register('$doc', ServiceFactory.create('$doc', DOCLINKS));

export const DocService = ServiceFactory.resolve('$doc') as typeof DOCLINKS;
