/**
 * 覆盖全局的ESL配置，加载定制化配置
 *
 * @file bootstrap.js
 * <AUTHOR>
 */

import './common';
import './pages';
import './style';
import checkActive from '@common/check-active';

// 动态创建 link 标签
function loadCSS(filename: string) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = filename;
    document.head.appendChild(link);
}
// eslint-disable-next-line no-undef
const hashID = __WEBPACK_TIME_ID__;
// 使用 __WEBPACK_TIME_ID__ 全局变量获取当前构建的唯一 hash 值加载 css
const cssFileName = `https://bce.bdstatic.com/console/static/palo/bootstrap.${hashID}.css`;
hashID && loadCSS(cssFileName);

export async function start() {
    await checkActive();
    return Promise.resolve();
}

export function stop() {
    return Promise.resolve();
}
