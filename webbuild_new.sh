#!/bin/sh
set -x

export WORKSPACE=$(readlink -f $(dirname $0)) 

# Compile the frontend source code.
cd ${WORKSPACE}

# export PATH=$NODEJS_20_11_1_BIN:$PATH
echo "node: $(node -v)"
echo "npm: v$(npm -v)"

# 设置编译机群上的npm变量
npm config set registry http://registry.npm.baidu-int.com

# 如果NODE_ENV为production, npm5以上版本就不会安装devDependencies.
export NODE_OPTIONS=--openssl-legacy-provider
npm install --legacy-peer-deps
npm run build

cd ${WORKSPACE}
rm -rf output
mv dist output