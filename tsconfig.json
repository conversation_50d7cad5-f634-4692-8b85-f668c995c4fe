{
    "compilerOptions": {
        "target": "es5",
        "module": "commonjs",
        "removeComments": true,
        "isolatedModules": true,
        "strict": true,
        "noImplicitAny": true,
        "strictNullChecks": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "experimentalDecorators": true,
        "emitDecoratorMetadata": true,
        "baseUrl": "./",
        "paths": {
            "@/": ["/*"],
            "@common/*": ["common/*"],
            "@components/*": ["components/*"],
            "@pages/*": ["pages/*"],
            "@style": ["style/*"],
        }
    },
    "exclude": ["output", "dist", "node_modules"]
}
