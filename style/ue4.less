/*
 * @Author: <EMAIL>
 * @Date: 2022-12-05 14:35:51
 * @Last Modified by: <EMAIL>
 * @Last Modified time: 2024-05-08 14:20:33
 */
@tabSelectBgColor: #e6f0ff;
@tabBorderColor: rgba(242, 242, 244, 1);
@titleColor: #151b26;
@backTextColor: #84868c;
/** 状态-基础 */
@status-base: #151a26;
/** 状态-灰 */
@status-grey: #bbbabf;
/** 状态-红 */
@status-red: #f33e3e;
/** 状态-橙 */
@status-origin: #ff9326;
/** 状态-绿 */
@status-green: #30bf13;

@primary-color: #2468f2;

.ue4-card {
    min-width: 1480px;
    margin: 0 16px 16px;
    padding: 24px;
    // border-radius: 6px;
    background-color: #fff;

    .title {
        margin-bottom: 16px;

        label {
            font-weight: 500;
            font-size: 16px;
        }
    }

    .s-form {
        .s-form-item {
            margin-top: 24px;
            margin-bottom: 0;

            .s-form-item-label {
                width: 80px;

                .slot-label {
                    vertical-align: middle;
                }
            }

            .s-form-item-error,
            .s-form-item-extra,
            .s-form-item-help {
                padding: 4px 0 0;
            }
        }
    }
}

.s-legend {
    border: none;
}

.update-card {
    padding: 24px;
    margin: 16px;
    width: calc(~'100% - 32px');
    margin-bottom: 0;
    border-radius: 6px;
    background-color: #fff;

    .title {
        margin-bottom: 24px;

        label {
            font-weight: 500;
            font-size: 16px;
        }
    }
}

// sui版本270 -> 316样式问题

.s-daterangepicker-popup {
    width: auto;

    .s-daterangepicker-popup-panel {
        width: 272px;
    }
}

.s-picker-shortcut {
    height: 40px;
    line-height: 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px 12px 10px 10px;
    background-color: #f7f7f9;
    font-size: 12px;
}

.s-button .s-loading-nested .s-loading-wrap {
    position: static;
    background: none;
}

/** status状态修改 */
.normal.status,
.error.status,
.warning.status,
.unavailable.status,
.rolling.status,
.waiting.status {
    color: @status-base;
}

.unavailable.status::before {
    color: @status-grey;
}

.normal.status::before {
    color: @status-green;
}

.warning.status::before {
    color: @status-origin;
}

.error.status::before {
    color: @status-red;
}

.processing.status::before {
    position: relative;
    border-radius: 8px;
    margin-right: 8px;
    background-color: #d4e5ff;
    color: #528eff;
    box-shadow: 0 0 0px 2px #d4e5ff;
    animation: flash 1s infinite;
}

.waiting.status::after {
    color: @primary-color;
}

.back-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: @backTextColor;
    font-size: 14px;

    .back-text {
        margin-left: 4px;
        font-size: 14px;
    }

    .s-icon {
        fill: @backTextColor;
    }

    &:hover {
        .back-text {
            color: var(--defaultColor);
        }

        .s-icon {
            fill: var(--defaultColor);
        }
    }
}

.ue4-drawer-card {
    border-bottom: 1px solid #e8e9eb;

    .title {
        margin: 16px 0;
    }
}

.s-form-item-label-required > label:before {
    margin-right: 0;
}

// 根样式
:root {
    --borderColor: #aaaaaa;
    --borderColor2: #e8e9eb;
    --borderColor3: #dddddd;
    --borderColor4: #cccccc;
    --errorColor: #f33e3e;
    --successColor: #30bf13;
    --defaultColor: #2468f2;
    --warningColor: #ff9326;
    --unavailableColor: #999999;
    --whiteColor: #ffffff;
    --blackColor: #000000;
    --bgColor: #f5f5f5;
    --bgColor2: #f9f9f9;
    --blackHeadColor: #333333;
    --descColor: #84868c;
    --descColor2: #666666;
    --descColor3: #2468f2;
    --shadowColor: rgba(0, 0, 0, 0.1);
    --priceColor: #f33d3d;
    --formErrColor: #d0021b;
    --backgroundColor: #f7f7f9;
    --stepLineBackgroundColor: #dadbdd;
    --titleColor: #151b26;
    --priceDescColor: #5c5f66;
    --failColor: #b8babf;
}

// 详情页
.app-detail-page {
    background: #f7f7f9;

    .app-detail-page-title {
        background: #fff;
        margin: 0;
        padding: 0 20px;
    }

    .app-detail-page-content {
        background: #fff;
    }
}

// 解决两个loading问题
.s-table {
    .s-table-loading-content {
        background-image: none;
        background: none;
    }
}

// billing确认订单页面
.billing-confirm {
    min-width: 1280px;
    overflow-x: auto;
    .order-legend {
        .item {
            display: flex;

            &:nth-child(3n + 1) > label {
                width: 110px;
            }
            &:nth-child(3n + 2) > label {
                width: 100px;
            }
            &:nth-child(3n + 3) > label{
                width: 80px;
            }

            span {
                flex: 1;
            }
        }
    }
    .coupon-header-info {
        align-items: center;

        .bui-button {
            height: 100%;
        }
    }
}

.order-legend {
    .content {
        .item {
            label {
                width: 65px;
                display: inline-block;
            }

            span {
                max-width: 400px;
            }
        }
    }
}

.order-confirm-default {
    .order-legend {
        .content {
            padding: 16px 0 8px;
        }

        .legend-header {
            line-height: 24px;
            padding: 24px 0 16px;

            .title {
                height: 24px;
            }

            .charge-wrapper {
                height: 24px;
            }
        }
    }

    .postpay-tip {
        .bui-toastlabel-warning {
            color: #151b26;
            font-weight: 400;
            fill: #ff9326;
            background-color: #fff4e6;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            padding: 6px 16px;
            margin: 4px 0;
            position: relative;
            border-radius: 4px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;

            .toastlabel-icon {
                line-height: 16px;
                margin-right: 8px;
            }

            .bui-toastlabel-content {
                line-height: 20px;
            }
        }
    }
}

// 标签
.tag-edit-panel {
    .inline-form {
        .s-form-item {
            margin-top: 0;
            margin-bottom: 16px;
        }
    }

    .error-msg {
        display: contents;
    }

    .footer {
        align-items: center;
        height: 30px;
    }
}

// a标签 颜色处理
a {
    color: #2468f2;

    &:hover {
        color: #144bcc;
    }
}

a:visited {
    color: #2468f2;

    &:hover {
        color: #144bcc;
    }
}

// 状态样式的特殊处理
.status:before {
    margin-right: 6px;
}

.status.status-processing::before {
    color: #528eff;
    border-radius: 6px;
}

.status.status-processing::before {
    animation: flash 0.4s ease-in infinite alternate;
}

// 刷新按钮样式处理
.list-refresh-button {
    padding: 0 7px;
}

// 空表格
.ue4-table-empty {
    background-image: url('../static/img/empty2.svg');
    height: 100px;
    width: 100px;
    margin: auto;

    &-action {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #2468f2 !important;
        line-height: 20px;
        font-weight: 400;

        &:hover {
            cursor: pointer;
        }
        svg {
            margin-bottom: 2px;
        }
    }
}

// icon-button特殊处理
.s-icon-button {
    padding: 0 7px;
}

// 操作按钮样式
.operate-btn {
    padding: 0;
}

// 侧边栏特殊处理
.s-app-sidebar-item {
    a {
        color: currentColor;
    }
}

.s-steps {
    width: 848px;
    display: flex;
    justify-content: center;
    margin: 24px auto;

    .s-step-content {
        position: relative;
        z-index: 2;

        .s-step-line {
            position: absolute;
            top: 0;
            left: 60px;
            width: 240px;
            z-index: 2;
        }
    }
}

.shopping-cart {
    .prices-wrapper {
        .price-content {
            .price-item {
                .price {
                    font-size: 20px;
                    color: #f33d3d;
                    font-weight: 500;
                }
            }
        }
    }
}

// 表单相关
.s-form-item-error,
.s-form-item-extra,
.s-form-item-help {
    padding: 4px 0 0;
}

.form-item-switch-middle {
    .s-form-item-control-wrapper {
        .s-form-item-label {
            line-height: 20px !important;
        }
    }
}
.s-create-page-content {
    padding-bottom: 96px !important;
}
// 列表相关
.s-table {
    .s-checkbox-input {
        border: 1px solid #e8e9eb;

        &:hover {
            border: 1px solid #2468f2;
        }
    }

    .s-checkbox-group-input-indeterminate,
    .s-checkbox-input-indeterminate {
        border-color: #2468f2;
    }

    .s-checkbox-group-input:checked,
    .s-checkbox-input:checked {
        border-color: transparent;
    }
}

// 按钮相关
.s-button {
    .button-icon {
        height: 100%;
        display: -webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }
}

.flex {
    display: flex;
}

// span-disable
.span-disabled {
    color: #b8babf;
    display: inline-block;
    height: 100%;
}

.span-enabled {
    color: #151b26;
    display: inline-block;
    height: 100%;
}

#main {
    // 没有导航栏的情况下不需要减去导航栏的宽度
    div:nth-child(2).app-view {
        // max-width: ~"calc(100% - 160px)";
        width: 0;
    }
}

#main.ui-v5 {
}

body {
    overflow-x: hidden;
}

.bes-app-sidebar {
    .s-app-sidebar-title {
        color: #151b26;
        font-weight: 600;
    }
}

.s-app-sidebar {
    z-index: 3750001;
}

// 临时解决 table 版本不同造成的图标对齐问题
.s-table .s-table-thead tr th.s-table-hcell .s-table-filter .s-icon {
    vertical-align: middle;
}

.s-table .s-table-hcell .s-table-hcell-text .s-table-hcell-text-content {
    display: flex;
    align-items: center;
}

.s-menu-item {
    & > a {
        color: #151b26;

        :visited {
            color: #151b26;
        }
    }
}

.s-menu-item-disabled {
    & > a {
        color: #b8babf;

        :visited {
            color: #b8babf;
        }
    }
}

.palo-tab-page {
    padding: 0 !important;
    border-radius: 6px;

    > .s-tabs {
        border: 0 !important;

        > .s-tabnav {
            flex: 0 0 160px;
            width: 160px;
            margin: 0 !important;

            > .s-tabnav-scroll {
                width: 100%;
                position: relative;

                &::after {
                    position: absolute;
                    content: '';
                    top: 0;
                    right: 0;
                    height: 100%;
                    width: 1px;
                    background: @tabBorderColor;
                }

                > .s-tabnav-nav {
                    border: 0 !important;
                    padding-top: 15px;

                    .s-tabnav-nav-item {
                        margin-bottom: 0;
                        padding: 0 16px;
                        width: 160px;
                        height: 40px;
                        line-height: 40px;
                        text-align: left !important;
                        font-size: 14px;

                        &::after {
                            display: none;
                            width: 4px;
                        }
                    }

                    .s-tabnav-nav-selected {
                        background-color: @tabSelectBgColor !important;
                    }
                }
            }
        }

        > .s-tabpane-wrapper {
            width: 100%;
            height: 100%;

            .s-tab-page-panel {
                height: 100%;

                .s-tabpane {
                    height: 100%;
                    padding: 0;

                    .s-list-page {
                        padding: 0;
                    }
                }
            }
        }
    }
}

.s-legend > label {
    display: flex;
    align-items: center;
}

.s-radio-button-group {
    border-color: #d4d6d9;
}

.s-checkbox-group .s-radio-text,
.s-checkbox .s-radio-text {
    font-size: 12px;
    line-height: 12px;
}

// formItem中switch 样式
.s-form-item-control-content:has(>.s-switch) {
    display: flex;
    align-items: center;
}

// datepicker 样式
.s-daterangepicker-popup, .s-datepicker-popup{
    width: unset;

    .s-daterangepicker-popup-panel {
        width: unset;
    }

    .s-time {
        width: unset;
        padding: unset;

        .s-time-wrap {
            margin: unset;
        }
    }
}
.s-popover .s-popover-body .s-popover-content {
    min-width: unset;
}
.s-dropdown .s-popup-content-box .s-menu {
    margin: 0;
    padding: 0;
}

.s-table .s-table-cell-sel {
    width: 40px;
    padding-left: 12px;
}

.s-daterangepicker {
    .s-trigger-container {
        position: relative;
    }
}

.new-pwd-layer {
    &-title {
        font-size: 12px;
        color: #151B26;
        line-height: 20px;
        font-weight: 500;
    }
}

.err {
    color: #f33e3e;
}
.s-cascader-panel {
    .s-cascader-column {
        position: static!important;
    }
}
.refresh-button {
    width: 8px;
}
.order-confirm-default .order-legend .coupon-items table tr td:first-child {
    display: none;
}

// text类型的button不需要左右内边距
.no-padding {
    padding: 0!important;
}
.s-tree2 .s-checkbox {
    padding-top: 0!important;
}