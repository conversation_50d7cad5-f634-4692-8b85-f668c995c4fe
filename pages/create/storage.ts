import _ from 'lodash';
import {Component} from "san";
import {OrderConfirm, ShoppingCart} from '@baiducloud/billing-sdk/san';
import {BillingSDK, OrderItem} from '@baiducloud/billing-sdk';
import {html, decorators, redirect, ServiceFactory} from '@baiducloud/runtime';
import {
    AppCreatePage,
    AppLegend
} from '@baidu/sui-biz';
import {
    Button,
    Steps,
    Select,
    Badge,
    Table,
    Alert,
    Tooltip
} from '@baidu/sui';
import {ContextService} from '@common/index';
import {baseOrderItem,  PAYTYPE} from '@common/config';
import CreateCluster from './components/create-cluster';
import {getDiskNameFormType, getDiskTypeFormType} from './components/create-utils';
import './index.less';

const $flag = ServiceFactory.resolve('$flag');

const { asPage } = decorators;

const template = html`
<div class="palo-create">
    <app-create
        class="palo-create"
        backTo="{{backUrl}}"
        pageTitle="创建集群">
            <s-steps
                style="width: 400px;margin-top:20px; margin-bottom:20px"
                current="{{steps.current}}"
                on-change="stepChange">
                    <s-step
                        s-for="step in steps.datasource"
                        title="{{step.title}}"
                        description="{{step.description}}"/>
            </s-steps>
            <div style="display:{{steps.current === 1 ? 'block' : 'none'}}; width: 100%;">
                <create-cluster
                    san-ref="create-cluster"
                    pageType="{{2}}"
                />
            </div>
            <template s-if="steps.current === 2">
                <s-alert skin="info" style="width: 100%" class="mb16">
                        <div slot="description">
                            <p>温馨提示：1.按量付费服务将根据使用情况从账户余额中扣除，请保证账户有足够的余额；如果您有代金券，产生账单后会优先从代金券中扣除；如果有折扣，会在实际产生的账单中体现</p>
                            <p style="margin-left: 60px">2. 非付费产品和应用不会在订单中展示</p>
                        </div>
                </s-alert>
                <template s-if="$flag.PaloXS">
                    <order-confirm
                        items="{{items}}"
                        sdk="{{sdk}}"
                        merge-by="orderServiceType"
                        class="mt10"
                        class="billing-confirm"
                    />
                </template>
                <template s-else>
                    <order-confirm
                        class="billing-confirm"
                        items="{=items=}"
                        sdk="{{sdk}}"
                        merge-by="orderServiceType"
                        use-coupon="{{formData.productType === 'prepay'}}"
                        class="billing-confirm"
                        theme="default"
                        useCoupon="{{productType === PAYTYPE.PREPAY}}"
                    />
                </template>
            </template>
            <div slot="pageFooter">
                <div class="pager-footer">
                    <div class="foot-wrapper">
                        <s-button
                            s-if="steps.current === 2"
                            on-click="stepPre"
                            class="step-button"
                        >
                            上一步
                        </s-button>
                        <s-button
                            skin="primary"
                            s-if="steps.current === 1"
                            disabled="{{pricing}}"
                            loading="{{buttonLoading}}"
                            on-click="stepNext"
                            class="step-button"
                        >
                            确认订单
                        </s-button>
                        <s-button
                            s-if="steps.current === 2"
                            skin="primary"
                            disabled="{{confirming}}"
                            on-click="onConfirm"
                            class="step-button"
                        >
                            {{orderText}}
                        </s-button>
                        <div class="shopping-cart shopping-cart-item" style="flex: 1;">
                            <div class="prices-wrapper">
                                <div class="price-content">
                                    <div class="price-item price-item-num-0">
                                        <div><span class="grey-text">配置费用</span></div>
                                        <div>
                                            <span class="price" s-if="pricing">
                                                -
                                            </span>
                                            <span class="price" s-else>
                                                {{totalPrice}}
                                            </span>
                                            <span class="grey-text"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <s-tooltip class="com-warning-tip">
                            <a href="javascript:void(0)">明细</a>
                            <div class="create-page-popover" slot="content">
                                <h3 style="margin-bottom:10px;font-weight:bold">
                                    {{priceDetailTitle}}
                                </h3>
                                <s-table
                                    columns="{{columns}}"
                                    datasource="{{priceDetail}}"
                                >
                                    <span slot="c-deploy">{{row.deploy | raw}}</span>
                                    <span slot="c-price" class="price-detail-num">{{row.price}}</span>
                                </s-table>
                            </div>
                        </s-tooltip>
                    </div>
                </div>
            </div>
    </app-create>
</div>
`;
@asPage('/palo/decoupled-create')
export default class Create extends Component {
    static pageName = 'palo-create';
    static template = template;

    static components = {
        'app-create': AppCreatePage,
        's-button': Button,
        's-steps': Steps,
        's-step': Steps.Step,
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        's-select': Select,
        's-badge': Badge,
        's-table': Table,
        'create-cluster': CreateCluster,
        'app-legend': AppLegend,
        's-alert': Alert,
        's-tooltip': Tooltip
    };

    initData() {
        return {
            PAYTYPE,
            backUrl: '/palo/#/palo/decoupledlist',
            steps: {
                datasource: [{
                    title: '集群配置'
                }, {
                    title: '确认订单'
                }],
                current: 1
            },
            // 订单确认中
            confirming: false,
            // 正在询价（还没获取到价格），初始值true不让点击下一步
            pricing: true,
            productType: PAYTYPE.PREPAY,
            buttonLoading: false,
            columns: [
                {
                    name: 'item', label: '计费项', width: 100
                },
                {name: 'deploy', label: '配置', width: 200},
                {name: 'price', label: '参考价格', width: 100}
            ],
            priceDetail: [],
            total: 0,
        };
    }

    attached() {
        this.data.set('sdk',
            new BillingSDK({
                OrderType: 'NEW', 
                region: window.$context.getCurrentRegion().id,
                serviceType: 'palo'
            }, window.$context)
        );
    }
    static computed = {
        stepLength(): number {
            return this.data.get('steps.datasource').length;
        }
    }

    static messages = {
        'update-price': function (arg: any) {
            const formData = this.ref('create-cluster').data.get('formData');
            if (arg.value.productType) {
                this.data.set('productType', arg.value.productType);
            }
            if (arg.value) {
                this.initOrder({ ...formData, ...arg.value });
            } else {
                this.initOrder({ ...formData });
            }
        }
    }

    checkHasEmpty(priceData): boolean {
        let flag = false;
        if (!priceData || Object.keys(priceData).length === 0) {
            return true;
        }
        for (let key of Object.keys(priceData)) {
            if (!priceData[key]) {
                flag = true;
            }
            if (typeof priceData[key] === 'object') {
                flag = this.checkHasEmpty(priceData[key]) || flag;
            }
        }
        return flag;
    }

    async initOrder(formData: any) {
        this.data.set('pricing', true);
        const params = this.getPriceParamsNew(formData);
        if (this.checkHasEmpty(params)) {
            return;
        }
        try {
            const {total, paloCachePrice, paloCuPrice} = await this.$http.paloPost('paloOrderSeperationPrice',
                {
                    ...params,
                    isOriginalPrice: $flag.PaloXS
                }
            );
            const configDetail = this.getConfigDetail(formData);
            const storageDetail = this.getStorageDetail(formData);
            const region = ContextService.SERVICE_TYPE.PALO.region;
            const commonParam = {
                region: formData.region,
                type: 'NEW',
                duration: formData.time,
                productType: formData.productType,
                timeUnit: formData.productType === PAYTYPE.POSTPAY ? 'MINUTE' : 'MONTH',
            };
            const computeGroupItem = new OrderItem({
                ...baseOrderItem,
                serviceName: '计算组',
                ...commonParam,
                price: paloCuPrice,
                orderServiceType: 'palo_group',
                configDetail,

            });
            const storageItem = new OrderItem({
                ...baseOrderItem,
                serviceName: '缓存及存储',
                ...commonParam,
                price: paloCachePrice,
                orderServiceType: 'palo_storage',
                configDetail: storageDetail
            });
            this.data.set('items', [computeGroupItem, storageItem]);
            this.data.set('totalPrice', formData.productType === 'prepay' ? `${total}\/${formData.time}个月` : `${total}\/分钟`);
            const items = this.data.get('items');
            const priceDetail =  [
                {
                    item: '计算组',
                    deploy: `<p>付费方式：${PAYTYPE.getTextFromValue([formData.productType])}</p>
                             <p>计算资源：${formData.cuNums}CU</p>`,
                    price: formData.productType === 'prepay' ? `${items[0].price}/${formData.time}个月` : `${items[0].price}/分钟`
                },
                {
                    item: '缓存及存储',
                    deploy: `<p>缓存类型：${getDiskNameFormType(formData.diskSlotInfo.type)}</p>
                             <p>缓存容量：${formData.diskSlotInfo.size}GB</p>
                             <p>存储配置：按实际用量计费</p>`,
                    price: formData.productType === 'prepay' ? `${items[1].price}/${formData.time}个月` : `${items[1].price}/分钟`
                }
            ];
            this.data.set('priceDetail', priceDetail);
            this.data.set('priceDetailTitle', region[formData.region] + ' 可用区' + formData.availableZone.slice(4));
        }
        catch (e) {
            console.log(e);
        }
        this.data.set('pricing', false);
    }

    getConfigDetail(formData: any) {
        const configDetail = [
            {label: '可用区', value: '可用区' + formData.availableZone.slice(4)},
            {label: '计算资源', value: formData.cuNums + 'CU'}
        ];
        return configDetail;
    }
    
    getStorageDetail(formData: any) {
        const storageDetail = [
            {label: '可用区', value: '可用区' + formData.availableZone.slice(4)},
            {label: '缓存类型', value: getDiskNameFormType(formData.diskSlotInfo.type)},
            {label: '缓存容量', value: formData.diskSlotInfo.size + 'GB'},
            {label: '存储资源', value: '按实际用量计算'},
        ];
        return storageDetail;
    }

    // 获取订单价格接口参数
    getPriceParamsNew(formData: any) {
        const {cuNums} = this.ref('create-cluster').data.get('formData');;
        const modules: any = 
            [{
                name: "lakepalo",
                type: "cu",
                cuNum: cuNums,
                desiredVersion: formData.desiredVersion,
                diskSlotInfo: {
                    type: getDiskTypeFormType(formData.diskSlotInfo.type),
                    size: formData.diskSlotInfo.size
                }
            }];
        return {
            region: ContextService.getCurrentRegion().id,
            productType: formData.productType,
            time: formData.time,
            modules
        };
    }

    getCreateCheckParams() {
        const formData = this.ref('create-cluster')?.data.get('formData');
        const securityGroupName = this.ref('create-cluster')?.ref('basic-setting').getSecurityGroupNameById(formData.securityGroupId);
        const {renewalChecked, productType} = formData;
        const autoRenewInfo = renewalChecked && productType === 'prepay' ? {
            renewTimeUnit: formData.autoRenewInfo.renewTimeUnit,
            renewTime: formData.autoRenewInfo.renewTime
        } : null;
        
        return {
            name: formData.clusterName,
            password: formData.clusterPassword,
            vpcId: formData.vpcId,
            securityGroupId: formData.securityGroupId,
            securityGroupName,
            region: ContextService.getCurrentRegion().id,
            subnetUuid: formData.subnetId,
            productType: formData.productType,
            availableZone: formData.availableZone,
            time: formData.time,
            autoRenewInfo,
            computeGroupInfos: [{
                name: "CU",
                type: "lakepalo",
                cuNums: formData.cuNums,
                desiredVersion: formData.desiredVersion,
                diskSlotInfo: {
                    type: getDiskTypeFormType(formData.diskSlotInfo.type),
                    size: formData.diskSlotInfo.size
                }
            }]
        };
    }

    async stepNext() {
        try {
            await this.ref('create-cluster').validateForm();
            // 校验之前后端校验接口数据
            const parameter = this.getCreateCheckParams();
            try {
                this.data.set('buttonLoading', true);
                await this.$http.paloPost('paloSeperationCreateCheckV2', parameter);
            } catch (e) {
                console.log(e);
            } finally {
                this.data.set('buttonLoading', false);
            }
            this.data.set('steps.current', this.data.get('steps.current') + 1);
            this.data.set('orderText', parameter.productType ==='prepay' ? '去支付' : '提交订单');
        } catch (e) {
            this.ref('create-cluster').handleValidateError(e);
        }
    }

    stepPre() {
        this.data.set('steps.current', this.data.get('steps.current') - 1)
    }

    stepChange() {
        console.log('step', this.data.get('steps'));
    }

    // 切换当前地域
    onRegionChange() {
        this.ref('create-cluster').regionChange();
    }

    /**
     * 获取优惠券列表，没有就返回[]
     * @returns [couponId]
     */
    getCoupons() {
        const items = this.data.get('items') || [];
        const couponId = items[0]?.couponId ? items[0]?.couponId : '';
        // 不选择优惠券
        if (!couponId) {
            return [];
        }
        return [{
            type: 'coupon',
            values: [couponId]
        }];
    }

    /**
     * 提交订单
     */
    async onConfirm() {
        this.data.set('confirming', true);
        const parameter = this.getCreateCheckParams();
        const param = {
            items: [
                {
                    config: parameter,
                    paymentMethod: this.getCoupons()
                },
            ],
            paymentMethod: [],
        };
        
        try {
            const data = await this.$http.paloPost('paloSeperateConfirm', param);
            const sdk = this.data.get('sdk');

            try {
                const sdkResult = await sdk.checkPayInfo(data);
                sdkResult.url && redirect(sdkResult.url);
            } catch ({ url = null }) {
                url && redirect(url);
            }

        } catch (e: any) {
        } finally {
            this.data.set('confirming', false);
        }
    };
}