#bce-content:has(.palo-create) {
    overflow-x: auto;
}

.palo-create {
    color: #151b26;

    .create-row {
        display: flex;
    }
    
    .warning {
        margin-left: 5px;
        color: #f38900;
    }

    .s-create-page-content {
        // height: 100%;
        background-color: #f7f7f9;
        padding: 0 16px 64px;

        .s-step-content-title, .s-step-content-icon {
            background-color: #f7f7f9;
        }
        .billing-sdk-order-confirm-wrapper {
            width: 100%;
            padding: 0 16px;
            .billing-sdk-order-legend {
                width: 100%;
                background-color: #fff;
                .legend-header {
                    background-color: #fff;
                }
            }
            // 隐藏订单右上角信息
            .charge-wrapper {
                display: none;
            }
        }
        .s-legend {
            background-color: #fff;
        }
    }

    .s-create-page-footer {
        width: 100%;
        height: 80px;
        left: 50%;
        transform: translate(-50%);
    }
    .page-footer-wrapper {
        padding: 10px 16px 10px 0;
        width: 100%;
    }

    .page-title-nav {
        position: static;
        font-size: 14px;
    }

    .wrapper {
        display: flex;
        .shopping-cart {
            flex: initial;
            .shopping-cart-detail-container {
                flex: initial;
                padding-left: 10px;
            }
            .shopping-cart-item-wrapper {
                padding: 0 10px;
            }
        }
    }

    .step-button {
        height: 40px;
        box-sizing: border-box;
        border-radius: 4px;

        &:not(:first-child) {
            margin-left: 16px;
        }
    }

    .right-buttons{
        width: 100%;
        display: flex;
        justify-content: flex-end;

        button + button {
            margin-left: 15px;
        }
    }

    .order-item-container {
        .content {
            display: block;
        }

        .item {
            display: block;
            width: 100%;
        }
    }

    .renew-item {
        .s-form-item-control-content {
            display: flex;
            align-items: center;
            .s-radio-text {
                display: inline-block;
                width: 50px;
            }
        }
    }
}
.create-node-config {
    .node-card {
        display: flex;

        .active {
            border: 1px solid #2468F2;
            background: #EEF3FE;
        }

        .s-card {
            margin-right: 16px;

            &:hover {
                border-color: #2468F2;
            }

            .card-title {
                font-size: 14px;
                color: #151B26;
                line-height: 22px;
                font-weight: 500;
                margin-bottom: 4px;
            }

            .info-item {
                display: flex;
                font-size: 12px;
                color: #5C5F66;
                line-height: 20px;
                margin-top: 8px;
                justify-content: space-between;

                .value {
                    color: #151b26;
                }
            }
        }

        .ds_il {
            width: 235px;
            cursor: pointer;
            position: relative;
        }

        .tip {
            width: 50px;
            height: 20px;
            display: inline-block;
            line-height: 20px;
            position: absolute;
            text-align: center;
            vertical-align: middle;
            text-indent: 4px;
            top: 0;
            right: 0;
            background-color: #2468F2;
            color: white;
            font-size: 12px;
            border-bottom-left-radius: 12px;
            clip-path: polygon(100% 0, 100% 100%, 10% 100%, 0 0);
        }
    }

    .node-config-table {
        width: 600px;
        margin-top: 15px;

        .s-table-row-disabled {
            .s-table-cell-text {
                color: #84868c;
            }

            .soldout-tag {
                margin: 0 8px;

                .s-tag {
                    background: #b8babf;
                    color: #fff;
                }
            }
            
        }
    }

    .s-form-item-control-wrapper {
        flex:1;
    }

    .cacheup-form-item {
        .s-form-item-control-content {
            display: block;
            .cacheup__panel {
                width: 100%;
                margin: 24px 0 0;
                padding-right: 20px;
                background-color: var(--bgColor);
                border-radius: 4px;
                padding: 10px;
            }
        }
    }

    .node-row {
        .tip {
            margin-top: 8px;
        }
    }

    .mr5 {
        margin-right: 5px;
    }

    .ml10 {
        margin-left: 10px;
    }

    .tip-gray {
        color: #999;
        padding-top: 8px;
    }

    .tip-warn1 {
        color: red;
        padding-top: 10px;
    }

    .tip-warn2 {
        color:#F47108;
        padding-top: 10px;
    }

    .tip-warn3 {
        color: #FFA54F;
        padding-top: 10px;
    }
}

.create-cluster {
    .s-legend {
        margin-top: 20px;
        padding: 15px 20px 0;
        box-sizing: border-box;
        border: none;
    }

    .s-form-item-label > label {
        padding-left: 0;
    }
}

.create-purchase-info {
    .time-radio {
        .s-radio-text{
            position: relative;
            min-width: 48px;
        }
    }

    .sale-tip-tag {
        position: absolute;
        height: 16px;
        line-height: 16px;
        top: -8px;
        right: -1px;
        border-radius: 2px;
        color: #fff;
        font-size: 12px;
        padding: 0 4px;
        background-image: linear-gradient(90deg, #F3413F 22%, #F86454 100%);
    }
    .time-tip{
        position: absolute;
        bottom: 50%;
        // left: 0;
        transform: translateX(50%);
    }

    .mt15 {
        margin-top: 15px;
    }

    .purchase-center {
        display: flex;
        align-items: center;
    }

    .purchase-time {
        display: flex;
        align-items: center;
    }

}

.create-available {
    .tip-gray {
        color: #999;
        padding-top: 8px;
    }

    .s-radio-button-group {
        .s-radio-text {
            width: 96px;
            padding: 0;
        }
    }
}

.create-basic {
    .tip-gray {
        color: #999;
        padding-top: 8px;
    }
}

.slot-type-row {
    display: flex;
    align-items: center;
    .s-tag {
        margin: 0!important;
    }
}

.palo-create {

    .ue4-card {
        min-width: fit-content;
        // margin: 0 16px 16px;
        margin: 0 0;
        padding: 24px;
        // border-radius: 6px;
        background-color: #fff;
    
        .title {
            margin-bottom: 16px;
            label {
                font-weight: 500;
                font-size: 16px;
            }
        }
    
        .s-form-item {
            margin-top: 24px;
            margin-bottom: 0;

            .s-form-item-label {
                width: 110px;
                text-align: left;

                .slot-label {
                    vertical-align: middle;
                }
            }

            .s-form-item-error,
            .s-form-item-extra,
            .s-form-item-help {
                padding: 4px 0 0;
            }
        }
    }

    .tag-manage {

        .footer {
            a {
                display: none;
            }
            margin: 0 0 16px;

            .tag-button-add {
                padding-left: 0;
            }
        }
    
        .desc {
            margin-top: 10px;
            color: #999;
        }

        .s-form-item{
            margin-top: 0;
        }
        // .s-form-item-label{
        //     margin-top: 24px;
        // }
    }

    .billing-confirm .order-legend .item:nth-child(3n+3)>label {
        width: 115px;
    }
    
    .billing-confirm .order-legend .item:nth-child(3n+2)>label {
        width: 70px;
    }
    .payment-card {
        &-list {
            display: flex;
        }
        &-item {
            position: relative;
            display: inline-flex;
            align-items: center;
            width: 260px;
            padding: 13px 0 13px 20px;
            border: 1px solid rgba(232, 233, 235, 1);
            border-radius: 6px;
            cursor: pointer;
            margin-right: 8px;

            &:hover {
                border: 1px solid rgba(36, 104, 242, 1);
            }

            &.active {
                background: #eef3fe;
                border: 1px solid rgba(36, 104, 242, 1);

                .payment-card-item__info-title {
                    color: #2468f2;
                }
            }

            &__icon {
                display: inline-block;
                width: 32px;
                height: 32px;
                margin-right: 20px;

                &.prepaid {
                    background-image: url(~@static/prepaid.png);
                }
                &.postpaid {
                    background-image: url(~@static/postpaid.png);
                }
            }

            &__info {
                &-title {
                    font-size: 14px;
                    line-height: 22px;
                    font-weight: 500;
                }

                &-desc {
                    font-size: 12px;
                    color: var(--text-sub-color);
                    line-height: 20px;
                    margin-top: 4px;
                }
            }

            &__tag {
                position: absolute;
                right: 0;
                top: 0;
                background-image: linear-gradient(90deg, #f3413f 22%, #f86454 100%);
                border-top-right-radius: 4px;
                line-height: 20px;
                padding: 0 4px;
                color: #fff;
                border-bottom-left-radius: 12px;
                clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0 0);
                text-align: center;
                text-indent: 4px;
            }
        }
    }
}

.s-create-page-footer{
    justify-content: flex-start;

}

.pager-footer {
    .foot-wrapper {
        display: flex;
        align-items: center;
        box-sizing: border-box;

        .shopping-cart-item {
            // flex: 1;

            .price-content {
                overflow: visible;
                flex-wrap: wrap;

                .price-item {
                    display: flex;
                    flex-direction: column;
                    padding-left: 40px;
                    padding-right: 0;

                    .grey-text {
                        color: #5c5f66;
                    }

                    div {
                        display: inline-block;
                        position: relative;
                        height: 20px;
                        line-height: 20px;

                        .price {
                            font-size: 20px;
                            color: #f33d3d;
                            font-weight: 500;
                        }
                    }

                    div:nth-child(2) {
                        height: max-content;
                        line-height: 28px;

                    }

                    &.price-item-num-0 {
                        div:nth-child(2) {
                            span.grey-text {
                                font-size: 0px;
                                color: #333;
                                text-decoration: line-through;
                                top: 0;
                                height: 0;
                                display: none;
                            }
                        }
                    }
                }

                .seperate-price-item {
                    display: flex;
                    align-items: center;
                    padding-left: 40px;
                    padding-right: 0;

                    .grey-text {
                        color: #5c5f66;
                    }
                    .price {
                        font-size: 20px;
                        color: #f33d3d;
                        font-weight: 500;
                    }
                    .split {
                        margin: 0 16px;
                        border-left: 1px solid #5c5f66;
                        height: 30px;
                    }
                    .timeUnit {
                        font-size: 20px;
                        color: #5c5f66;
                    }
                }
            }
            .detail-wrapper {
                display: none;
            }
        }

        .right-buttons {
            -webkit-box-flex: 1;
            flex-grow: 1;
            text-align: right;
            .purchaseWarn {
                line-height: 40px;
                margin: 0 10px;
                color: #f4b329;
            }
        }
        .detail-wrapper {
            display: none;
        }
    }
    .shopping-cart .price-content {
        flex-direction: column;
        min-width: 150px;
        .price-item div {
            display: inline;
        }
        .grey-text {
            vertical-align: text-bottom;
            margin-right: 10px;
        }
    }
    .com-warning-tip {
        margin-left: 16px;
        margin-top: 25px;
    }
}
.create-page-popover {
    width: 500px;
    .price-detail-num {
        font-size: 12px;
        color: #F33E3E;
        line-height: 20px;
        font-weight: 500;
    }
}
.cuNum-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .sellout {
        background: #B8BABF;
        border-radius: 2px;
        display: inline-block;
        height: 20px;
        padding-left: 4px;
        padding-right: 4px;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 20px;
        font-weight: 400;
    }
}