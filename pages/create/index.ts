/**
 *
 * @file 创建集群
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from "san";
import {OrderConfirm, ShoppingCart} from '@baiducloud/billing-sdk/san';
import {BillingSDK, OrderItem} from '@baiducloud/billing-sdk';
import {html, decorators, redirect, ServiceFactory} from '@baiducloud/runtime';
import {
    AppCreatePage,
    AppLegend
} from '@baidu/sui-biz';
import {
    Button,
    Steps,
    Select,
    Badge,
    Table
} from '@baidu/sui';
import {ContextService} from '@common/index';
import {NODE_TYPE_ENUM, baseOrderItem,  PAYTYPE} from '@common/config';
import {getCPUNum, getStoreNum, getDiskNameFormType, NODE_TYPE_LEADER, NODE_TYPE_COMPUTE} from './components/create-utils';
import CreateCluster from './components/create-cluster';
import './index.less';
import {compareVersions} from '@common/utils';
const $flag = ServiceFactory.resolve('$flag');
const nodeTypeList = NODE_TYPE_ENUM.toArray().filter((item: any) => item.value !== 'palostudio');

const {asPage} = decorators;

const template = html`
<div class="palo-create">
    <app-create
        class="palo-create"
        backTo="{{backUrl}}"
        pageTitle="创建集群">
            <s-steps
                style="width: 400px;margin-top:20px; margin-bottom:20px"
                current="{{steps.current}}"
                on-change="stepChange">
                    <s-step
                        s-for="step in steps.datasource"
                        title="{{step.title}}"
                        description="{{step.description}}"
                    />
            </s-steps>
        <div style="display:{{steps.current === 1 ? 'block' : 'none'}}; width: 100%;">
            <create-cluster
                san-ref="create-cluster"
                pageType="{{1}}"
            />
        </div>
        <template s-if="steps.current === 2">
            <order-confirm
                s-if="$flag.PaloXS"
                items="{{items}}"
                sdk="{{sdk}}"
                merge-by="orderServiceType"
                class="mt10"
                class="billing-confirm"
            />
            <order-confirm
                s-else
                class="billing-confirm"
                items="{=items=}"
                sdk="{{sdk}}"
                merge-by="orderServiceType"
                use-coupon="{{formData.productType === 'prepay'}}"
                class="billing-confirm"
                theme="default"
                useCoupon="{{productType === PAYTYPE.PREPAY}}"
            />
        </template>
        <div slot="pageFooter">
            <div class="pager-footer">
                <div class="foot-wrapper">
                    <s-button
                        s-if="steps.current === 2"
                        on-click="stepPre"
                        class="step-button"
                    >
                        上一步
                    </s-button>
                    <s-button
                        skin="primary"
                        s-if="steps.current === 1"
                        disabled="{{pricing}}"
                        loading="{{buttonLoading}}"
                        on-click="stepNext"
                        class="step-button"
                    >
                        下一步
                    </s-button>
                    <s-button
                        s-if="steps.current === 2"
                        skin="primary"
                        disabled="{{confirming}}"
                        on-click="onConfirm"
                        class="step-button"
                    >
                        提交订单
                    </s-button>
                    <div class="shopping-cart shopping-cart-item" style="flex: 1;">
                        <div class="prices-wrapper">
                            <div class="price-content">
                                <div class="price-item price-item-num-0">
                                    <div><span class="grey-text">配置费用</span></div>
                                    <div>
                                            <span class="price" s-if="pricing">
                                                -
                                            </span>
                                            <span class="price" s-else>
                                                {{items[0].price}}
                                            </span>
                                            <span class="grey-text"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </app-create>
</div>
`;
@asPage('/palo/create')
export default class Create extends Component {
    static pageName = 'palo-create';
    static template = template;

    static components = {
        'app-create': AppCreatePage,
        's-button': Button,
        's-steps': Steps,
        's-step': Steps.Step,
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        's-select': Select,
        's-badge': Badge,
        's-table': Table,
        'create-cluster': CreateCluster,
        'app-legend': AppLegend
    };

    initData() {
        return {
            PAYTYPE,
            backUrl: '/palo/#/palo/list',
            steps: {
                datasource: [{
                    title: '集群配置'
                }, {
                    title: '确认订单'
                }],
                current: 1
            },
            detailColumns: [
                {
                    name: 'nodeName',
                    label: '节点类型',
                    width: '25%'
                },
                {
                    name: 'numValue',
                    label: '节点数量',
                    width: '20%'
                },
                {
                    name: 'detailInfo',
                    label: '节点配置详情',
                    width: '55%',
                    minWidth: 120,
                }
            ],
            // 订单确认中
            confirming: false,
            // 正在询价（还没获取到价格），初始值true不让点击下一步
            pricing: true,
            productType: PAYTYPE.PREPAY,
            buttonLoading: false
        };
    }

    attached() {
        this.data.set('sdk',
            new BillingSDK({
                OrderType: 'NEW', 
                region: window.$context.getCurrentRegion().id,
                serviceType: 'palo'
            }, window.$context)
        );
    }
    static computed = {
        stepLength(): number {
            return this.data.get('steps.datasource').length;
        }
    }

    static messages = {
        'init-order': function (arg: any) {
            // this.initOrder(arg.value);
        },
        'update-price': function (arg: any) {
            const formData = this.ref('create-cluster').data.get('formData');
            if (arg.value.productType) {
                this.data.set('productType', arg.value.productType);
            }
            if (arg.value) {
                this.initOrder({ ...formData, ...arg.value });
            } else {
                this.initOrder({ ...formData });
            }
        }
    }

    static filters = {
        setDetailDataSource: function (items: any) {
            return items[0]?.options?.cartDetail || [];
        }
    }

    checkParamsNew(params: any) {
        return params && params.modules && params.modules.every((item: any) => item.module_version != null)
    }

    async initOrder(formData: any) {
        this.data.set('pricing', true);
        const sdk = this.data.get('sdk');
        const params = this.getPriceParamsNew(formData);
        if (!this.checkParamsNew(params)) {
            sdk?.clearItems();
            sdk?.addItems([]);
            return;
        }
        const {total: price} = await this.$http.paloPost('paloOrderGetNewPackagePrice',
            {
                ...params,
                isOriginalPrice: $flag.PaloXS
            }
        );
        const configDetail = this.getConfigDetail(formData);
        const cartDetail = this.getCartDetail(formData);
        const orderItem = new OrderItem({
            ...baseOrderItem,
            region: formData.region,
            type: 'NEW',
            productType: formData.productType,
            timeUnit: formData.productType === PAYTYPE.POSTPAY ? 'MINUTE' : 'MONTH',
            price,
            configDetail,
            cartDetail
        });
        this.data.set('items', [orderItem]);
        this.data.set('pricing', false);
    }

    getConfigDetail(formData: any) {
        const configDetail = [
            { label: '可用区', value: '可用区' + formData.availableZone.slice(4) },
            // { label: '内核版本', value: formData.desiredVersion },
        ];
        nodeTypeList.forEach((item: any) => {
            const nodeData = formData[item.value];
            const slotType = nodeData.selectRow?.slotType || '';
            const CPU = getCPUNum(nodeData.selectRow);
            const storage = getStoreNum(nodeData.selectRow);
            const diskName = getDiskNameFormType(nodeData.diskType);
            const { numValue, diskSize } = nodeData;
            let detailInfo = `机型：${slotType}；CPU：${CPU}核；内存：${storage}GB`;
            if (diskName) {
                detailInfo += `；数据盘：${diskName} ${diskSize}GB`;
            }
            configDetail.push(
                {
                    label: item.name,
                    value: detailInfo
                },
                {
                    label: '数量',
                    value: numValue
                },
            )
        });

        return configDetail;
    }

    /**
     * 购物车查看详情的table数据
     * @param formData 
     * @returns 
     */
    getCartDetail(formData: any) {
        const cartDetail: any = [];
        nodeTypeList.forEach((item: any) => {
            const nodeData = formData[item.value];
            const CPU = getCPUNum(nodeData.selectRow);
            const storage = getStoreNum(nodeData.selectRow);
            const diskName = getDiskNameFormType(nodeData.diskType);
            const { numValue, diskSize } = nodeData;
            let detailInfo = `CPU ${CPU}核，内存 ${storage}GB`;
            if (diskName) {
                detailInfo += `，${diskName} ${diskSize}GB`;
            }
            cartDetail.push(
                {
                    numValue,
                    nodeName: item.name,
                    detailInfo
                }
            )
        });
        return cartDetail;
    }

    // 获取订单价格接口参数
    getPriceParamsNew(formData: any) {
        const modules: any = [];
        nodeTypeList.forEach((item: any) => {
            const module: any = {
                instance_num: formData[item.value]?.numValue,
                module_type: item.value,
                module_version: formData[item.value]?.selectRow?.version,
                slot_type: formData[item.value]?.selectRow?.slotType,
                product_name: item.value
            }
            if (formData[item.value].diskType) {
                module.diskSlotInfo = {
                    type: formData[item.value].diskType,
                    size: formData[item.value].diskSize,
                    instanceNum: formData[item.value]?.numValue
                };
            }
            modules.push(module)
        });
        return {
            region: ContextService.getCurrentRegion().id,
            productType: formData.productType,
            time: formData.time,
            modules,
            isOldPackage: false
        };
    }

    getCreateCheckParams() {
        const formData = this.ref('create-cluster')?.data.get('formData');
        const securityGroupName = this.ref('create-cluster')?.ref('basic-setting').getSecurityGroupNameById(formData.securityGroupId);
        const { renewalChecked, productType } = formData;
        const autoRenewInfo = renewalChecked && productType === 'prepay' ? {
            renewTimeUnit: formData.autoRenewInfo.renewTimeUnit,
            renewTime: formData.autoRenewInfo.renewTime
        } : null;
        
        let temp = {
            modules: [
                {
                    type: NODE_TYPE_LEADER,
                    desireInstanceNum: formData[NODE_TYPE_LEADER].numValue,
                    packageVersion: formData[NODE_TYPE_LEADER].selectRow.slotType,
                    desiredVersion: formData.desiredVersion,
                    diskSlotInfo: {
                        type: formData[NODE_TYPE_LEADER].diskType,
                        size: formData[NODE_TYPE_LEADER].diskSize
                    }
                },
                {
                    type: NODE_TYPE_COMPUTE,
                    desireInstanceNum: formData[NODE_TYPE_COMPUTE].numValue,
                    packageVersion: formData[NODE_TYPE_COMPUTE].selectRow.slotType,
                    desiredVersion: formData.desiredVersion,
                    diskSlotInfo: {
                        type: formData[NODE_TYPE_COMPUTE].diskType,
                        size: formData[NODE_TYPE_COMPUTE].diskSize
                    }
                },
            ],
            name: formData.clusterName,
            password: formData.clusterPassword,
            vpcId: formData.vpcId,
            securityGroupId: formData.securityGroupId,
            securityGroupName,
            region: ContextService.getCurrentRegion().id,
            subnetUuid: formData.subnetId,
            isOpenService: false,
            productType: formData.productType,
            availableZone: formData.availableZone,
            isOldPackage: false,
            time: formData.time,
            autoRenewInfo,
            highAvailability: formData.highAvailable,
        }
        if (compareVersions('2.0', formData.desiredVersion) <= 0) {
            temp = {
                ...temp,
                kernelConfList: formData.kernelConfList
            }
        }
        return temp;
    }

    async stepNext() {
        try {
            await this.ref('create-cluster').validateForm();
            // 校验之前后端校验接口数据
            const parameter = this.getCreateCheckParams();
            try {
                this.data.set('buttonLoading', true);
                await this.$http.paloPost('paloClusterCreateCheckV2', parameter);
                // await this.initOrder(this.ref('create-cluster').data.get('formData'));
            } catch (e) {
                console.log(e);
            } finally {
                this.data.set('buttonLoading', false);
            }
            this.data.set('steps.current', this.data.get('steps.current') + 1)
        } catch (e) {
            this.ref('create-cluster').handleValidateError(e);
        }
    }

    stepPre() {
        this.data.set('steps.current', this.data.get('steps.current') - 1)
    }

    stepChange() {
        console.log('step', this.data.get('steps'));
    }


    /**
     * 创建集群按钮条件性开发 TODO
     */
    enableSubmitBtn() {
        const vpcId = this.data.get('formData.vpcId');
        const subnetId = this.data.get('formData.subnetId');
        const slotType = this.data.get('formData.slotType');

        // 创建集群按钮按钮是否可点击
        // if (!!vpcId && !!subnetId && slotType) {
        //     this.get('buyBucket').enable();
        // } else {
        //     this.get('buyBucket').disable();
        // }
    }

    /**
     * 更新费用 TODO
     */
    async updatePrice() {
        // const params = this.data.get('formData');
        // params.region = ContextService.getCurrentRegion().id;

        // const priceData = await this.$http.paloPost('paloOrderGetNewPackagePrice', params);
        // // POSTPAY 的 price 才带时间单位
        // priceData.priceText = priceData.price;
        // if (params.productType === PAYTYPE.POSTPAY) {
        //     priceData.priceText += '/分钟';
        // }
    }

    // 切换当前地域
    onRegionChange() {
        this.ref('create-cluster').regionChange();
    }

    /**
     * 获取优惠券列表，没有就返回[]
     * @returns [couponId]
     */
    getCoupons() {
        // const sdk = this.data.get('sdk');
        const items = this.data.get('items') || [];
        // const couponId = sdk?.orderItems ? sdk?.orderItems[0]?.couponId : '';
        const couponId = items[0]?.couponId ? items[0]?.couponId : '';
        // 不选择优惠券
        if (!couponId) {
            return [];
        }
        return [{
            type: 'coupon',
            values: [couponId]
        }];
    }

    /**
     * 提交订单
     */
    async onConfirm() {
        this.data.set('confirming', true);

        const param = {
            items: [
                {
                    config: {...this.getCreateCheckParams()},
                    paymentMethod: this.getCoupons()
                },
            ],
            paymentMethod: [],
        };
        try {
            const data = await this.$http.paloPost('paloOrderBccConfirm', param);
            const sdk = this.data.get('sdk');

            try {
                const sdkResult = await sdk.checkPayInfo(data);
                sdkResult.url && redirect(sdkResult.url);
            } catch ({ url = null }) {
                url && redirect(url);
            }

        } catch (e: any) {
        } finally {
            this.data.set('confirming', false);
        }
    };
}