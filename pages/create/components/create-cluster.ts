/**
 * 创建集群第一步
*/

import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {
    AppLegend,
    AppRow,
    AppCell
} from '@baidu/sui-biz';
import {
    Form,
    Input,
    Icon,
    Select,
    Card,
} from '@baidu/sui';
import {
    nameValidator,
    required,
    passwordValidator
} from '@common/utils/rules';
import {ContextService} from '@common/index';
import {PAYTYPE} from '@common/config';
import {NODE_TYPE_LEADER, NODE_TYPE_COMPUTE} from './create-utils';
import AvailableZone from './available-zone';
import BasicSetting from './basic-setting';
import NodeConfig from './node-config';
import PurchaseInfo from './purchase-info';
import StorageConfig from './storage-config';

const tempalte = html`
    <div class="create-cluster">
        <s-form
            s-ref="form"
            data="{= formData =}"
            rules="{{rules}}"
            label-align="right"
            layout="horizontal"
        >
            <app-legend label="付费及地域" class="ue4-card" noHighlight>
                <available-zone formData="{= formData =}" pageType="{{pageType}}"/>
            </app-legend>
            <app-legend label="基础设置" class="ue4-card" noHighlight>
                <basic-setting s-ref="basic-setting" formData="{= formData =}" pageType="{{pageType}}" />
            </app-legend>
            <app-legend label="节点配置" class="ue4-card" noHighlight s-if="pageType === 1">
                <node-config s-ref="node-config" formData="{= formData =}"/>
            </app-legend>
            <app-legend label="资源配置" class="ue4-card" noHighlight s-else>
                <storage-config s-ref="storage-config" formData="{= formData =}"/>
            </app-legend>
            <app-legend
                label="购买信息"
                class="ue4-card"
                style="display:{{formData.productType === PAYTYPE.PREPAY ? 'block' : 'none'}}" noHighlight>
                <purchase-info s-ref="purchase-info" formData="{= formData =}"/>
            </app-legend>
        </s-form>
    </div>`;

@decorators.asComponent('@create-cluster')

export default class CreateCluster extends Component {
    static template = tempalte;

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-icon': Icon,
        's-input': Input,
        's-select': Select,
        's-card': Card,
        'app-legend': AppLegend,
        'app-row': AppRow,
        'app-cell': AppCell,
        'available-zone': AvailableZone,
        'basic-setting': BasicSetting,
        'node-config': NodeConfig,
        'purchase-info': PurchaseInfo,
        'storage-config': StorageConfig
    }

    initData() {
        const sameValidator = (key: string, rule: any, value: any, callback: (arg0: string | undefined) => void) => {
            const newPassword = this.data.get(key);
            if (value !== newPassword) {
                return callback('两次输入的内容不一致');
            }
            callback();
        };
        return {
            PAYTYPE,
            formData: {
                knownIsNotHighAvailable: false,
                highAvailable: true,
                productType: PAYTYPE.PREPAY,
                region: ContextService.getCurrentRegion().id, // 所选区域
                availableZone: '', // 所选可用区
                clusterName: '',
                desiredVersion: '',
                clusterPassword: '',
                confirmPassword: '',
                vpcId: '',
                subnetId: '',
                securityGroupId: '',
                moduleVersion: '',
                // 节点type
                [NODE_TYPE_LEADER]: {
                    // 节点类型:通用型，计算型等
                    nodeType: '',
                    // 节点配置
                    selection: {
                        mode: 'single',
                        selectedIndex: [0],
                        disabledIndex: []
                    },
                    selectRow: {},
                    // 购买数量
                    numValue: 3,
                    diskType: 'premium_ssd',
                    diskSize: 200
                },
                [NODE_TYPE_COMPUTE]: {
                    nodeType: '',
                    selection: {
                        mode: 'single',
                        selectedIndex: [0],
                        disabledIndex: []
                    },
                    selectRow: {},
                    numValue: 3,
                    diskType: 'premium_ssd',
                    diskSize: 200
                },
                // 购买时长
                time: 1,
                renewalChecked: true,
                autoRenewInfo: {
                    renewTime: 1,
                    renewTimeUnit: 'month'
                },
                kernelConfList: [
                    {
                        configName: 'enable_file_cache',
                        expectedConfigValue: false,
                        kernelType: 'FE'
                    },
                    {
                        configName: 'enable_file_cache',
                        expectedConfigValue: false,
                        kernelType: 'BE'
                    },
                    {
                        configName: 'file_cache_path_total',
                        expectedConfigValue: 0,
                        kernelType: 'BE'
                    }
                ],
                diskSlotInfo: {
                    type: '',
                    size: 0
                },
                cuNums: null
            },
            rules: {
                knownIsNotHighAvailable: [{
                    validator: (rule: any, value: any, callback: any) => {
                        if(!this.data.get('formData.highAvailable') && !this.data.get('formData.knownIsNotHighAvailable')) {
                            return callback('请勾选此项')
                        }
                        callback();
                    }
                }],
                productType: [required],
                region: [required],
                availableZone: [required],
                clusterName: [required, {validator: nameValidator}],
                desiredVersion: [{required: true, message: '请选择版本'}],
                clusterPassword: [required, {validator: passwordValidator}],
                securityGroupId: [required],
                confirmPassword: [
                    required,
                    {
                        validator: (...theArgs: any) => {sameValidator('formData.clusterPassword', ...theArgs);}
                    }
                ],
                vpcId: [
                    {
                        required,
                        validator: (rule: any, value: any, callback: (arg0: string | undefined) => void) => {
                            const subnetId = this.data.get('formData.subnetId');
                            if (!subnetId) {
                                return callback('请选择子网');
                            }
                            callback();
                        }
                    }
                ],
                cuNums: [{required: true, message: '请选择计算资源'}]
            }
        };
    }

    inited() {}

    static messages = {
        'init-module-version': function () {
            if (this.data.get('pageType') === 1) {
                this.nextTick(() => {
                    this.ref('node-config')?.initModuleVersion();
                });
            }
        },
        'available-zone-change': function (arg: any) {
            const {availableZone, isInit} = arg.value;
            this.data.set('formData.availableZone', availableZone);
            const {pageType} = this.data.get('');
            this.nextTick(() => {
                this.ref('basic-setting').updateSubnet();
            });
            if (!isInit && pageType === 1) {
                this.nextTick(() => {
                    this.ref('node-config').switchAvailableZone();
                })
            }
            else if (!isInit && pageType === 2) {
                this.nextTick(() => {
                    this.data.set('formData.cuNums', null);
                    this.dispatch('update-price', '');
                });
            }
        },
        'subnet-id-change': function (arg: any) {
            this.data.set('formData.subnetId', arg.value);
        },
        'security-group-id-change': function (arg: any) {
            this.data.set('formData.securityGroupId', arg.value);
        },
        'select-row-change': function (arg: any) {
            this.data.set(`formData.${arg.value.type}.selectRow`, arg.value.selectRow || {});
            this.dispatch('update-price', '');
        },
        'module-node-type-change': function (arg: any) {
            // 切换类型之后选择第一个,选择第一个的时候也会触发select-row-change从而触发价格更新，所以不用手动触发
            const {currentNode, selectedIndex, selectedRow, disabledIndex} = arg.value;
            this.data.set(`formData.${currentNode}.selection.disabledIndex`, disabledIndex);
            this.data.set(`formData.${currentNode}.selectRow`, selectedRow);
            this.data.set(`formData.${currentNode}.selection.selectedIndex`, [selectedIndex]);
            // this.dispatch('update-price', '');
        },
        // 磁盘数量，磁盘类型，数量的改变的事件
        'module-change': function (arg: any) {
            this.data.set(`formData.${arg.value.formDataAttr}`, arg.value.value);
            this.dispatch('update-price', '');
        },
        'vpc-id-change': function (arg: any) {
            this.data.set(`formData.vpcId`, arg.value);
        },
        'region-change': function (arg: any) {
            ContextService.setRegion(arg.value);
            this.nextTick(() => {
                this.regionChange();
            })
        },
        // 续费时长改变不用询价
        'time-unit-change': function (arg: any) {
            this.data.set('formData.autoRenewInfo.renewTimeUnit', arg.value);
            this.data.set('formData.autoRenewInfo.renewTime', 1);
            // this.dispatch('update-price', {autoRenewInfo: this.data.get('formData.autoRenewInfo')});
        },
        'renew-time-change': function (arg: any) {
            this.data.set('formData.autoRenewInfo.renewTime', arg.value);
            // this.dispatch('update-price', {autoRenewInfo: this.data.get('formData.autoRenewInfo')});
        },
        // 设置默认的模块数据，和版本无关的 
        // 'default-module-change': function (arg: any) {
        //     const defaultModules = arg.value;
        //     const nodeTypeList =  NODE_TYPE_ENUM.toArray();
        //     nodeTypeList.forEach((nodeItem: {value: any}) => {
        //         const {value: nodeType} = nodeItem;
        //         // const instance_num = defaultModules[nodeType].instance_range.default_num;
        //         // const ssd_num = defaultModules[nodeType].ssd_range.default_num;
        //         const premium_ssd_num = defaultModules[nodeType].premium_ssd_range?.default_num;
        //         premium_ssd_num && this.data.set(`formData.${nodeType}.diskSize`, premium_ssd_num);
        //     });
        // },
        // 集群版本改变的事件
        'module-version-change': function (arg: any) {
            this.data.set(`formData.palofe.selectRow`, arg.value.palofe.selectedRow || {});
            this.data.set(`formData.palobe.selectRow`, arg.value.palobe.selectedRow || {});
            this.data.set(`formData.palofe.selection.selectedIndex`, [arg.value.palofe.selectedIndex]);
            this.data.set(`formData.palobe.selection.selectedIndex`, [arg.value.palobe.selectedIndex]);
            this.data.set(`formData.palofe.selection.disabledIndex`, arg.value.palofe.disabledIndex);
            this.data.set(`formData.palobe.selection.disabledIndex`, arg.value.palobe.disabledIndex);
             // this.dispatch('init-order', this.data.get('formData'));
        },
        'module-type-change': function (arg: any) {
            const {palofe, palobe} = arg.value;
            this.data.set(`formData.palofe.nodeType`, palofe);
            this.data.set(`formData.palobe.nodeType`, palobe);
        },
        'cacheup-change': function (arg: any) {
            const {cache_endable, cache_size} = arg.value;
            this.data.set('formData.kernelConfList[0].expectedConfigValue', cache_endable);
            this.data.set('formData.kernelConfList[1].expectedConfigValue', cache_endable);
            this.data.set('formData.kernelConfList[2].expectedConfigValue', cache_size?.toString());
        },
        'storage-change': function (arg: any) {
            const {diskSlotInfo, cuNums} = arg.value;
            this.data.set('formData.cuNums', cuNums);
            this.data.set('formData.diskSlotInfo', diskSlotInfo);
            this.dispatch('update-price', arg.value);
        },
        'version-change': function (arg: any) {
            this.dispatch('update-price', arg.value);
        }
    }

    /**
     * 1.设置默认的节点类型，节点配置和选择的具体配置selectRow
     * 2.查询价格
     * @param nodeConfig
     */
    // setDefaultModule(nodeConfig: any, modulesDefaultConfig: any) {
    //     const nodeTypeList =  NODE_TYPE_ENUM.toArray().filter((item: any) => item.value !== 'palostudio');
    //     nodeTypeList.forEach((nodeItem: {value: any}) => {
    //         const {value: nodeType} = nodeItem;
    //         const defaultNodeType = modulesDefaultConfig[nodeType].type;
    //         const tableRows = nodeConfig[nodeType][defaultNodeType].modules;
    //         const selectIndex = tableRows.findIndex((row: any) => row.slotType === modulesDefaultConfig[nodeType].slot_type);
    //         this.data.set(`formData.${nodeType}.nodeType`, defaultNodeType);
    //         this.data.set(`formData.${nodeType}.selectRow`, tableRows[selectIndex]);
    //         this.data.set(`formData.${nodeType}.selection.selectedIndex`, [selectIndex]);
    //     });
    //     this.dispatch('init-order', this.data.get('formData'));
    // }

    regionChange() {
        const regionId = ContextService.getCurrentRegion().id;
        this.data.set('formData.region', regionId);
        // 地域改变
        window.location.reload();
    }

    validateForm() {
        return this.ref('form').validateFields();
    }

    /**
     * 处理表单校验的结果
     * 1. studio的密码和账号没写的话，就跳到那个tab
     * @param e 
     */
    handleValidateError(e: any) {
        const studioFormItem = ['palostudio.studioName', 'palostudio.studioPassword', 'palostudio.studioConfirmPassword'];
        const failItem = studioFormItem.find(item => e[item]);
        if (failItem) {
            this.ref('node-config').setCurrentNodeStudio();
        }
    }
}