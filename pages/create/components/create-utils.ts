import { NODE_TYPE_ENUM } from '@common/config';

export const NODE_TYPE_LEADER = NODE_TYPE_ENUM.getValueFromAlias('LEADER');

export const NODE_TYPE_COMPUTE = NODE_TYPE_ENUM.getValueFromAlias('COMPUTE');

export const NODE_TYPE_STUDIO = NODE_TYPE_ENUM.getValueFromAlias('STUDIO');

/**
 * 根据dataRow得到CPU数
 * @param dataRow : table的一行数据
 */
export const getCPUNum = function (dataRow: any) {
    const cpu_num = dataRow?.cpu_num || dataRow?.cpuNum;
    if (cpu_num) {
        return cpu_num;
    }
    let desArr = dataRow?.slotDescription?.split(' ');
    return desArr?.[1].slice(0, desArr[1].length - 2) || '';
}

/**
 * 根据dataRow得到核数
 * @param dataRow : table的一行数据
 */
export const getStoreNum = function (dataRow: any) {
    if (dataRow?.memory) {
        return dataRow.memory;
    }
    let desArr = dataRow?.slotDescription?.split(' ');
    return desArr?.[3].slice(0, desArr[3].length - 2) || '';
}

export const timeList = [
    { text: '1个月', value: 1 },
    { text: '2', value: 2 },
    { text: '3', value: 3 },
    { text: '4', value: 4 },
    { text: '5', value: 5 },
    { text: '6', value: 6 },
    { text: '7', value: 7 },
    { text: '8', value: 8 },
    { text: '9', value: 9 },
    { text: '1年', value: 12, class: 'sale-tip', tip: '注：购买1年8.3折' },
    // { text: '2年', value: 24, class: 'sale-tip', tip: '注：购买2年7折' },
    // { text: '3年', value: 36, class: 'sale-tip', tip: '注：购买3年5折' }
];

export const xsTimeList = [
    { text: '1个月', value: 1 },
    { text: '2', value: 2 },
    { text: '3', value: 3 },
    { text: '4', value: 4 },
    { text: '5', value: 5 },
    { text: '6', value: 6 },
    { text: '7', value: 7 },
    { text: '8', value: 8 },
    { text: '9', value: 9 },
    { text: '10', value: 10 },
    { text: '11', value: 11 },
    { text: '1年', value: 12 },
    { text: '2年', value: 24 },
    { text: '3年', value: 36 }
];

export const monthList = [
    { text: '1个月', value: 1 },
    { text: '2个月', value: 2 },
    { text: '3个月', value: 3 },
    { text: '4个月', value: 4 },
    { text: '5个月', value: 5 },
    { text: '6个月', value: 6 },
    { text: '7个月', value: 7 },
    { text: '8个月', value: 8 },
    { text: '9个月', value: 9 },
];

export const yearList = [
    { text: '1年', value: 1 },
    { text: '2年', value: 2 },
    { text: '3年', value: 3 }
];

export const diskDatasource = [
    {
        value: 'premium_ssd',
        text: 'SSD云磁盘',
        extra: 'premiumSsdCacheCapacity'
    },
    {
        value: 'ssd',
        text: '高性能云磁盘'
    },
];

export const diskDatasource4Decoupled = [
    {
        value: 'premium_ssd',
        text: 'SSD云磁盘',
        extra: 'premiumSsdCacheCapacity',
        diskType: 'Cache_Premium_SSD'
    },
    // {alias: 'enhanced_ssd_pl3', text: '增强型SSD_PL3', value: 'enhanced_ssd_pl3', extra: 'ssdPl3CacheCapacity', diskType: 'Cache_SSD_PL3'},
    // {alias: 'enhanced_ssd_pl2', text: '增强型SSD_PL2', value: 'enhanced_ssd_pl2', extra: 'ssdPl2CacheCapacity', diskType: 'Cache_SSD_PL2'},
    {alias: 'enhanced_ssd_pl1', text: '增强型SSD_PL1', value: 'enhanced_ssd_pl1', extra: 'ssdPl1CacheCapacity', diskType: 'Cache_SSD_PL1'},
];

export const getDiskNameFormType = function (typeValue: string) {
    return diskDatasource.find((item: { value: any; }) => item.value === typeValue)?.text || diskDatasource4Decoupled.find((item: { value: any; }) => item.value === typeValue)?.text;
}

export const getDiskTypeFormType = function (typeValue: string) {
    return diskDatasource4Decoupled.find((item: { value: any; }) => item.value === typeValue)?.diskType || '';
}