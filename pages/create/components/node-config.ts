/**
 * 节点设置
*/

import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {
    Form,
    Input,
    Icon,
    Select,
    Card,
    InputNumber,
    Radio,
    Badge,
    Table,
    Tag,
    Tooltip,
    Checkbox,
    Switch,
} from '@baidu/sui';
import {PasswordInput} from '@components/password-input';
import {leaderNumConfig, computeNumConfig} from '@common/config';
import {
    NODE_TYPE_LEADER,
    NODE_TYPE_COMPUTE,
    NODE_TYPE_STUDIO,
    getCPUNum,
    getStoreNum,
    diskDatasource,
    getDiskNameFormType
} from './create-utils';
import {ContextService} from '@common/index';
import {compareVersions} from '@common/utils'
import QuestionTip from './question-tip';
const $flag = ServiceFactory.resolve('$flag');

const tempalte = html`
    <div class="create-node-config">
        <s-form-item prop="highAvailable" label="高可用："  class="form-item-switch-middle">
            <s-switch checked="{{formData.highAvailable}}" on-change="onHighAvailableChange"></s-switch>
            <p slot="help" class="tip-gray" s-if="formData.highAvailable">启用高可用后，集群最少部署3个Leader Node+3个Compute Node，从而实现读写高可用（HA）。</p>
        </s-form-item>
        <s-form-item s-if="!formData.highAvailable" prop="knownIsNotHighAvailable">
            <s-checkbox checked="{{formData.knownIsNotHighAvailable}}" on-change="onKnownChange">
                <span style="color: red">
                    我已知晓不启用高可用后不承诺 SLA，生产环境推荐启用高可用（HA）。
                </span>
            </s-checkbox>
        </s-form-item>
        <s-form-item label="节点类型：">
            <div class="node-card">
                <div
                    s-for="node in nodeConfigList trackBy node.type"
                    on-click="nodeTypeClick(node)">
                        <s-card
                            class="card ds_il {{currentNode === node ? 'active' : ''}}">
                            <h3 class="card-title">{{formData[node.type].numValue | getNodeTitle(node.titleText)}}</h3>
                            <p s-if="node.infoText">{{node.infoText}}</p>
                            <p s-if="node.nodeTypeText" class="info-item">
                                <label>类型：</label>
                                <span class="value">{{formData[node.type].nodeType | getTypeTitle}}</span>
                            </p>
                            <p s-if="node.deployText" class="info-item">
                                <label>配置：</label>
                                <span class="value">{{formData[node.type] | getDeployTitle}}</span>
                            </p>
                            <p s-if="node.diskText" class="info-item">
                                <label>数据盘：</label>
                                <span class="value">{{formData[node.type] | getDiskTitle}}</span>
                            </p>
                            <span class="tip active">{{node.configStatusText}}</span>
                        </s-card>
                </div>
            </div>
        </s-form-item>
        <s-form-item label="节点配置：">
            <template
                s-for="node in nodeConfigList">
                <template s-if="currentNode === node">
                    <s-radio-group
                        radioType="button"
                        datasource="{{clusterNodeConfig[node.type] | getConfigDatasource(node.type)}}"
                        value="{=formData[node.type].nodeType=}"
                        on-change="onNodeConfigChange"
                    />
                    <s-table
                        san-ref="config-table"
                        class="node-config-table"
                        columns="{{tableColumns}}"
                        selection="{=formData[node.type].selection=}"
                        datasource="{{tableDatasource}}"
                        on-selected-change="onTableRowSelected"
                        loading="{{tableLoading}}"
                        >
                        <div class="slot-type-row" slot="c-slotType">
                            <div>
                                {{row.slotType}}
                            </div>
                            <s-tooltip content="售罄！请您移步其他可用区购买资源">
                                <s-tag skin="info" s-if="checkStock(rowIndex, node.type)" enhanced class="soldout-tag">售罄</s-tag>
                            </s-tooltip>
                        </div>
                    </s-table>
                </template>
            </template>
        </s-form-item>
        <s-form-item
            s-if="currentNode.chooseDisk"
            label="数据盘：">
            <template
                s-for="node in nodeConfigList">
                <div class="node-row" s-if="currentNode === node">
                    <s-select
                        class="mr5"
                        value="{=formData[node.type].diskType=}"
                        datasource="{{diskDatasource}}"
                        on-change="diskTypeChange"
                    />
                    <s-input-number
                        class="mr5"
                        min="{{node.diskMin}}"
                        step="100"
                        width="100"
                        value="{=formData[node.type].diskSize=}"
                        max="{{node.diskMax}}"
                        stepStrictly="{{true}}"
                        on-change="diskSizeChange"/> GB
                    <div className="tip">
                        <span class="tip-gray">容量：200-10000GB，峰值性能：随机IO 
                        <span>{{formData[node.type].diskType === 'premium_ssd' ? 25000 : 5000}}</span>
                         IOPS，吞吐量 
                         <span>{{formData[node.type].diskType === 'premium_ssd' ? 300 : 140}}</span>
                          MB/s</span>
                        <br>
                        <span class="tip-gray">SSD云磁盘/高性能云磁盘</span>

                        <a s-if="!$flag.PaloXS" href="https://cloud.baidu.com/doc/CDS/s/rjwvyachw" target="_blank">选择说明</a>
                    </div>
                </div>
            </template>
        </s-form-item>
        <s-form-item class="form-item-switch-middle cacheup-form-item" s-if="canCacheUp">
            <span slot="label">
                <span>开启缓存：</span>
                <question-tip placement="right" type="question">
                    开启缓存配置可以大幅度提升湖仓、冷热场景中的查询效率 <a href="https://cloud.baidu.com/doc/PALO/index.html" target="_blank">使用说明</a>
                </question-tip>
            </span>
            <s-switch
                checked="{{cache_endable}}"
                on-change="cacheupChange('cache_endable', $event.value)"
            />
            <div class="cacheup__panel" s-if="cache_endable">
                缓存容量:
                <s-input-number
                    class="mr5 ml16"
                    value="{{cache_size}}"
                    max="{{cache_size_max}}"
                    min="{{cache_size_min}}"
                    on-change="cacheupChange('cache_size', $event.value)"
                />
                GB
            </div>
        </s-form-item>
        <s-form-item label="节点数量：">
            <template
                s-for="node in nodeConfigList">
                <template s-if="currentNode === node">
                    <s-input-number
                        value="{=formData[node.type].numValue=}"
                        disabled="{{currentNode.instanceNum.disabled}}"
                        stepStrictly="{{currentNode.instanceNum.stepStrictly}}"
                        min="{{formData.highAvailable ? 3 : currentNode.instanceNum.min}}"
                        max="{{currentNode.instanceNum.max}}"
                        step="{{currentNode.instanceNum.step}}"
                        on-change="instanceNumChange"
                    />
                </template>
            </template>
            <div class="tip-warn1" s-if="computeNumEq1">特别提示：1个节点上运行有单点故障的风险，不建议生产环境使用。</div>
            <div class="tip-warn2" s-elif="computeNumEq2">特别提示：为保障服务高可用，建议在生产环境中，数据节点数量不小于3。</div>
            <div class="tip-warn3" s-elif="needNumTip">对于单一节点集群，节点故障后的数据迁移和服务重启阶段，服务可能无法访问。因此，为实现服务高可用，建议在生产环境中，两种节点数量均不小于3。</div>
        </s-form-item>
    </div>`;

@decorators.asComponent('@node-config')
export default class NodeConfig extends Component {
    static template = tempalte;

    static components = {
        's-form-item': Form.Item,
        's-icon': Icon,
        's-input': Input,
        's-select': Select,
        's-card': Card,
        's-input-number': InputNumber,
        's-radio-group': Radio.RadioGroup,
        's-badge': Badge,
        's-table': Table,
        'password-input': PasswordInput,
        's-tag': Tag,
        's-tooltip': Tooltip,
        's-checkbox': Checkbox,
        's-switch': Switch,
        'question-tip': QuestionTip
    }

    initData() {
        let nodeConfigList = [
            {
                // type节点名称,根据这个名称，来确定formData中的节点数据
                type: NODE_TYPE_LEADER,
                titleText: 'Leader Node',
                configStatusText: '已配置',
                nodeTypeText: '类型',
                deployText: '配置',
                diskText: '数据盘',
                chooseDisk: true,
                instanceNum: leaderNumConfig,
                diskMin: 200,
                diskMax: 10000
            },
            {
                type: NODE_TYPE_COMPUTE,
                titleText: 'Compute Node',
                configStatusText: '已配置',
                nodeTypeText: '类型',
                deployText: '配置',
                diskText: '数据盘',
                chooseDisk: true,
                instanceNum: computeNumConfig,
                diskMin: 200,
                diskMax: 10000
            },
        ];
        // 默认选中第一个LeaderNode
        let currentNode = nodeConfigList[0];
        return {
            NODE_TYPE_STUDIO,
            moduleVersionDatasource: [],
            modulesDefaultConfig: {},
            clusterNodeConfig: {},
            nodeConfigList,
            currentNode,
            tableColumns: [
                { name: 'slotType', label: '规格' },
                { name: 'num1', label: 'CPU（核）', render: this.renderCPUNum },
                { name: 'num2', label: '内存（GB）', render: this.renderStoreNum }
            ],
            diskDatasource,
            packageStockList: [],
            tableLoading: true,
            cache_endable: false,
            cache_size: 20
        };
    }

    static computed = {
        tableDatasource: function () {
            const clusterNodeConfig: any = this.data.get('clusterNodeConfig');
            const currentNode: any = this.data.get('currentNode');
            const nodeType: string = currentNode.type;
            const nodeConfigType: any = this.data.get(`formData.${currentNode.type}.nodeType`);
            return clusterNodeConfig?.[nodeType]?.[nodeConfigType]?.modules || [];
        },
        computeNumEq1: function () {
            const currentNode: any = this.data.get('currentNode');
            const num: any = this.data.get(`formData.${NODE_TYPE_COMPUTE}.numValue`);
            return currentNode.type === NODE_TYPE_COMPUTE && +num === 1;
        },
        computeNumEq2: function () {
            const currentNode: any = this.data.get('currentNode');
            const num: any = this.data.get(`formData.${NODE_TYPE_COMPUTE}.numValue`);
            return currentNode.type === NODE_TYPE_COMPUTE && +num === 2;
        },
        needNumTip: function () {
            const currentNode: any = this.data.get('currentNode');
            return currentNode.type === NODE_TYPE_LEADER || currentNode.type === NODE_TYPE_COMPUTE;
        },
        cache_size_min: function () {
            const currentNode = this.data.get('currentNode');
            const memory: number = currentNode.diskMin;
            return Math.max(Math.ceil(memory / 100), 10);
        },
        cache_size_max: function () : number {
            const formData = this.data.get('formData');
            return Math.floor(formData[NODE_TYPE_COMPUTE].diskSize * 0.6);
        },
        canCacheUp: function () : boolean {
            const desiredVersion = this.data.get('formData.desiredVersion');
            return compareVersions('2.0', desiredVersion) <= 0;
        }
    }

    static filters = {
        getNodeTitle: function (value: string | number, text: string) {
            return `${text}（${value}个）`;
        },
        getTypeTitle: function (value: string) {
            return `${value || ''}`;
        },
        getDeployTitle: function (value: any) {
            const selectData: any = value.selectRow;
            if (!selectData?.type) {
                return;
            }
            const cpu_num = getCPUNum(selectData);
            const memory = getStoreNum(selectData);
            let freeFlag = '';
            if (+cpu_num === 2 && memory === 4 && selectData.type === NODE_TYPE_STUDIO) {
                freeFlag = '(免费)';
            }
            return `${cpu_num}核${memory}G${freeFlag}`;
        },
        getDiskTitle: function (value: any) {
            const typeText = getDiskNameFormType(value.diskType);
            return `${value.diskSize}GB (${typeText})`;
        },

        getConfigDatasource: function (value: any, nodeType: string) {
            // 后台给的是无序的，调整成通用型在前面
            if (nodeType === NODE_TYPE_COMPUTE || nodeType === NODE_TYPE_LEADER) {
                return [{
                    text: '通用型',
                    value: '通用型'
                },
                {
                    text: '内存型',
                    value: '内存型'
                }];
            }

            if (!value) {
                return [];
            };
            let arr: any = [];
            Object.keys(value).forEach(item => {
                arr.push({
                    text: item,
                    value: item
                });
            });
            return arr;
        }
    }

    inited() {
        // this.initModuleVersion();
        const currentNode = this.data.get('currentNode');
        this.data.set('cache_size', Math.ceil(currentNode?.diskMin * 0.1));
    }

    cacheupChange(field: string, value: boolean | number) {
        this.data.set(`${field}`, value);
        const cache_endable = this.data.get('cache_endable');
        const cache_size = this.data.get('cache_size');
        this.dispatch('cacheup-change', {
            cache_endable,
            cache_size
        });
    }

    renderCPUNum(item: any, key: any, col: any, rowIndex: any, colIndex: any, data: any) {
        const cpu_num = getCPUNum(item);
        return cpu_num;
    }

    renderStoreNum(item: any, key: any, col: any, rowIndex: any, colIndex: any, data: any) {
        const memory = getStoreNum(item);
        return memory;
    }

    getPackageStock(moduleVersionType: any) {
        const currentZone = this.data.get('formData.availableZone');
        const packageStockList = this.data.get('packageStockList');
        // console.log('zone',currentZone, packageStockList);
        return  packageStockList.find((item: any) => item.packageVersion === moduleVersionType && item.logicalZone === currentZone); 
    }

    resolveSelectedAndDisabledIndex(currentNode: any, nodeType: any) {
        let selectedRow = {};
        let selectedIndex = -1;
        const disabledIndex: number[] = [];
        const clusterNodeConfig = this.data.get('clusterNodeConfig');
        // console.log('clusterNodeConfig', clusterNodeConfig)
        clusterNodeConfig[currentNode][nodeType].modules.forEach((item: any, index: number, arr: any[]) => {
            const itemStock = this.getPackageStock(item.slotType);
            if (!itemStock) {
                disabledIndex.push(index)
            }
            if((!itemStock || itemStock.inventoryQuantity > 0)) {
                if(selectedIndex === -1) {
                    selectedIndex = index;
                    selectedRow = arr[index];
                }
            }else {
                disabledIndex.push(index);
            }
        });
        return {
            selectedIndex,
            selectedRow,
            disabledIndex,
        }
    }

    /**
     * 初始化版本
     */
    async initModuleVersion() {
        // Boxer2升级
        const [res, {packageStockList}] = await Promise.all([this.$http.paloPost('paloListWithSlot'), this.$http.paloPost('paloPackageStock', {
            region: ContextService.getCurrentRegion().id,
            productName: 'palo',
        }).catch(() => {
            return ({packageStockList: []})
        })]);
        let clusterNodeConfig = res.moduleSlots || {};
        this.data.set('clusterNodeConfig', clusterNodeConfig);
        this.data.set('defaultConfig', res.config);
        this.data.set('packageStockList', packageStockList);
        const defaultModule = res.config.modules;
        const defaultPalofeType = defaultModule.palofe.type;
        const defaultPalobeType = defaultModule.palobe.type;
        this.dispatch('module-type-change', {
            palofe: defaultPalofeType,
            palobe: defaultPalobeType,
        });
        let defaultPalofeModuleVersionType = defaultModule.palofe.slot_type;
        let defaultPalobeModuleVersionType = defaultModule.palobe.slot_type;
        let defaultPalofeModuleVersionIndex = clusterNodeConfig.palofe[defaultPalofeType].modules.findIndex((item: any) => item.slotType === defaultPalofeModuleVersionType);
        let defaultPalobeModuleVersionIndex = clusterNodeConfig.palobe[defaultPalobeType].modules.findIndex((item: any) => item.slotType === defaultPalobeModuleVersionType);
        let defaultPalofeModuleVersion = clusterNodeConfig.palofe[defaultPalofeType].modules[defaultPalofeModuleVersionIndex];
        let defaultPalobeModuleVersion = clusterNodeConfig.palobe[defaultPalobeType].modules[defaultPalobeModuleVersionIndex];
        const defaultPalofeModuleVersionStock = this.getPackageStock(defaultPalofeModuleVersionType); 
        const defaultPalobeModuleVersionStock = this.getPackageStock(defaultPalobeModuleVersionType);
        let isDefaultPalofeModuleVersionAvailable = !defaultPalofeModuleVersionStock || defaultPalofeModuleVersionStock.inventoryQuantity > 0;
        let isDefaultPalobeModuleVersionAvailable = !defaultPalobeModuleVersionStock || defaultPalobeModuleVersionStock.inventoryQuantity > 0;

        const {selectedRow: palofeSelectedRow, selectedIndex: palofeSelectedIndex, disabledIndex: palofeDisabledIndex} = this.resolveSelectedAndDisabledIndex('palofe', defaultPalofeType);

        const {selectedRow: palobeSelectedRow, selectedIndex: palobeSelectedIndex, disabledIndex: palobeDisabledIndex} = this.resolveSelectedAndDisabledIndex('palobe', defaultPalobeType);

        if(!isDefaultPalofeModuleVersionAvailable) {
            defaultPalofeModuleVersionIndex = palofeSelectedIndex;
            defaultPalofeModuleVersion = palofeSelectedRow;
        }

        if(!isDefaultPalobeModuleVersionAvailable) {
            defaultPalobeModuleVersionIndex = palobeSelectedIndex;
            defaultPalobeModuleVersion = palobeSelectedRow;
        }

        // 集群版本改变
        this.dispatch('module-version-change', {
            palofe: {
                selectedIndex: defaultPalofeModuleVersionIndex,
                selectedRow: defaultPalofeModuleVersion,
                disabledIndex: palofeDisabledIndex,
            },
            palobe: {
                selectedIndex: defaultPalobeModuleVersionIndex,
                selectedRow: defaultPalobeModuleVersion,
                disabledIndex: palobeDisabledIndex,
            }
        });
        this.data.set('tableLoading', false);
    }

    /**
     * 切换节点类型
     * @param node 
     */
    nodeTypeClick(node: any) {
        this.data.set('currentNode', node);
    }

    /**
     * 将当前节点设置成studio
     */
    setCurrentNodeStudio() {
        const currentNode = this.data.get('nodeConfigList').find((item: any) => item.type === NODE_TYPE_STUDIO);
        this.nodeTypeClick(currentNode);
    }

    /**
     * 节点配置类型改变（通用型,计算型）
     * @param e
     */
    onNodeConfigChange(e: any) {
        const currentNode = this.data.get('currentNode.type');
        const nodeType = e.value;
        const {selectedIndex, selectedRow, disabledIndex} = this.resolveSelectedAndDisabledIndex(currentNode, nodeType);
        this.data.set(`formData.${currentNode}.selection.disabledIndex`, disabledIndex);
        this.dispatch('module-node-type-change', {
            currentNode,
            selectedIndex,
            selectedRow,
            disabledIndex,
        });
    }

    /**
     * 磁盘类型改变
     * @param e 
     */
    diskTypeChange(e: any) {
        const nodeType = this.data.get('currentNode.type');
        this.dispatch('module-change', {
            formDataAttr: `${nodeType}.diskType`,
            value: e.value
        });
    }

    /**
     * 磁盘数量改变
     * @param e 
     */
    diskSizeChange(e: any) {
        const nodeType = this.data.get('currentNode.type');
        this.dispatch('module-change', {
            formDataAttr: `${nodeType}.diskSize`,
            value: e.value
        });
    }

    /**
     * 购买数量变化
     * @param e
     */
    instanceNumChange(e: { value: number }) {
        const nodeType = this.data.get('currentNode.type');
        this.dispatch('module-change', {
            formDataAttr: `${nodeType}.numValue`,
            value: e.value
        });
    }

    /**
     * 集群版本改变
     */
    // onModuleVersionChange(e: any) {
    //     const clusterNodeConfig = this.data.get('clusterNodeConfig');
    //     const defaultConfig = this.data.get('defaultConfig');
    //     const moduleVersion = e.value;
    //     this.dispatch('module-version-change', {
    //         moduleVersion,
    //         nodeConfig: clusterNodeConfig[moduleVersion],
    //         modulesDefaultConfig: defaultConfig.modules
    //     })
    // }

    onTableRowSelected(e: any) {
        // 保存选择的数据
        const selectRow = this.ref('config-table').getSelectedItems();
        if (!selectRow?.length) {
            return;
        }
        const type = this.data.get('currentNode.type');
        this.dispatch('select-row-change', { selectRow: selectRow[0], type });
    }

    studioNameChange() { }

    studioPasswordChange(e: any) { }

    checkStock(rowIndex: number, nodeType: any) {
        return this.data.get(`formData.${nodeType}.selection.disabledIndex`).includes(rowIndex);
    }

    switchAvailableZone() {
        if(this.data.get('tableLoading')) return;
        let palofeSelectedIndex = this.data.get('formData.palofe.selection.selectedIndex')[0];
        let palofeSelectedRow = this.data.get('formData.palofe.selectRow');
        let palobeSelectedIndex = this.data.get('formData.palobe.selection.selectedIndex')[0];
        let palobeSelectedRow = this.data.get('formData.palobe.selectRow');
        const palofeSelectedRowStock = this.getPackageStock(palofeSelectedRow.slotType);
        const palobeSelectedRowStock = this.getPackageStock(palobeSelectedRow.slotType);
        const isPalofeSelectedRowAvailable = !palofeSelectedRowStock || palofeSelectedRowStock.inventoryQuantity > 0;
        const isPalobeSelectedRowAvailable = !palobeSelectedRowStock || palobeSelectedRowStock.inventoryQuantity > 0;
        const palofeNodeType = this.data.get('formData.palofe.nodeType');
        const palobeNodeType = this.data.get('formData.palobe.nodeType');
        const {disabledIndex: palofeDisabledIndex} = this.resolveSelectedAndDisabledIndex('palofe', palofeNodeType);
        const {disabledIndex: palobeDisabledIndex} = this.resolveSelectedAndDisabledIndex('palobe', palobeNodeType);
        if(!isPalofeSelectedRowAvailable) {
            palofeSelectedIndex = -1;
            palofeSelectedRow = {};
        }
        if(!isPalobeSelectedRowAvailable) {
            palobeSelectedIndex = -1;
            palobeSelectedRow = {};
        }
        this.data.set('formData.palofe.selection.disabledIndex', palofeDisabledIndex);
        this.data.set('formData.palobe.selection.disabledIndex', palobeDisabledIndex);
        this.dispatch('module-version-change', {
            palofe: {
                selectedIndex: palofeSelectedIndex,
                selectedRow: palofeSelectedRow,
                disabledIndex: palofeDisabledIndex,
            },
            palobe: {
                selectedIndex: palobeSelectedIndex,
                selectedRow: palobeSelectedRow,
                disabledIndex: palobeDisabledIndex,
            }
        });
        this.forceTableUpdate();
        
    }
    forceTableUpdate() {
        this.data.set('tableLoading', true);
        this.nextTick(() => {
            this.data.set('tableLoading', false);
        })
    }
    // 高可用
     onHighAvailableChange(e: any) {
        this.data.set('formData.highAvailable', e.value);
        this.data.set('formData.knownIsNotHighAvailable', false);
        if(e.value) {
            if(this.data.get('formData.palofe.numValue') < 3) {
                this.data.set('formData.palofe.numValue', 3);
            }
            if(this.data.get('formData.palobe.numValue') < 3) {
                this.data.set('formData.palobe.numValue', 3);
            }
        }
    }

    onKnownChange(e: any) {
        this.data.set('formData.knownIsNotHighAvailable', e.value);
    }

}
