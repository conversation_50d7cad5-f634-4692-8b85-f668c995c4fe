import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Popover} from '@baidu/sui';
import {OutlinedQuestionCircle, OutlinedInfoCircle} from '@baidu/sui-icon';
import './question-tip.less';

export default class QuestionTip extends Component {
    static template = html`
    <span class="tip-custom {{type === 'question' ? 'tip-question' : 'tip-sign'}}">
        <s-popover placement="{{placement}}" getPopupContainer="{{getPopupContainer}}">
            <s-question class="s-question" s-if="{{type === 'question'}}" color="{{color}}" width="14px"/>
            <s-info class="s-exclamation" s-if="{{type === 'sign'}}" color="{{color}}" width="14px"/>
            <div slot="content">
                <slot />
            </div>
        </s-popover>
    </span>`;

    static components = {
        's-popover': Popover,
        's-question': OutlinedQuestionCircle,
        's-info': OutlinedInfoCircle
    };

    initData() {
        return {
            type: 'question',
            placement: 'right',
            color: '#84868C'
        };
    }
}
