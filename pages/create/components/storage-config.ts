/**
 * 存算分离创建页-资源配置
 */

import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {
    Form,
    Radio,
    Select,
    InputNumber
} from '@baidu/sui';
import _ from 'lodash';
import {getDiskNameFormType} from './create-utils';

const tempalte = html`
    <div class="create-available">
        <s-form-item label="计算资源：" prop="cuNums">
            <s-select
                class="mr8"
                value="{{formData.cuNums}}"
                on-change="onCuNumsChange"
            >
            <s-option
                s-for="item in cuDatasource"
                value="{{item.value}}"
                label="{{item.label}}"
                disabled="{{item.disabled}}"
            >
                <div class="cuNum-option">
                    <span>{{item.label}}</span>
                    <span s-if="item.disabled" class="sellout">售罄</span>
                </div>
            </s-option>
            </s-select> CU
            <p slot="help" s-if="{{showTip}}">当前可用区下计算资源全部售罄，请尝试切换可用区</p>
            <p slot="help" s-else>1CU约为1核CPU+4GB内存</p>
        </s-form-item>
        <s-form-item label="缓存配置：" prop="diskSlotInfo" required>
            <s-select
                class="mr8"
                value="{{formData.diskSlotInfo.type}}"
                datasource="{{diskDatasource}}"
                on-change="diskTypeChange"
            />
            <s-input-number
                class="mr5"
                width="100"
                min="{{cdsMin}}"
                step="{{cdsStepSize}}"
                value="{{formData.diskSlotInfo.size}}"
                max="{{cdsMax}}"
                on-change="diskSizeChange"
            />GB
        </s-form-item>
        <s-form-item label="存储资源：" prop="cuResource">
            <s-radio-group
                radioType="button"
                datasource="{{storageResources}}"
                value="1"
            />
            <p slot="help">
                数据存储费用将会按照您的实际使用量，按 GB / 小时进行计费。计费详情请参考
                <a href="https://cloud.baidu.com/doc/PALO/index.html" target="_blank">计费说明</a>
            </p>
        </s-form-item>
    </div>`;

@decorators.asComponent('@storage-config')
export default class StorageConfig extends Component {
    static template = tempalte;

    static components = {
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-input-number': InputNumber,
        's-select': Select,
        's-option': Select.Option
    }

    static computed = {
        cuDatasource() {
            const moduleSlots = this.data.get('moduleSlots') || [];
            const packageStockList = this.data.get('packageStockList') || [];
            const availableZone = this.data.get('formData.availableZone');
            const res = moduleSlots.map(item => ({
                label: item.cuCount,
                value: item.cuCount,
                diskDatasource: item.decoupledDiskSlotInfo || [],
                disabled: _.findIndex(packageStockList, i => i.logicalZone === availableZone && i.packageVersion === item.slot_type && i.available) === -1
            }));
            return res;
        },
        showTip() {
            const moduleSlots = this.data.get('moduleSlots') || [];
            const packageStockList = this.data.get('packageStockList') || [];
            const availableZone = this.data.get('formData.availableZone');
            const cuDatasource = moduleSlots.map(item => ({
                disabled: _.findIndex(packageStockList, i => i.logicalZone === availableZone && i.packageVersion === item.slot_type && i.available) === -1
            }));
            const noStock = _.findIndex(cuDatasource, i => !i.disabled) === -1;
            return cuDatasource.length && noStock;
        }
    }

    initData() {
        return {
            storageResources: [
                {text: '按实际用量计费', value: '1'}
            ],
            stepStrictly: false,
            moduleSlots: [],
            packageStockList: []
        };
    }

    async attached() {
        await this.getCuslots();
    }

    async getCuslots() {
        const result = await this.$http.paloPost('listCuSlot');
        const {moduleSlots, packageStockList} = result;
        this.data.set('moduleSlots', moduleSlots);
        this.data.set('packageStockList', packageStockList);
    }

    onCuNumsChange(e: {value: any}) {
        this.data.set('formData.cuNums', e.value);
        const cuDatasource = this.data.get('cuDatasource');
        const cuItem = _.find(cuDatasource, item => item.value === e.value) || {};
        const diskDatasource = cuItem?.diskDatasource.map(i => ({
            label: getDiskNameFormType(i.cdsType),
            value: i.cdsType,
            ...i
        })) || [];
        this.data.set('diskDatasource', diskDatasource);
        this.diskTypeChange({value: diskDatasource[0].value});
    }

    diskTypeChange(e: { value: any }) {
        const diskDatasource = this.data.get('diskDatasource');
        const diskItem = _.find(diskDatasource, i => i.value === e.value) || {};
        const { cdsMin, cdsMax, cdsRecommend, cdsStepSize, cdsType } = diskItem;
        this.data.set('formData.diskSlotInfo.type', cdsType);
        this.data.set('cdsMin', cdsMin);
        this.data.set('cdsMax', cdsMax);
        this.data.set('cdsStepSize', cdsStepSize);
        this.diskSizeChange({value: cdsRecommend});
    }

    diskSizeChange(e: { value: any }) {
        this.data.set('formData.diskSlotInfo.size', e.value);
        const {diskSlotInfo, cuNums} = this.data.get('formData');
        this.dispatch('storage-change', {
            diskSlotInfo: diskSlotInfo,
            cuNums: cuNums
        });
    }
}