/**
 * 购买信息
*/

import { Component } from 'san';
import { decorators, html, ServiceFactory } from '@baiducloud/runtime';
import {
    Form,
    Input,
    Icon,
    Select,
    Radio,
    Tooltip,
    Switch,
    Row,
    Col
} from '@baidu/sui';
import { timeList, xsTimeList, monthList, yearList } from './create-utils';
const $flag = ServiceFactory.resolve('$flag');

const tempalte = html`
    <div class="create-purchase-info">
        <s-form-item label="购买时长：">
            <s-radio-group
                radioType="button"
                value="{=formData.time=}"
                on-change="onTimeChange"
                name="timeRadio"
            >
                <template s-if="$flag.PaloXS" s-for="time in xsTimeList">
                    <s-radio
                        value="{{time.value}}"
                        class="time-radio {{time.class || ''}}">
                            <s-tooptip  placement="top" content="{{time.tip}}">
                                <span>{{time.text}}</span>
                            </s-tooptip>
                            <span s-if="time.class === 'sale-tip'" class="sale-tip-tag">SALE</span>
                    </s-radio>
                </template>
                <template s-else s-for="time in timeList">
                    <s-radio
                        value="{{time.value}}"
                        class="time-radio {{time.class || ''}}">
                            <s-tooptip  placement="top" content="{{time.tip}}">
                                <span>{{time.text}}</span>
                            </s-tooptip>
                            <span s-if="time.class === 'sale-tip'" class="sale-tip-tag">SALE</span>
                    </s-radio>
                </template>
            </s-radio-group>
        </s-form-item>
        <s-form-item label="自动续费：" class="renew-item">
            <s-radio-group
                datasource="{{renewSource}}"
                value="{=formData.renewalChecked=}"
                enhanced="{{true}}"
                radioType="button"
                on-change="onRenewalChange"
            >
            </s-radio-group>
            <a s-if="!$flag.PaloXS"
                style="cursor:pointer" 
                href="https://cloud.baidu.com/doc/Finance/s/gjwvysrlu"
                target="_blank"
                class="ml12"
            >什么是自动续费？</a>
        </s-form-item>
        <s-form-item label="选择续费周期：" s-if="formData.renewalChecked">
            <s-select
                width="100"
                size="small"
                value="{=formData.autoRenewInfo.renewTimeUnit=}"
                datasource="{{timeUnitList}}"
                on-change="timeUnitChange"
            />
            <template s-if="formData.autoRenewInfo.renewTimeUnit === 'month'">
                <s-select
                    width="100"
                    size="small"
                    value="{=formData.autoRenewInfo.renewTime=}"
                    datasource="{{monthList}}"
                    on-change="renewTimeChange"
                    class="ml8"
                />
            </template>
            <template s-else>
                <s-select
                    width="100"
                    size="small"
                    value="{=formData.autoRenewInfo.renewTime=}"
                    datasource="{{yearList}}"
                    on-change="renewTimeChange"
                />
            </template>
            <span class="ml8">{{formData.autoRenewInfo.renewTime | getTipInfo(renewalTimeList)}}</span>
        </s-form-item>
    </div>`;

@decorators.asComponent('@purchase-info')
export default class PurchaseInfo extends Component {
    static template = tempalte;

    static components = {
        's-form-item': Form.Item,
        's-icon': Icon,
        's-input': Input,
        's-select': Select,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-tooptip': Tooltip,
        's-switch': Switch,
        's-col': Col,
        's-row': Row
    }

    initData() {
        return {
            timeUnitList: [
                {
                    text: '按月',
                    value: 'month'
                },
                {
                    text: '按年',
                    value: 'year'
                }
            ],
            renewSource: [
                {
                    label: '开启',
                    value: true
                },
                {
                    label: '关闭',
                    value: false
                }
            ],
            timeList,
            xsTimeList,
            monthList,
            yearList
        };
    }

    static computed = {
        renewalTimeList: function () {
            const timeListMap: any = {
                month: monthList,
                year: yearList
            };
            const key: string = this.data.get('formData.autoRenewInfo.renewTimeUnit');
            return timeListMap[key];
        }
    }

    static filters = {
        getTipInfo: function (renewTime: string, renewalTimeList: any) {
            return `系统将于到期前7天进行扣费，扣费时长为${renewalTimeList.find((item: any) => item.value === renewTime)?.text}`;
        }
    }

    inited() { }

    onTimeChange(e: any) {
        this.dispatch('update-price', { time: e.value });
    }

    onRenewalChange(e: any) {
        this.dispatch('update-price', { renewalChecked: e.value });
    }

    timeUnitChange(e: any) {
        this.dispatch('time-unit-change', e.value);
    }

    renewTimeChange(e: any) {
        this.dispatch('renew-time-change', e.value);
    }

}