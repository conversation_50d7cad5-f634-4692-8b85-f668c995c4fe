/**
 * 基础设置
*/

import {Component} from 'san';
import {decorators, html} from '@baiducloud/runtime';
import {
    Form,
    Input,
    Icon,
    Select
} from '@baidu/sui';
import {PasswordInput} from '@components/password-input';
import { OutlinedRefresh } from '@baidu/sui-icon';

const tempalte = html`
    <div class="create-basic">
        <s-form-item
            label="集群名称："
            prop="clusterName">
            <s-input
                width="250"
                autocomplete="new-password"
                value="{= formData.clusterName =}"
                on-change="clusterNameChange"
            />
            <p slot="help">支持字母(a-z及A-Z)、数字(0-9),长度小于20个字符</p>
        </s-form-item>
        <s-form-item
            label="内核版本："
            prop="desiredVersion">
            <s-select
                value="{=formData.desiredVersion=}"
                datasource="{{versions}}"
                on-change="onVersionChange"
                width="245"
                filterable
            ></s-select>
        </s-form-item>
        <s-form-item
            label="管理密码："
            prop="clusterPassword">
            <password-input
                width="235"
                value="{= formData.clusterPassword =}"
                on-change="clusterPasswordChange"
            />
            <p slot="help">支持小写字母(a-z)、大写字母(A-Z)、数字(0-9)、!@#$%^&*()_+-=[]{};:,<.>/?| 并且至少包含其中三种, 长度8~16个字符</p>
        </s-form-item>
        <s-form-item
            label="确认密码："
            prop="confirmPassword">
            <password-input
                width="235"
                value="{= formData.confirmPassword =}"
                on-change="confirmPasswordChange"
            />
        </s-form-item>
        <s-form-item
            label="网络类型："
            prop="vpcId">
            <s-select
                width="245"
                value="{= formData.vpcId =}"
                datasource="{{vpcIdDatasource}}"
                placeholder="请选择"
                loading="{{loadingVpc}}"
                loading-text="异步获取数据中"
                on-change="onVpcChange">
            </s-select>
            <s-select
                width="250"
                class="ml8"
                value="{= formData.subnetId =}"
                datasource="{{subnetIdDatasource}}"
                loading="{{loadingSubnet}}"
                loading-text="异步获取数据中"
                placeholder="请选择子网"
                on-change="onSubnetIdChange">
            </s-select>
            <outlined-refresh on-click="refreshSubnet" class="ml8"/>
            <p slot="extra">
                当前子网可用IP共{{availableIp || 0}}个，如需新的私有网络/子网，可
                <a href="/network/#/vpc/instance/list" target="_blank"> 新建VPC</a> 或
                <a href="/network/#/vpc/subnet/list" target="_blank"> 新建子网</a>
            </p>
        </s-form-item>
        <s-form-item
            label="安全组："
            prop="securityGroupId">
            <s-select
                width="245"
                value="{= formData.securityGroupId =}"
                datasource="{{ securityGroupDatasource }}"
                placeholder="请选择"
                loading="{{loadingGroup}}"
                loading-text="异步获取数据中"
                on-change="onSecurityGroupChange"
                noDataText="暂无可用安全组">
            </s-select>
            <outlined-refresh on-click="updateSecurityGroup" class="ml8"/>
            <p slot="help">
                安全组是在服务器外部做防护，如果服务器本身有防火墙（如windows高级安全防火墙，Linux的iptables防火墙）则需要在防火墙做相应设置。
                <br>请在实例创建后，根据实际访问需求添加或修改访问规则。如需新的安全组，可
                <a
                    href="/network/#/vpc/security/list"
                    target="blank"
                >新建安全组</a>
            </p>
        </s-form-item>
    </div>`;

@decorators.asComponent('@basic-setting')
export default class BasicSetting extends Component {
    static template = tempalte;

    static components = {
        's-form-item': Form.Item,
        's-icon': Icon,
        's-input': Input,
        's-select': Select,
        'password-input': PasswordInput,
        'outlined-refresh': OutlinedRefresh
    }

    initData() {
        return {
            vpcIdDatasource: [],
            subnetIdDatasource: [],
            securityGroupDatasource: [],
            versions: [],
            loadingGroup: true,
            loadingSubnet: true,
            loadingVpc: true,
        };
    }

    inited() {
        this.initVpc();
        this.getVersions();
    }

    /**
     * 初始化网络类型数据
     */
    async initVpc() {
        this.data.set('loadingVpc', true);
        const res = await this.$http.paloPost('paloVpcs');
        let vpcId = '';
        const datasource = res.map((item: any) => {
            if (item.defaultVpc) {
                vpcId = item.vpcId;
            }
            return {
                text: `${item.name}(${item.cidr})`,
                value: item.vpcId,
                cidr: item.cidr,
                shortId: item.shortId
            };
        });
        this.data.set('loadingVpc', false);
        this.data.set('vpcIdDatasource', datasource);
        this.onVpcChange({value: vpcId});
    }

    isBeta(version: string) {
        return version.includes('beta');
    }

    async getVersions() {
        const pageType = this.data.get('pageType');
        const api = pageType === 1 ? 'paloVersionList' : 'paloSeperateVersionList';
        const res = await this.$http.paloPost(api);
        this.data.set('versions', res.version.map(i => {
            return {
                text: i,
                value: i
            }
        }));
        this.data.set('formData.desiredVersion', res.defaultVersion || '');
    }

    onVersionChange(e: { value: any; }) {
        this.dispatch('version-change', {desiredVersion: e.value});
    }

     /**
     * 更新子网
     */
      async updateSubnet() {
        const vpcId = this.data.get('formData.vpcId');
        const zone = this.data.get('formData.availableZone');
        if (!vpcId || !zone) {
            return;
        }
        const subnetTypes = [1, 3];

        const params = {
            vpcId,
            zone,
            subnetTypes
        }
        this.data.set('loadingSubnet', true);
        const res = await this.$http.paloPost('paloSubnets', params);
        let defaultSubnet = '';
        this.data.set('availableIp', 0);
        const datasource = res.map((item: any, index: number) => {
            if (item.defaultSubnet) {
                defaultSubnet = item.defaultSubnet;
            }
            return {
                text: `${item.name}(${item.cidr})`,
                value: item.subnetId,
                uuid: item.subnetUuid
            }
        });
        this.data.set('loadingSubnet', false);
        this.dispatch('subnet-id-change', defaultSubnet || '');
          this.data.set('subnetIdDatasource', datasource);
          this.nextTick(() => {
              this.updateSecurityGroup();
          });
      }
    
    async onSubnetIdChange(target: { value: string }) {
        const subnetId = target.value;
        const res = await this.$http.get(`/api/network/v1/subnet/${subnetId}/resource`, {});  
        this.data.set('availableIp', res.availableIp);
    }

    /**
     * 更新安全组
     */
    async updateSecurityGroup() {
        const vpcId =  this.data.get('formData.vpcId');
        this.data.set('loadingGroup', true);
        const res = await this.$http.paloPost('paloSecurityList',{vpcId});
        let securityGroupId = '';
        const datasource = res.result.map((group: { id: any; name: string, desc: string}) => {
            if (group.desc === 'default') {
                securityGroupId = group.id;
            }
            return {
                ...group,
                text: group.name,
                value: group.id
            }
        }
        );
        this.data.set('loadingGroup', false);
        this.data.set('securityGroupDatasource', datasource);
        this.dispatch('security-group-id-change', securityGroupId);
    }

    /**
     * 网络类型改变
     */
     onVpcChange(e: { value: any; }) {
         this.dispatch('vpc-id-change', e.value);
         this.nextTick(() => {
             this.updateSubnet();
         });
    }

    /**
     * 根据安全组id获取名字
     * @param securityGroupId 
     * @returns 
     */
    getSecurityGroupNameById(securityGroupId: string) {
        return this.data.get('securityGroupDatasource').find((item: any) => item.id === securityGroupId).name;
    }

    refreshSubnet() {
        this.initVpc();
    }


}