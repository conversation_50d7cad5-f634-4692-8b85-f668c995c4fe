/**
 * 计费模式及可用区
 */

import {Component} from 'san';
import {decorators, html, ServiceFactory} from '@baiducloud/runtime';
import {
    Form,
    Radio,
    Tooltip
} from '@baidu/sui';
import {OutlinedQuestionSquare} from '@baidu/sui-icon';
import {ContextService} from '@common/index';
import QuestionTip from './question-tip';
const $flag = ServiceFactory.resolve('$flag');

const tempalte = html`
    <div class="create-available">
        <s-form-item label="计费模式：" prop="productType">
            <span slot="label">
                <span>计费模式：</span>
                <question-tip placement="right" type="question">
                    包年包月：需要提前一次性支付所选时间段内的费用，这种模式价格相较于后付费模式更低廉。<br>
                    按量付费：按实例的实际使用量进行收费，不需要提前支付费用，但需要保证余额大于100元，这种模式比包年包月模式价格高一些。<br>
                </question-tip>
            </span>
            <div class="payment-card-list">
                <div
                    s-for="item in productTypeList"
                    on-click="onProductTypeChange(item.value)"
                    class="payment-card-item {{formData.productType === item.value ? 'active' : ''}}"
                >
                    <i class="payment-card-item__icon {{item.class}}"></i>
                    <div class="payment-card-item__info">
                        <h4 class="payment-card-item__info-title">{{item.text}}</h4>
                        <p class="payment-card-item__info-desc">{{item.desc}}</p>
                    </div>
                    <span s-if="item.tag" class="payment-card-item__tag">{{item.tag}}</span>
                </div>
            </div>
        </s-form-item>
        <s-form-item label="当前地域：" prop="region">
            <s-radio-group
                radioType="button"
                enhanced="{{true}}"
                datasource="{{regionList}}"
                value="{=formData.region=}"
                on-change="onRegionChange"
            />
            <p class="tip-gray">不同地域云产品之间内网不互通；选择最靠近您客户的地域，可降低访问延时，创建成功后不支持更换地域。</p>
        </s-form-item>
        <s-form-item label="可用区：" prop="availableZone">
            <span slot="label">
                <span>可用区：</span>
                <question-tip placement="right" type="question">
                    可用区是指在同一区域下，电力和网络互相独立的区域，故障会被隔离在一个可用区内。如果您的应用程序需要更高的高可用性，建议您将云服务创建在不同的可用区内。
                    <a s-if="!$flag.PaloXS" href="https://cloud.baidu.com/doc/Reference/Regions.html#.E5.8F.AF.E7.94.A8.E5.8C.BA" target="_blank">了解详情。</a>
                </question-tip>
            </span>
            <s-radio-group
                radioType="button"
                datasource="{{zoneList}}"
                enhanced="{{true}}"
                value="{= formData.availableZone =}"
                on-change="onAvailableZoneChange"
            />
        </s-form-item>
    </div>`;

@decorators.asComponent('@available-zone')
export default class AvailableZone extends Component {
    static template = tempalte;

    static components = {
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-tooltip': Tooltip,
        's-question-square': OutlinedQuestionSquare,
        'question-tip': QuestionTip,
    }

    initData() {
        return {
            productTypeList: [
                {
                    alias: 'PREPAID',
                    text: '包年包月',
                    value: 'prepay',
                    desc: '先付费后使用，价格更低廉',
                    tag: '推荐',
                    class: 'prepaid'
                },
                {
                    alias: 'POSTPAID',
                    text: '按量付费',
                    value: 'postpay',
                    desc: '先使用后付费，按需开通',
                    class: 'postpaid'
                }
            ],
            regionList: [],
            zoneList: [],
        };
    }

    inited() {
        this.initRegionData();
        this.initZoneList();
    }

    /**
     * 初始化地域数据
     */
    initRegionData() {
        const list: { label: string; value: string; }[] = [];
        const region = ContextService.SERVICE_TYPE.PALO.region;
        Object.keys(region).forEach((key: any) => {
            list.push(
                {
                    label: region[key],
                    value: key
                }
            );
        });
        const pageType = this.data.get('pageType');
        const regionList = pageType === 2 ? [{
            label: '华北 - 北京',
            value: 'bj'
        }] : list;
        this.data.set('regionList', regionList);
    }


    /**
     * 初始化可用区数据
     */
    async initZoneList() {
        const res = await this.$http.paloPost('paloZoneList');
        let availableZone: any = '';
        const zoneList = res.map((item: any) => {
            if (item.defaultZone) {
                availableZone = item.zone;
            }
            return {
                value: item.zone,
                label: item.zone.replace(/zone/g, '可用区')
            }
        });
        this.data.set('zoneList', zoneList);
        this.dispatch('available-zone-change', {
            availableZone,
            isInit: true
        });
        this.dispatch('init-module-version', '');
    }

    /**
     * 计费模式切换：更新费用
     */
    onProductTypeChange(value: string) {
        this.data.set('formData.productType', value);
        this.dispatch('update-price', {productType: value});
    }

    /**
     * 当前地域改变
     */
    onRegionChange(e: { value: any; }) {
        this.dispatch('region-change', e.value);
    }

    /**
     * 可用区改变
     */
    onAvailableZoneChange(e: { value: any; }) {
        this.dispatch('available-zone-change', {
            availableZone: e.value,
            isInit: false,
        });
    }

}