/**
 * 激活页
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {Notification, Alert} from '@baidu/sui';
import {AppOrderPage} from '@baidu/sui-biz';
import {Component} from 'san';
import bgImgSrc from '../../static/background.png';
import logoSrc from '../../static/right.png';
import checkActive from '@common/check-active';
import './index.less';
const klass = 'palo-active';

const { asPage } = decorators;
@asPage('/palo/active')
export default class extends Component {
    static template = html`
    <div>
        <div class="${klass}-order-container">
            <s-order-page
                class="${klass}-s-order-page"
                title="{{title}}"
                bgImgSrc="{{bgImgSrc}}"
                logoSrc="{{logoSrc}}"
                desc="{{desc}}"
                process="{{process}}"
                protocal="{{protocal}}"
                advantage="{{advantage}}"
                useNewVersion="{{true}}"
                openBtnDisabled="{{openBtnDisabled}}"
                on-click="onOrderCreate"
            >
            </s-order-page>
        </div>
    </div>`;

    static components = {
        's-order-page': AppOrderPage,
        's-alert': Alert
    };

    static computed: SanComputedProps = {
        desc() {
            const desc1 = `百度数据仓库Palo是基于百度开源的ApacheDoris打造的企业级数仓产品，支持海量数据高效导入、实时更新，致力于帮助企业快速且低成本地构建极速易用的数据分析平台。`;
            return desc1;
        },
        title() {
            const title1 = '百度数据仓库PALO';
            return title1;
        }
    };

    initData() {
        return {
            bgImgSrc: bgImgSrc,
            logoSrc: logoSrc,
            protocal: {
                text: '同意使用',
                content: [
                    {
                        content: '《百度智能云使用协议》',
                        link: 'https://cloud.baidu.com/doc/Agreements/index.html'
                    },
                    {
                        content: '《服务等级协议》',
                        link: 'https://cloud.baidu.com/doc/PALO/s/sksis5ies'
                    }
                ]
            },
            advantage: {
                content: [
                    {
                        title: "极致性能",
                        desc: "100%PB级别数据毫秒/秒级延时，向量执行引擎，性能优异，海量数据无缝应用",
                        imgSrc: require('../../static/excellent-performance.png'),
                    },
                    {
                        title: "场景丰富",
                        desc: "支持对接多种数据源，支持在离线数据统一查询，支持湖仓联邦查询",
                        imgSrc: require('../../static/situation.png'),
                    },
                    {
                        title: "简单易用",
                        desc: "兼容MySQL协议和标准SQL，可以快速对接多种数据应用场景",
                        imgSrc: require('../../static/easy-use.png'),
                    }
                ]
            },
            process: {
                content: [
                    {
                        title: '创建资源',
                        desc: `您可以灵活选择的计费模式、配置项，针对不同的应用场景，选择不同规格的实例，生产环境请开启高可用选项`,
                    },
                    {
                        title: '连接服务',
                        desc: '创建资源后，您可以通过多种方式访问数据仓库，连接后可以进行数据导入导出，以及创建数据库表、帐户、查询等操作',
                    },
                    {
                        title: '管理资源',
                        desc: '根据业务发展需要，您可以随时添加集群实例的节点、扩容存储、变更规格、备份及恢复数据。除此之外，还可以实时查看监控指标，以便及时了解业务的健康状态',
                    }
                ]
            },
        };
    }

    // 服务开通
    async onOrderCreate() {
        try {
            await this.$http.paloPost('paloServiceActivate');
            Notification.success('服务开通成功');
            redirect(`#/palo/list`);
         } catch(e) {
            Notification.error('服务开通失败');
         }
    }

    // region 切换
    onRegionChange() {
        checkActive();
    }
}