/**
 * all pages
 * @file pages/index.js
 * <AUTHOR>
 */

import './active';
import './list';
import './list/storage-list';
import './detail';
import './detail/storage';
import './detail/components/message';
import './detail/components/monitor';
import './detail/components/compute-manage';
import './detail/components/storage-message';
import './stretch';
import './create';
import './create/storage';
import './create/components/storage-config';
import './create/components/available-zone';
import './create/components/basic-setting';
import './create/components/node-config';
import './create/components/purchase-info';
import './create/components/create-cluster';
import './renewal';
import './renewal/components/renewal-info';

import '@baidu/bce-billing-sdk-san/es/style/index.css';
import './index.less';
import './../style/index';
import './../style/ue4.less';

window.onload = function () {
  const hash = window.location.hash
  if(!hash) {
    window.location.hash = '/palo/list'
    window.location.reload()
  }
}