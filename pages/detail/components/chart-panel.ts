import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Loading} from '@baidu/sui';
import Chart from '@components/chart';
import {SCOPE} from '@common/config/constant';

export default class ChartPanel extends Component{
    static template = html`
    <s-loading loading="{{loading}}" style="width: 100%;">
        <div class="chart-panel" s-ref="chart-panel">
            <template s-for="item, index in indicators">
                <chart
                    s-ref="chart-{{item.key}}"
                    item="{{item}}"
                    numPerLine="{{numPerLine}}"
                    diskTypes="{{diskTypes}}"
                    nameType="{{nameType}}"
                    startTime="{{time.startTime}}"
                    endTime="{{time.endTime}}"
                    type="{{type}}"
                    hasNotInstallAuditPlugin="{{hasNotInstallAuditPlugin}}"
                    dimensions="{{dimensions}}"
                    hideLegend="{{dimensions.length <= 1}}"
                    scope="${SCOPE}"
                    precision="{{2}}"
                    deployId="{{deployId}}"
                >
                </chart>
            </template>
        </div>
    </s-loading>
    `;

    initData() {
        return {
            indicators: [],
            loading: true,
        }
    }

    static components = {
        's-loading': Loading,
        chart: Chart
    }

    static computed = {
        indicators() {
            const indicatorList : any[] = this.data.get('indicatorList') || [];
            const statistics: string = this.data.get('statistics');
            const indicators = indicatorList.map(item => ({
                key: item.key,
                name: item.label,
                metrics: [{
                    name: item.label,
                    value: item.value,
                }],
                unit: item.unit,
                statistics
            }));
            return indicators;
        }
    }

    attached() {
        this.watch('dimensions', () => {
            this.refresh();
        });
        // 监听滚动事件 - 联动tree组件
        this.ref('chart-panel')?.addEventListener('scroll', this.handleScroll.bind(this));
    }
    detached() {
        this.ref('chart-panel')?.removeEventListener('scroll', this.handleScroll.bind(this));
    }
    refresh() {
        const indicators = this.data.get('indicators');
        if (!indicators || !indicators.length) {
            return;
        }
        for (let i = 0; i < indicators.length; i++) {
            if (this.ref(`chart-${indicators[i].key}`)) {
                this.ref(`chart-${indicators[i].key}`)?.refresh();
            }
        }
    }
    handleScroll(e) {
        const indicators = this.data.get('indicators');
        const monitorContainerTop = e.target.getBoundingClientRect().top;
        if (!indicators || !indicators.length || !monitorContainerTop) {
            return;
        }
        for(const indicator of indicators) {
            const chart = this.ref(`chart-${indicator.key}`);
            if (chart && chart?.el.getBoundingClientRect().top >= monitorContainerTop) {
                this.fire('scroll', {key: indicator.key, el: chart?.el});
                break;
            }
        }
    }
}