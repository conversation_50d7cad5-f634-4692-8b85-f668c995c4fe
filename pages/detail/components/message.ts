/**
 * 集群详情
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Component, defineComponent} from 'san';
import {AppLegend, ClipBoard} from '@baidu/sui-biz';
import {Eye} from '@baidu/xicon-san';
import {Table, Button, Notification, Pagination, Tooltip, Loading, Dialog} from '@baidu/sui';
import TileInfo from './tile-info';
import EipBind from './eip-bind';
import PasswordConfirmDialog from './password-reset';
import {renderStatus, formatTime, compareVersions} from '@common/utils'
import {hardwareColumns, clusterExampleColumns} from './columns';
import {PALO_TYPE_MAPPING, PayType} from '@common/config';
import {NODE_TYPE_COMPUTE} from '../../create/components/create-utils';
import VersionUpgradeDialog from './version-upgrade';
import ConfigEditDialog from './edit-config';

const klass = 'palo-cluster-detail-info';

const TEXT_MAP = {
    deployId: '集群ID：',
    deployName: '集群名称：',
    region: '区域：',
    productType: '付费方式：',
    expireTime: '到期时间：',
    availableZone: '可用区：',
    adminUsername: '管理员账号：',
    vpc: '所在网络（VPC）:',
    subnet: '子网：',
    securityGroup: '安全组：',
    paloFeEIPStatus: 'Leader Node协议公网地址 ：',
    paloBeEIPStatus: 'Compute Node协议公网地址 ：',
    mysqlProtocalEndpoint: 'MySQL 协议连接目标：',
    httpProtocalEndpoint: 'Leader Node HTTP协议连接目标：',
    beHttpProtocalEndpoint: 'Compute Node HTTP协议连接目标：',
    jdbcUrl: 'JDBC URL：',
    odbcUrl: 'ODBC URL：',
    paloUIAddress: 'UI 地址：',
    blbsPalofe: 'FE服务发布点：',
    blbsPalobe: 'BE服务发布点：',
    version: '内核版本：',
    config: '缓存配置：'
};

const renderItem = (
    label: string | number,
    key: string,
    text: string | string[] | number | Object = '',
    isShow: boolean = true,
    wide: boolean = false,
    canCopy: boolean = false,
    labelWidth: number) => ({label, key, text, isShow, wide, canCopy, labelWidth});


const arr = ['unbinding', 'binding'];

export default class Info extends Component{
    static template = html`
    <div class="${klass} palo-tab-info">
        <s-loading s-if="{{isLoading}}" size="large" loading="{{true}}" style="width: 100%; height: 100px; margin-top: 100px"></s-loading>
        <template s-else>
            <tile-info
                s-for="item,index in infoList"
                isLast="{{index === infoList.length - 1}}"
                type="{{item.type}}"
                title="{{item.title}}"
                list="{{item.list}}"
                showAlert="{{item.showAlert}}"
            >
                
                <span slot="c-adminUsername-text" class="${klass}__adminUser">
                    {{text}}
                    <s-button
                        skin="stringfy"
                        on-click="openPasswordResetDialog"
                        class="table-btn-slim ml8 passreset"
                        disabled="{{passwordDisabled}}"
                    >
                        密码重置
                    </s-button>
                </span>
                <span slot="c-version-text" s-if="{{versionDisabled}}">
                    {{text}}
                    <sui-tooltip content="{{unableDes}}" placement="top">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim ml8"
                            on-click="openVersionUpgradeDialog"
                            disabled="{{true}}"
                        >版本升级</s-button>
                    </sui-tooltip>
                </span>
                <span slot="c-version-text" s-else>
                    {{text}}
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim ml8"
                        on-click="openVersionUpgradeDialog"
                    >版本升级</s-button>
                </span>
                <span slot="c-config-text">
                    {{text}}
                    <s-button
                        skin="stringfy"
                        on-click="editConfig"
                        class="table-btn-slim ml8 passreset"
                        disabled="{{editConfigDisabled}}"
                    >
                        修改
                    </s-button>
                </span>
                <span slot="c-blbsPalofe-text">
                    <s-loading loading="{{true}}" s-if="eiploading === 'fe'"></s-loading>
                    <span s-else-if="{{text}}">
                        {{text}}
                        <s-eye
                            s-if="showFeEye"
                            class="hover-cursor"
                            on-click="servicePoint('check', 'fe')"
                            theme="line"
                            color="#000"
                            size="{{16}}"
                            strokeLinejoin="round"
                        />
                        <span class="box-icon" s-else>
                            <s-clip-board class="clipboard-default" text="{{text}}" />
                            <s-button skin="stringfy" on-click="deletPoint(text)">删除</s-button>
                        </span>
                    </span>
                    <span s-else>
                        <a on-click="servicePoint('create', 'fe')" href="javascript:void(0)">创建服务发布点</a>
                    </span>
                </span>
                <span slot="c-blbsPalobe-text">
                    <s-loading loading="{{true}}" s-if="eiploading === 'be'"></s-loading>
                    <span s-else-if="{{text}}">
                        {{text}}
                        <s-eye
                            class="hover-cursor"
                            s-if="showBeEye"
                            on-click="servicePoint('check', 'be')"
                            theme="line"
                            color="#000"
                            size="{{16}}"
                            strokeLinejoin="round"
                        />
                        <span class="box-icon" s-else>
                            <s-clip-board class="clipboard-default" text="{{text}}" />
                            <s-button skin="stringfy" on-click="deletPoint(text)">删除</s-button>
                        </span>
                    </span>
                    <span s-else>
                        <a on-click="servicePoint('create', 'be')" href="javascript:void(0)" >创建服务发布点</a>
                    </span>
                </span>
                <span slot="c-paloFeEIPStatus-text">
                    <span s-if="{{text === 'binded'}}">
                        {{detail.paloFeEIP}}
                        <s-button skin="stringfy" on-click="unBindEip('palofe')">{{text | bindEipText}}</s-button>
                    </span>
                    <span s-else>
                        {{text | filterEipStatus}}
                        <s-button skin="stringfy" on-click="bindEip('palofe')">{{text | bindEipText}}</s-button>
                    </span>
                </span>
                <span slot="c-paloUIAddress-text">
                    <a href="{{text}}" target="_blank">{{text}}</a>
                </span>
                <span slot="c-paloBeEIPStatus-text">
                    <span s-if="{{text === 'binded'}}">
                        {{detail.paloBeEIP}}
                        <s-button skin="stringfy" on-click="unBindEip('palobe')">{{text | bindEipText}}</s-button>
                    </span>
                    <span s-else>
                        {{text | filterEipStatus}}
                        <s-button skin="stringfy" on-click="bindEip('palobe')">{{text | bindEipText}}</s-button>
                    </span>
                </span>
            </tile-info>
            <s-append label="硬件配置" class="title" noHighlight />
            <s-table columns="{{hardwareColumns}}" datasource="{{modules}}" class="mb24"> </s-table>
            <s-append label="集群实例" class="title" noHighlight />
            <s-table columns="{{clusterExampleColumns}}" datasource="{{instances}}">
                <div slot="c-exampleStatus">
                    {{row.displayActualStatus | filterStatus(row.status) | raw}}
                </div>
                <div slot="c-operate">
                    <template s-if="{{row.restartable}}">
                        <s-button
                            disabled="{{detail.status !== 'Running'}}"
                            s-if="{{row.status === 'Stopped'}}"
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="changeClusterExample('start', row)"
                        >
                            开启
                        </s-button>
                        <s-button
                            disabled="{{detail.status !== 'Running'}}"
                            s-if="{{row.status === 'Running'}}"
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="changeClusterExample('stop', row)"
                        >
                            暂停
                        </s-button>
                        <s-button
                            disabled="{{row.moduleType | getShowDeleteExample(instances)}}"
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="deleteClusterExample(row)"
                        >
                            删除
                        </s-button>
                    </template>
                    <template s-else>-</template>
                </div>
            </s-table>
        </template>
    </div>`;

    static computed: SanComputedProps = {
        infoList() {
            const detail = this.data.get('detail');
            const wide = true;
            const canCopy = true;
 
            const {
                deployId,
                deployName,
                expireTime,
                productType,
                adminUsername,
                availableZone,
                vpc,
                subnet,
                paloFeEIPStatus,
                paloBeEIPStatus,
                mysqlProtocalEndpoint,
                httpProtocalEndpoint,
                beHttpProtocalEndpoint,
                jdbcUrl,
                odbcUrl,
                paloUIAddress,
                blbs,
                modules,
                highAvailability,
                kernelConf,
                securityGroup,
                securityGroupId,
            } = detail;

            const version = modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;

            const securityGroupDatasource = this.data.get('securityGroupDatasource');

            const BaseInfo = {
                title: '基本信息',
                type: 'baseinfo',
                showAlert: !highAvailability && deployId != null,
                list: [
                    renderItem(TEXT_MAP.deployId, 'deployId', deployId, true, false, canCopy, 60),
                    renderItem(TEXT_MAP.deployName, 'deployName', deployName, true, false, false, 72),
                    renderItem(TEXT_MAP.version, 'version', version, true, false, false, 72),
                    renderItem(TEXT_MAP.expireTime, 'expireTime', formatTime(expireTime), true, false, false, 60),
                    renderItem(TEXT_MAP.productType, 'productType', PayType[productType], true, false, false, 60),
                    renderItem(TEXT_MAP.adminUsername, 'adminUsername', adminUsername, true, false, false, 72),
                    renderItem(TEXT_MAP.region, 'region', window.$context.getCurrentRegion().label, true, false, false, 60),
                    renderItem(TEXT_MAP.availableZone, 'availableZone', '可用区' + availableZone?.slice(4,), true, false, false, 60),
                ]
            };

            const AccessInfo = {
                title: '连接信息',
                type: 'accessInfo',
                list: [
                    renderItem(TEXT_MAP.mysqlProtocalEndpoint, 'mysqlProtocalEndpoint', mysqlProtocalEndpoint, true, wide, canCopy, 204.5),
                    renderItem(TEXT_MAP.httpProtocalEndpoint, 'httpProtocalEndpoint', httpProtocalEndpoint, true, wide, canCopy, 204.5),
                    renderItem(TEXT_MAP.beHttpProtocalEndpoint, 'beHttpProtocalEndpoint', beHttpProtocalEndpoint, true, wide, canCopy, 204.5),
                    renderItem(TEXT_MAP.jdbcUrl, 'jdbcUrl', jdbcUrl, true, wide, canCopy, 204.5),
                    renderItem(TEXT_MAP.odbcUrl, 'odbcUrl', odbcUrl, true, wide, canCopy, 204.5),
                    renderItem(TEXT_MAP.paloUIAddress, 'paloUIAddress', paloUIAddress, true, wide, canCopy, 204.5),
                    renderItem(TEXT_MAP.blbsPalofe, 'blbsPalofe', blbs?.palofe, true, wide, false, 204.5),
                    renderItem(TEXT_MAP.blbsPalobe, 'blbsPalobe', blbs?.palobe, true, wide, false, 204.5),
                ]
            };

            const ConfigInfo = {
                title: '配置信息',
                type: 'configInfo',
                list: [
                    renderItem(TEXT_MAP.vpc, 'vpc', vpc, true, false, false),
                    renderItem(TEXT_MAP.subnet, 'subnet', subnet, true, false, false),
                    renderItem(TEXT_MAP.securityGroup, 'securityGroup', securityGroupId 
                        ? securityGroupDatasource.find((item: any) => item.id === securityGroupId)?.name 
                        : (securityGroup ?? '-'), true, false, false),
                    renderItem(TEXT_MAP.paloFeEIPStatus, 'paloFeEIPStatus', paloFeEIPStatus, true, true, false, 173),
                    renderItem(TEXT_MAP.paloBeEIPStatus, 'paloBeEIPStatus', paloBeEIPStatus, true, true, false, 173),
                ]
            };

            if (kernelConf?.enable_file_cache === 'true') {
                ConfigInfo.list.splice(2, 0, renderItem(TEXT_MAP.config, 'config', kernelConf?.file_cache_path_total + 'G/节点', true, false, false))
            }
            else if (compareVersions('2.0', version) <= 0) {
                ConfigInfo.list.splice(2, 0, renderItem(TEXT_MAP.config, 'config', '未开启', true, false, false))
            }

            return [
                BaseInfo,
                ConfigInfo,
                AccessInfo,
            ];
        },
        versionDisabled() {
            const upgradeList = this.data.get('upgradeList') || [];
            const status = this.data.get('detail.status');
            return upgradeList.length === 0 || status !== 'Running';
        },
        unableDes() {
            const modules = this.data.get('detail.modules');
            const version = modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
            return version < '2.0' ? '当前版本暂不支持自动升级，如有升级需求请提交工单' : '没有可升级的版本'
        }
    }

    static filters: SanFilters = {
        filterEipStatus(value: string) {
            switch (value) {
                case 'unbind': return '未分配';
                case 'binded': return value;
                case 'binding': return 'EIP绑定中，请稍后刷新';
                case 'unbinding': return 'EIP解绑中，请稍后刷新'
            }
        },
        filterStatus(displayActualStatus: string, status: string) {
            return renderStatus(displayActualStatus, status);
        },
        bindEipText(status: string) {
            switch (status) {
                case 'unbind': return '绑定EIP';
                case 'binded': return '解绑EIP';
                case 'binding': return '刷新';
                case 'unbinding': return '刷新'
            }
        },
        getShowDeleteExample(moduleType: string, instances: any[]) {
            const status = this.data.get('detail.status');
            const beNum = instances.filter((item: any) => item.moduleType === NODE_TYPE_COMPUTE).length;
            return status !== 'Running' || !(beNum > 1 && moduleType === NODE_TYPE_COMPUTE);
        },
    };

    static components = {
        's-append': AppLegend,
        'tile-info': TileInfo,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'sui-tooltip': Tooltip,
        's-loading': Loading,
        's-eye': Eye,
        's-clip-board': ClipBoard,
    };

    initData() {
        return {
            detail: {},
            table: {
                datasource: [],
                columns: []
            },
            showFeEye: true,
            showBeEye: true,
            isLoading: false,
            modules: [],
            instances: [],
            hardwareColumns: hardwareColumns,
            clusterExampleColumns: clusterExampleColumns,
            upgradeList: [],
            upgradeModes: [
                {
                    value: 'ROLLING',
                    label: '滚动升级'
                },
                {
                    value: 'ALL',
                    label: '全量升级'
                }
            ],
            upgradeModesList: ['ROLLING'],
            securityGroupDatasource: []
        };
    }

    attached() {
        this.watch('isLoading', val => this.getUpgradeVersions());
        this.watch('detail', (value) => {
            value?.vpcId && this.getSecurityGroup();
        });
    }

    async getSecurityGroup() {
        const vpcId = this.data.get('detail.vpcId');
        const res = await this.$http.paloPost('paloSecurityList',{vpcId});
        this.data.set('securityGroupDatasource', res.result);
    }

    refresh() {
        this.data.set('showFeEye', true);
        this.data.set('showBeEye', true);
    }

    servicePoint(type: 'create' | 'check', moduleType: 'fe' | 'be') {
        this.data.set('eiploading', moduleType);
        const params = {
            deployId: this.data.get('detail.deployId'),
            name: this.data.get('detail.deployName'),
            moduleType
        };
        const method = type === 'create' ? 'paloServicePointCreate' : 'paloServicePointCheck';
        this.$http.paloPost(method, params).then((res: {palofe: string; palobe: string}) => {
            const {palofe, palobe} = res;
            if (moduleType === 'be') {
                this.data.set('detail.blbs.palobe', palobe);
                this.data.set('showBeEye', type === 'create');
            } else {
                this.data.set('detail.blbs.palofe', palofe);
                this.data.set('showFeEye', type === 'create');
            }
            this.data.set('eiploading', '');
        });
    }

    async deletPoint(text: string) {
        const {deployId, deployName} = this.data.get('detail');
        await Dialog.warning({
            content: `您确定删除发布点吗？`,
            title: '提示',
            onOk: async () => {
                try {
                    await this.$http.paloPost('deletePoint', {
                        deployId,
                        service: text,
                        name: deployName
                    });
                    Notification.success('操作成功');
                    this.fire('refresh', {});
                } catch (e) {
                    Notification.error('操作失败，请重试！');
                }
            }
        });
    }

    async unBindEip(type: string) {
        const {paloFeEIPStatus, paloBeEIPStatus, paloFeEIP, paloBeEIP} = this.data.get('detail');
        if (type === 'palofe' && _.includes(arr, paloFeEIPStatus) && !paloFeEIP) {
            this.fire('refresh', {});
            return;
        }
        else if (type === 'palobe' && _.includes(arr, paloBeEIPStatus) && !paloBeEIP) {
            this.fire('refresh', {});
            return;
        }
        const deployId = this.data.get('detail.deployId');
        try {
            const tip = PALO_TYPE_MAPPING.getAliasFromValue(type);
            await Dialog.warning({
                content: `您确定解绑 ${tip} EIP吗？`,
                title: '提示'
            });
            let params = {
                deployId: deployId,
                moduleTemplateName: type
            };
            try {
                await this.$http.paloPost('paloEipUnbind', params);
                Notification.success('操作成功');
                this.fire('refresh', {});
            } catch (e) {
                Notification.error('解绑失败，请重试！');
            }
        } catch (e) {}
    }

    bindEip(type: string) {
        const {paloFeEIPStatus, paloBeEIPStatus} = this.data.get('detail');
        if (type === 'palofe' && _.includes(arr, paloFeEIPStatus)) {
            this.fire('refresh', {});
            return;
        }
        else if (type === 'palobe' && _.includes(arr, paloBeEIPStatus)) {
            this.fire('refresh', {});
            return;
        }
        const dialog = new EipBind({
            data: {
                openEip: true,
                detail: this.data.get('detail'),
                bindType: type
            }
        });
        dialog.on('success', () => {
            this.fire('refresh', {});
        });
        dialog.attach(document.body);
        return dialog;
    }

    openPasswordResetDialog() {
        const deployId = this.data.get('detail.deployId');
        const dialog = new PasswordConfirmDialog({
            data: {
                deployId
            }
        });
        dialog.on('success', () => {});
        dialog.attach(document.body);
        return dialog;
    }

    editConfig() {
        const { deployId, kernelConf, modules } = this.data.get('detail');
        const currentNode = modules?.find((item: any) => item.type === NODE_TYPE_COMPUTE);
        const diskSize = currentNode?.slotDescription?.split(' ')?.pop();
        const dialog = new ConfigEditDialog({
            data: {
                deployId,
                diskSize: parseInt(diskSize),
                formData: {
                    cache_endable: kernelConf?.enable_file_cache === 'true',
                    cache_size: kernelConf?.file_cache_path_total,
                    roll_restart: true
                }
            }
        });
        dialog.on('success', () => {
            this.fire('refresh', {});
        });
        dialog.attach(document.body);
        return dialog;
    }
    async changeClusterExample(type: string, row: {instanceId: any}) {
        const apiType: any = {
            start: 'startInstance',
            stop: 'stopInstance'
        };
        const {instanceId} = row;
        const deployId = this.data.get('detail.deployId');
        await this.$http.paloPost(apiType[type], {
            instanceId,
            deployId
        });
        Notification.success('操作成功');
        this.fire('refresh', {});
    }

    async deleteClusterExample(row: any) {
        const { instanceId } = row;
        await Dialog.confirm({
            content: defineComponent({
                template: `<div><span style="color: red">请先</span>
            <a target="_blank" href="https://ticket.bce.baidu.com/?_=1634632468566#/ticket/create~productId=50&questionId=107&channel=2">提交工单</a>
            <span style="color: red">, 由专业工程师帮助您进行数据迁移。</span>（实例ID：${instanceId})</div>`}),
            okText: '确认已迁移完成',
            width: 400
        });
        await Dialog.confirm({
            title: '确认删除实例？',
            content: defineComponent({
                template: `<p><span style="color: red">删除实例将同时删除实例中全部数据，不进行迁移将会有数据丢失及集群不可用的风险。</span>
            <br/>删除前请务必提交工单迁移数据。</p>`}),
            width: 400,
            onOk: async () => {
                const deployId = this.data.get('detail.deployId');
                await this.$http.paloPost('deleteInstance', {
                    instanceId,
                    deployId
                });
                Notification.success('操作成功');
                this.fire('refresh', {});
            }
        });
    }

    async getUpgradeVersions() {
        if (this.data.get('isLoading')) {
            return;
        }
        const params = {
            deployId: this.data.get('deployId')
        }
        const res = await this.$http.paloPost('getUpgradeList', params);
        this.data.set('upgradeList', res.upgradeEngineVersions);
        this.data.set('upgradeModesList', res.upgradeModes);
        this.data.set('oldEngineVersion', res.oldEngineVersion);
    }

    openVersionUpgradeDialog() {
        const {
            deployId,
            deployName,
        } = this.data.get('detail');
        const {upgradeModesList, oldEngineVersion, upgradeModes, upgradeList} = this.data.get('');
        const dialog = new VersionUpgradeDialog({
            data: {
                deployId,
                deployName,
                oldEngineVersion,
                versions: upgradeList.map(i => ({label: i, value: i})),
                upgradeModes: upgradeModes.filter(item => _.includes(upgradeModesList, item.value)),
            }
        });
        dialog.on('success', () => {
            this.fire('refresh', {});
        });
        dialog.attach(document.body);
        return dialog;
    }
};