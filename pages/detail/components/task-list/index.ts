import {Component} from 'san';
import {Table, Button, Progress, Pagination, Loading, Tooltip} from '@baidu/sui';
import {AppListPage} from '@baidu/sui-biz';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {html} from '@baiducloud/runtime';
import {formatTime, computeTimeDistance, renderStatus, pickEmpty, formateSize} from '@common/utils/index';
import {AllEnum, taskTypeEnum, taskStatus, stageType} from '@common/config';
import {taskType} from '@common/config/constant';
import {getDiskNameFormType} from '@pages/create/components/create-utils';
import './index.less';

const klass = 'palo-task-list';
const allEnum = AllEnum.toArray();
export default class TaskList extends Component {
    static template = html`
        <div class="${klass}">
            <app-list-page class="${klass}_content">
                <div slot="bulk"></div>
                <div slot="filter">
                    <s-button on-click="getComList" class="ml5 s-icon-button">
                        <s-icon-refresh />
                    </s-button>
                </div>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    width="{{1100}}"
                    loading="{{loading}}"
                    on-sort="onSort"
                    on-filter="onFilter"
                >
                    <div slot="c-status">
                        {{row.status | filterStatus | raw}}
                    </div>
                    <div class="history-detail" slot="c-detail">
                        <template s-if="{{row.type === 'RESIZE'}}">
                            <div class="t-m-ct-wr">
                                <div class="t-m-ct-wr-l">
                                    原配置
                                </div>
                                <div class="t-m-ct-wr-r">
                                    <div class="right-item">
                                        <div class="item-l mr8">{{row.detail.settings | formateDetail}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="t-m-ct-wr">
                                <div class="t-m-ct-wr-l">
                                    新配置
                                </div>
                                <div class="t-m-ct-wr-r">
                                    <div class="right-item">
                                        <div class="item-l mr8">{{row.detail.target_settings | formateDetail}}</div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template s-if="{{row.type === 'UPGRADE'}}">
                            <div class="t-m-ct-wr">
                                <div class="t-m-ct-wr-l">
                                    内核版本：
                                </div>
                                <div class="t-m-ct-wr-r">
                                    <div class="right-item">
                                        <div class="item-l mr8">从 {{row.detail.oldInfo.version}} 升级至 {{row.detail.newInfo.version}}</div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template s-if="{{row.type === 'RESTORE_SNAPSHOT' || row.type === 'BACKUP_SNAPSHOT'}}">
                            <p>文件名称：{{row.detail.snapshotName}}</p>
                            <p>文件大小：{{row.detail.dataSize | filterSize}}</p>
                            <p>表数量：{{row.detail.tableNum}}</p>
                        </template>
                        <template s-if="{{row.type === 'AUDIT_INSTALL'}}">
                            <p>审计日志版本：{{row.detail.auditVersionInfo.version}}</p>
                            <p>doris引擎版本：{{row.detail.engineVersionInfo.version}}</p>
                        </template>
                    </div>
                    <div slot="c-subTasks" class="pg-ct">
                        <div
                            class="pg-ct-it {{index === 0 ? 'first' : ''}} {{index === row.subTasks.length - 1 && item.status === 'FINISH' ? 'last' : ''}}"
                            s-for="item, index in row.subTasks" >
                            <p>{{stageType[item.type]}}: {{item.progress}}%</p>
                            <s-tooltip class="pg-ct-it-tooltip">
                                <div slot="content">{{item | getCurrentStageTime}}</div>
                                <s-progress
                                    percent="{{item.progress}}"
                                    width="{{row.subTasks.length ? 450 / row.subTasks.length : 0}}"
                                    skin="{{item.status | formatProgressSkin}}">
                                    <div slot="info"></div>
                                </s-progress>
                            </s-tooltip>
                        </div>
                    </div>
                </s-table>
                <s-pagination
                    class="mt10"
                    slot="pager"
                    layout="{{'total, pageSize, pager'}}"
                    total="{{pager.totalCount}}"
                    page="{{pager.pageNo}}"
                    page-size="{{pager.pageSize}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange">
                </s-pagination>
            </app-list-page>
        </div>
    `;
    static components = {
        'app-list-page': AppListPage,
        's-icon-refresh': OutlinedRefresh,
        's-table': Table,
        's-button': Button,
        's-progress': Progress,
        's-loading': Loading,
        's-pagination': Pagination,
        's-tooltip': Tooltip,
    };

    initData() {
        return {
            taskType: taskType,
            taskStatus: taskStatus,
            stageType: stageType,
            table: {
                columns: [
                    {
                        name: 'type',
                        label: '操作类型',
                        width: 120,
                        filter: {
                            options: [
                                ...allEnum,
                                ...taskTypeEnum.toArray()
                            ],
                            value: allEnum[0].value
                        },
                        render: item => {return taskTypeEnum.getTextFromValue(item.type);}
                    },
                    {
                        name: 'start_timestamp',
                        label: '开始时间',
                        width: 150,
                        sortable: true,
                        render: item => {
                            return formatTime(item.start_timestamp);
                        }
                    },
                    {
                        name: 'end_timestamp',
                        label: '执行时间',
                        width: 150,
                        render: item => {
                            return computeTimeDistance(item.start_timestamp, item.end_timestamp);
                        }
                    },
                    {
                        name: 'status',
                        label: '状态',
                        width: 150,
                    },
                    {
                        name: 'detail',
                        label: '操作详情',
                        width: 450
                    },
                    {name: 'subTasks', label: '执行进度', fixed: 'right', width: 450}
                ],
                loading: true,
                datasource: [],
            },
            pager: {
                pageSize: 10,
                pageNo: 1,
                totalCount: 0
            }
        };
    }

    static filters = {
        filterStatus(status: string) {
            return renderStatus(taskStatus[status], status);
        },
        formateDetail(obj : any) {
            const res = obj.palobe ? obj.palobe : obj.palofe;
            const {cpu, mem, disk, nodes, disk_type} = res;
            return `${obj.palofe ? 'LeaderNode节点' : 'ComputeNode节点'}： ${cpu}核${mem}G ${getDiskNameFormType(disk_type)}${disk}G 节点数量${nodes}`;
        },
        getCurrentStageTime(currentStage: any) {
            return '运行时间: ' + computeTimeDistance(currentStage?.start_timestamp, currentStage?.end_timestamp);
        },
        formatProgressSkin(status: string) {
            return status === 'FINISH' ? 'success' : status === 'RUNNING' ? 'normal' : 'error';
        },
        filterSize(size: string) {
            return formateSize(Number(size));
        }
    };

    attached() {
        this.getComList();
    }

    onPageChange(target: {value: {page: number; pageSize: number}}) {
        this.data.set('pager.pageNo', target.value.page);
        this.getComList();
    }

    onPageSizeChange(target: {value: {page: number; pageSize: number}}) {
        this.data.set('pager', {pageNo: 1, pageSize: target.value.pageSize});
        this.getComList();
    }

    onFilter(args: {field: {name: string}, filter: {value: string | number}}) {
        this.data.set('pager.pageNo', 1);
        this.data.set(args.field.name, args.filter.value);
        this.getComList();
    }

    onSort(args: {value: {order: number, orderBy: number}}) {
        this.data.set('orderBy', args.value.orderBy);
        this.data.set('order', args.value.order);
        this.data.set('pager.pageNo', 1);
        this.getComList();
    }

    async getComList() {
        const {deployId, pager, orderBy, order, type} = this.data.get('');
        const params = pickEmpty({
            userId: this.$context.getUserId(),
            deployId,
            productType: "palo",
            order,
            orderBy,
            pageNo: pager.pageNo,
            pageSize: pager.pageSize,
            taskType: type
        });
        this.data.set('table.loading', true);
        const res = await this.$http.paloPost('paloTaskList', params);
        this.data.set('table.datasource', res.result);
        this.data.set('pager.totalCount', res?.totalCount);
        this.data.set('table.loading', false);
    }

    refresh() {
        this.getComList();
    }
}