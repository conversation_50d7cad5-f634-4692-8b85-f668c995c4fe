.palo-task-list {
    .s-list-page .table-full-wrap {
        margin: 0;
    }
    .pg-ct {
        display: flex;
        flex-direction: row;
        align-items: center;
    
        &-it {
            display: flex;
            flex-direction: column;
            margin-right: 1px;
    
            &.first {
                .s-progress--line {
                    border-radius: 4px 0 0 4px;
                    flex: 1;
    
                    .s-progress-complete {
                        border-radius: 4px 0 0 4px;
                    }
                }
            }
    
            &.first.last {
                .s-progress--line {
                    border-radius: 4px;
    
                    .s-progress-complete {
                        border-radius: 4px;
                    }
                }
            }
    
            .s-progress--line {
                border-radius: 0;
    
                .s-progress-complete {
                    border-radius: 0;
                }
            }
    
            &.last {
                .s-progress--line {
                    border-radius: 0 4px 4px 0;
    
                    .s-progress-complete {
                        border-radius: 0 4px 4px 0;
                    }
                }
            }
    
            &-tooltip {
                height: 8px;
            }
        }
    
        &-add {
            display: flex;
            flex-direction: column;
            min-height: 40px;
            justify-content: flex-end;
        }
    
        .failed {
            color: #84868C;
        }
    
        .progress-inf {
            .s-button {
                padding: 0;
            }
        }
    
    }
    .t-m-ct-wr {
        display: flex;
        flex-direction: row;
        overflow: visible;
        width: 100%;
        border-bottom: 1px solid #f2f2f4;
        padding: 10px;

        &:last-child {
            border: none;
        }

        &-l {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            width: 80px !important;
        }

        &-r {
            overflow: visible;
            flex: 1;
            width: calc(~'100% - 80px');

            .right-item {
                display: flex;
                padding: 8px 0;
                border-bottom: 1px solid #f2f2f4;

                .item-l {
                    flex: 0 0 auto;
                }

                .ellipsis-tip {
                    display: inline-block;
                    max-width: 100%;
                    min-width: 24px;
                }

                &:last-child {
                    border: none;
                    padding-bottom: 0;
                }
                &:first-child {
                    padding-top: 0;
                }
            }
        }
    }
}