/**
 * 版本升级弹窗
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Radio, Form, Select, Notification} from '@baidu/sui';
import {AppDetailCell, AppLegend} from '@baidu/sui-biz';
import './upgrade-cluster.less';
const kDialogClass = 'upgrade-cluster-version';

export default class extends Component {
    static template = html`
    <div>
        <s-dialog
            class="${kDialogClass} ue-dialog"
            title="版本升级"
            okText="确定"
            open="{{open}}"
            on-confirm="onConfirm"
            on-close="onClose"
            width="700"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}">
            <p class="upgrade-title">基本信息</p>
            <s-detail-cell datasource="{{datasource}}" divide="{{3}}" />
            <p class="upgrade-title">升级方式</p>
            <s-form
                s-ref="form"
                rules="{{rules}}"
                data="{= formData =}">
                <s-form-item label="目标版本：" prop="version">
                    <s-select
                        value="{=formData.version=}"
                        datasource="{{versions}}"
                    ></s-select>
                </s-form-item>
                <s-form-item label="升级方式：" prop="upgradeMode" required>
                    <s-radio-group
                        datasource="{{upgradeModes}}"
                        value="{=formData.upgradeMode=}"
                        enhanced="{{true}}"
                        radioType="button"
                    ></s-radio-group>
                </s-form-item>
            </s-form>
        </s-dialog>
    </div>`;

    static components = {
        's-dialog': Dialog,
        's-select': Select,
        's-form': Form,
        's-form-item': Form.Item,
        's-radio-group': Radio.RadioGroup,
        's-detail-cell': AppDetailCell,
        's-app-legend': AppLegend,
    };

    initData() {
        return {
            open: true,
            formData: {
                version: '',
                upgradeMode: ''
            },
            datasource: [],
            rules: {
                version: [
                    {required: true, message: '请选择目标版本'}
                ]
            },
        };
    }

    attached() {
        const {upgradeModes, deployId, deployName, oldEngineVersion} = this.data.get('');
        this.data.set('formData.upgradeMode', upgradeModes[0].value);
        this.data.set('datasource', [
            {label: '集群ID：', value: deployId},
            {label: '集群名称：', value: deployName},
            {label: '当前版本：', value: oldEngineVersion}
        ]);
    }

    // 确认
    async onConfirm() {
        try {
            await this.ref('form').validateFields();
            this.data.set('confirming', true);
            const params = {
                ...this.data.get('formData'),
                deployId: this.data.get('deployId')
            }
            await this.$http.paloPost('upgradeVersion', params);
            Notification.success('版本升级提交成功');
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
        catch (e) {
            this.data.set('confirming', false);
        }
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}
