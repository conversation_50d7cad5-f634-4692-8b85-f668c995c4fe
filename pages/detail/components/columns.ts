export const hardwareColumns = [
    {
        name: 'moduleDisplayName',
        label: '节点类型',
        width: 100,
        render: function (item: {moduleDisplayName: any}) {
            return item.moduleDisplayName;
        }
    },
    {
        name: 'instanceNum',
        label: '已分配节点数量',
        width: 100,
        render: function (item: {actualInstanceNum: any}) {
            return item.actualInstanceNum;
        }
    },
    {
        name: 'desireInstanceNum',
        label: '请求节点数量',
        width: 100,
        render: function (item: {desireInstanceNum: any}) {
            return item.desireInstanceNum;
        }
    },
    {
        name: 'slotDescription',
        label: '节点配置详情',
        width: 100,
        render: function (item: {slotDescription: any}) {
            return item.slotDescription;
        }
    }
];

export const clusterExampleColumns = [
    {
        name: 'exampleId',
        label: '实例ID',
        width: 100,
        render: function (item: { instanceId: any }) {
            return item.instanceId;
        }
    },
    {
        name: 'hostIp',
        label: '实例IP',
        width: 100,
        render: function (item: { hostIp: any }) {
            return item.hostIp;
        }
    },
    {
        name: 'exampleStatus',
        label: '实例状态',
        width: 100
    },
    {
        name: 'stretchStatus',
        label: '伸缩状态',
        width: 100,
        render: function (item: { displayDecommissionStatus: any }) {
            return item.displayDecommissionStatus;
        }
    },
    {
        name: 'exampleType',
        label: '实例类型',
        width: 100,
        render: function (item: { moduleDisplayName: any }) {
            return item.moduleDisplayName;
        }
    },
    {
        name: 'operate',
        label: '操作',
        width: 100
    }
];

export const protocolColumns = [
    {
        name: 'protocolType',
        label: '类型',
        width: 200,
    },
    {
        name: 'protocolIp',
        label: '访问地址',
    },
];