import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Select, DatePicker, Button, Cascader} from '@baidu/sui';
import IndicatorDialog from './indicator-dialog';
import _ from 'lodash';
import {OutlinedFilter, OutlinedRefresh} from '@baidu/sui-icon';
import './monitor.less';
import {DETAIL_TYPE, INDICATOR_TYPE} from './config';

const ONE_HOUR = 1000 * 60 * 60;
const timeRange = [
    { text: '1小时', value: ONE_HOUR },
    { text: '6小时', value: ONE_HOUR * 6 },
    { text: '1天', value: ONE_HOUR * 24 },
    { text: '7天', value: ONE_HOUR * 24 * 7 },
    { text: '14天', value: ONE_HOUR * 24 * 14 },
    { text: '40天', value: ONE_HOUR * 24 * 40 }
];
export default class FilterPanel extends Component{

    static template = html`
        <div class="palo-monitor-filter-panel">
            <div class="left-content">
                <template s-if="showMonitorObj">
                    <span>监控对象：</span>
                    <s-cascader 
                        s-if="isDecoupledBe" 
                        datasource="{{decoupledDatasource}}"
                        value="{= decoupledValue =}"
                        collapse
                        clearable
                        multiple="{{true}}" 
                        class="mr8" width="200"/>
                    <s-select
                        s-else
                        value="{{selectedList}}"
                        datasource="{{instances}}"
                        multiple
                        clearable
                        checkAll="{{instances.length > 1}}"
                        loading="{{loading}}"
                        width="200"
                        class="mr8"
                        on-change="changeSelect"
                    ></s-select>
                </template>
                性能指标：
                <s-select
                    placeholder="请选择"
                    class="mr8"
                    value="{= indicatorType =}"
                    datasource="{{indicatorTypeSource}}"
                    on-change="changeIndicatorType"
                    width="120"
                >
                </s-select>
                单行展示数：
                <s-select
                    placeholder="请选择"
                    value="{{NumPerLine}}"
                    datasource="{{NumPerLineSource}}"
                    on-change="changeNumPerLine"
                    width="120"
                >
                </s-select>
            </div>
            <div class="right-content">
                <s-button on-click="filterIndicator" skin="enhance">
                    <s-icon-filter class="button-icon mr4" is-button="{{false}}" />
                        指标筛选
                    </s-button>
                <s-button class="ml8 s-icon-button" on-click="refresh">
                    <s-icon-refresh class="button-icon" is-button="{{false}}" />
                </s-button>
            </div>
        </div>
    `;

    static components = {
        's-select': Select,
        's-date-range-picker': DatePicker.DateRangePicker,
        's-button': Button,
        's-icon-filter': OutlinedFilter,
        's-icon-refresh': OutlinedRefresh,
        's-cascader': Cascader,
    }
    static computed = {
        // 是否为存算分离-集群监控
        isDecoupledFe(): boolean {
            const type = this.data.get('type');
            const tab = this.data.get('tab');
            return type === DETAIL_TYPE.decoupled && tab === INDICATOR_TYPE.palofe;
        },
        // 是否为存算分离-计算组监控
        isDecoupledBe(): boolean {
            const type = this.data.get('type');
            const tab = this.data.get('tab');
            return type === DETAIL_TYPE.decoupled && tab === INDICATOR_TYPE.palobe;
        },
        showMonitorObj(): boolean {
            const tab = this.data.get('tab');
            const isDecoupledFe = this.data.get('isDecoupledFe');
            return tab !== INDICATOR_TYPE.other && !isDecoupledFe;
        }
    }

    initData() {
        return {
            shortcutDateRange: [],
            indicatorType: 'average',
            indicatorTypeSource: [
                {
                    text: '平均值',
                    value: 'average'
                },
                {
                    text: '最大值',
                    value: 'maximum'
                },
                {
                    text: '最小值',
                    value: 'minimum'
                }
            ],
            NumPerLine: 2,
            NumPerLineSource: [
                {
                    text: '1',
                    value: 1
                },
                {
                    text: '2',
                    value: 2
                },
                {
                    text: '3',
                    value: 3
                },
            ]
        }
    }

    attached() {
        const now = new Date().getTime();
        const oneHour = 60 * 60 * 1000;
        const end = new Date().getTime() + 60 * 60 * 1000;
        const begin = end - 40 * 24 * 60 * 60 * 1000;
        const date = {
            begin: new Date(now - oneHour),
            end: new Date(now)
        };
        const range = {
            begin: new Date(begin),
            end: new Date(end)
        };
        this.data.set('range', range);
        this.data.set('date', date);
        this.fire('time-change', {data: date});
        this.initShortcutRange();
    }

    filterIndicator() {
        const {tab, type} = this.data.get('');
        const dialog = new IndicatorDialog({
            data: {
                tab,
                type
            }
        });
        dialog.on('success', checkedIndicatorsObj => {
            this.fire('change-indicator', checkedIndicatorsObj);
        });
        dialog.attach(document.body);
        return dialog;
    }

    changeSelect(target: {value: string[]}) {
        this.fire('change-instance', {selected: target.value});
    }

    onTimeChange(target: {value: any}) {
        let begin = new Date(target.value.begin).getTime();
        let end = new Date(target.value.end).getTime();
        if (begin === end) {
            begin = begin - 1000;
        }
        this.fire('time-change', {data: {begin, end}});
    }

    async initShortcutRange() {
        let finalShortcut = timeRange;
        const shortcut = this.data.get('shortcut');

        if (shortcut && Array.isArray(shortcut) && shortcut.length) {
            finalShortcut = shortcut;
        }

        const shortcutDateRange = [];
        _.forEach(finalShortcut, time => {
            shortcutDateRange.push({
                text: time.text,
                onClick: picker => {
                    const date = {
                        begin: new Date(new Date().getTime() - time.value),
                        end: new Date()
                    };
                    picker.setValueByShortCut(date);
                }
            })
        });

        // 太多的话排列不开，所以修改 style
        if (shortcutDateRange.length > 7) {
            this.data.set('isMoreShortcut', true);
        }

        this.data.set('shortcutDateRange', shortcutDateRange);
    }

    changeIndicatorType(target: {value: string}) {
        this.fire('change-indicator-type', {value: target.value});
    }

    changeNumPerLine(target: {value: number}) {
        this.fire('change-num-per-line', {value: target.value});
    }

    refresh() {
        this.fire('refresh', {});
    }
}