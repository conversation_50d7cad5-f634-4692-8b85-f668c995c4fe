import _ from "lodash";

export enum DETAIL_TYPE {
    // 存算一体
    united =  'united',
    // 存算分离
    decoupled = 'decoupled',
}

// Leader Node 监控指标
export enum FE_SECOND_LABLE {
    // 基础监控
    baseMonitor = 'baseMonitor',
    // 查询监控
    queryMonitor = 'queryMonitor',
    // 导入任务监控
    importMonitor = 'importMonitor',
    // 调度与事务数量监控
    scheduleMonitor = 'scheduleMonitor',
    // JVM监控
    jvmMonitor = 'jvmMonitor',
    // 元数据监控
    metaMonitor = 'metaMonitor'
}
export const feIndicator = 
    {
        [FE_SECOND_LABLE.baseMonitor]: {
            label: '基础监控',
            indicators: [
                {
                    "key": "CpuSystemPercent",
                    "label": "CPU使用率",
                    "alias": "cpu",
                    "unit": "%",
                    "value": "CpuSystemPercent",
                    "checked": true,
                },
                {
                    "key": "CpuStolenPercent",
                    "label": "CPU抢占率",
                    "alias": "cpuStl",
                    "unit": "%",
                    "value": "CpuStolenPercent",
                    "checked": false,
                },
                {
                    "key": "MemoryUsedPercent",
                    "label": "内存使用率",
                    "alias": "memPct",
                    "unit": "%",
                    "value": "MemoryUsedPercent",
                    "checked": true,
                },
                {
                    "key": "MemoryUsedGBytes",
                    "label": "内存使用量",
                    "alias": "mem",
                    "unit": "GB",
                    "value": "MemoryUsedGBytes",
                    "checked": false,
                },
                {
                    "key": "DiskUsedPercent",
                    "label": "磁盘空间使用率",
                    "alias": "diskUsedPct",
                    "unit": "%",
                    "value": "DiskUsedPercent",
                    "checked": true,
                },
                {
                    "key": "DiskTotalGBytes",
                    "label": "磁盘空间使用量",
                    "alias": "diskUsed",
                    "unit": "GB",
                    "value": "DiskTotalGBytes",
                    "checked": false,
                },
                {
                    "key": "DiskPartitionMaxUsedPercent",
                    "label": "数据盘最大使用率",
                    "alias": "diskMaxPct",
                    "unit": "%",
                    "value": "DiskPartitionMaxUsedPercent",
                    "checked": false,
                },
                {
                    "key": "ConnectionTotal",
                    "label": "当前连接数",
                    "alias": "connection",
                    "unit": "",
                    "value": "ConnectionTotal",
                    "checked": true,
                },
                {
                    "key": "MaxTabletCompactionScore",
                    "label": "数据分片合并情况",
                    "alias": "tabletCompactionScore",
                    "unit": "",
                    "value": "MaxTabletCompactionScore",
                    "checked": true,
                },
                {
                    "key": "BrokerDeadNum",
                    "label": "Broker异常个数",
                    "alias": "brokerNodeDeadNum",
                    "unit": "",
                    "value": "BrokerDeadNum",
                    "checked": false,
                },
                {
                    "key": "BackendDeadNum",
                    "label": "ComputeNode异常个数",
                    "alias": "computeNodeDeadNum",
                    "unit": "",
                    "value": "BackendDeadNum",
                    "checked": false,
                },
                {
                    "key": "FrontendDeadNum",
                    "label": "LeaderNode异常个数",
                    "alias": "leaderNodeDeadNum",
                    "unit": "",
                    "value": "FrontendDeadNum",
                    "checked": false,
                },
            ]
        },
        [FE_SECOND_LABLE.queryMonitor]: {
            label: '查询监控',
            indicators: [
                {
                    "key": "QueryLatencyMs_0_99",
                    "label": "查询延时99分位数",
                    "alias": "QueryLatencyMs_0_99",
                    "unit": "ms",
                    "value": "QueryLatencyMs_0_99",
                    "checked": false,
                },
                {
                    "key": "RequestTotal",
                    "label": "累计操作请求数",
                    "alias": "RequestTotal",
                    "unit": "Count",
                    "value": "RequestTotal",
                    "checked": false,
                },
                {
                    "key": "QueryErrRate",
                    "label": "每秒查询错误数",
                    "alias": "errRate",
                    "unit": "err/s",
                    "value": "QueryErrRate",
                    "checked": false,
                },
                {
                    "key": "Rps",
                    "label": "每秒请求数",
                    "alias": "rps",
                    "unit": "r/s",
                    "value": "Rps",
                    "checked": false,
                },
                {
                    "key": "MaxJournalId",
                    "label": "当前元数据日志id",
                    "alias": "maxJournalId",
                    "unit": "N/A",
                    "value": "MaxJournalId",
                    "checked": false,
                },
                {
                    "key": "Qps",
                    "label": "每秒查询数",
                    "alias": "qps",
                    "unit": "q/s",
                    "value": "Qps",
                    "checked": false,
                },
            ]
        },
        [FE_SECOND_LABLE.importMonitor]: {
            label: '导入任务监控',
            indicators: [
                {
                    "key": "RoutineLoadRows",
                    "label": "Routine Load 的行数",
                    "alias": "RoutineLoadRows",
                    "unit": "Count/s",
                    "value": "RoutineLoadRows",
                    "checked": false,
                },
                {
                    "key": "RoutineLoadErrorRows",
                    "label": "Routine Load 错误的行数",
                    "alias": "RoutineLoadErrorRows",
                    "unit": "Count/s",
                    "value": "RoutineLoadErrorRows",
                    "checked": false,
                },
                {
                    "key": "JobAlterRunningRollup",
                    "label": "Running状态Rollup任务数",
                    "alias": "JobAlterRunningRollup",
                    "unit": "Count",
                    "value": "JobAlterRunningRollup",
                    "checked": false,
                },
                {
                    "key": "JobAlterRunningSchemaChange",
                    "label": "Running状态SchemaChange任务数",
                    "alias": "JobAlterRunningSchemaChange",
                    "unit": "Count",
                    "value": "JobAlterRunningSchemaChange",
                    "checked": false,
                },
                {
                    "key": "JobLoadPendingSpark",
                    "label": "Pending状态Spark导入任务数",
                    "alias": "JobLoadPendingSpark",
                    "unit": "Count",
                    "value": "JobLoadPendingSpark",
                    "checked": false,
                },
                {
                    "key": "JobLoadLoadingSpark",
                    "label": "Loading状态Spark导入任务数",
                    "alias": "JobLoadLoadingSpark",
                    "unit": "Count",
                    "value": "JobLoadLoadingSpark",
                    "checked": false,
                },
                {
                    "key": "JobLoadPendingInsert",
                    "label": "Pending状态Insert导入任务数",
                    "alias": "JobLoadPendingInsert",
                    "unit": "Count",
                    "value": "JobLoadPendingInsert",
                    "checked": false,
                },
                {
                    "key": "JobLoadLoadingInsert",
                    "label": "Loading状态Insert导入任务数",
                    "alias": "JobLoadLoadingInsert",
                    "unit": "Count",
                    "value": "JobLoadLoadingInsert",
                    "checked": false,
                },
                {
                    "key": "JobLoadFinishedInsert",
                    "label": "Finished状态Insert导入任务数",
                    "alias": "JobLoadFinishedInsert",
                    "unit": "Count",
                    "value": "JobLoadFinishedInsert",
                    "checked": false,
                },
                {
                    "key": "JobLoadCancelledInsert",
                    "label": "Cancelled状态Insert导入任务数",
                    "alias": "JobLoadCancelledInsert",
                    "unit": "Count",
                    "value": "JobLoadCancelledInsert",
                    "checked": false,
                },
                {
                    "key": "JobLoadPendingBroker",
                    "label": "Pending状态Broker导入个数",
                    "alias": "JobLoadPendingBroker",
                    "unit": "Count",
                    "value": "JobLoadPendingBroker",
                    "checked": false,
                },
                {
                    "key": "JobLoadFinishedBroker",
                    "label": "Finished状态Broker导入个数",
                    "alias": "JobLoadFinishedBroker",
                    "unit": "Count",
                    "value": "JobLoadFinishedBroker",
                    "checked": false,
                },
                {
                    "key": "JobLoadLoadingBroker",
                    "label": "Loading状态Broker导入个数",
                    "alias": "JobLoadLoadingBroker",
                    "unit": "Count",
                    "value": "JobLoadLoadingBroker",
                    "checked": false,
                },
                {
                    "key": "JobLoadCancelledBroker",
                    "label": "Cancelled状态Broker导入个数",
                    "alias": "JobLoadCancelledBroker",
                    "unit": "Count",
                    "value": "JobLoadCancelledBroker",
                    "checked": false,
                },
                {
                    "key": "JobLoadNeedScheduleRoutineLoad",
                    "label": "NeedSchedule状态Routine导入个数",
                    "alias": "JobLoadNeedScheduleRoutineLoad",
                    "unit": "Count",
                    "value": "JobLoadNeedScheduleRoutineLoad",
                    "checked": false,
                },
                {
                    "key": "JobLoadRunningRoutineLoad",
                    "label": "Running状态Routine导入个数",
                    "alias": "JobLoadRunningRoutineLoad",
                    "unit": "Count",
                    "value": "JobLoadRunningRoutineLoad",
                    "checked": false,
                },
                {
                    "key": "JobLoadPausedRoutineLoad",
                    "label": "Paused状态Routine导入个数",
                    "alias": "JobLoadPausedRoutineLoad",
                    "unit": "Count",
                    "value": "JobLoadPausedRoutineLoad",
                    "checked": false,
                },
                {
                    "key": "JobLoadStoppedRoutineLoad",
                    "label": "Stopped状态Routine导入个数",
                    "alias": "JobLoadStoppedRoutineLoad",
                    "unit": "Count",
                    "value": "JobLoadStoppedRoutineLoad",
                    "checked": false,
                },
                {
                    "key": "JobLoadCancelledRoutineLoad",
                    "label": "Cancelled状态Routine导入个数",
                    "alias": "JobLoadCancelledRoutineLoad",
                    "unit": "Count",
                    "value": "JobLoadCancelledRoutineLoad",
                    "checked": false,
                },
            ]
        },
        [FE_SECOND_LABLE.scheduleMonitor]: {
            label: '调度与事务数量监控',
            indicators: [
                {
                    "key": "TxnCounterFailed",
                    "label": "失败的事务数量",
                    "alias": "TxnCounterFailed",
                    "unit": "Count/s",
                    "value": "TxnCounterFailed",
                    "checked": false,
                },
                {
                    "key": "TxnCounterReject",
                    "label": "被拒绝的事务数量",
                    "alias": "TxnCounterReject",
                    "unit": "Count/s",
                    "value": "TxnCounterReject",
                    "checked": false
                },
                {
                    "key": "ScheduledTabletNum",
                    "label": "Master节点正在调度的Tablet数量",
                    "alias": "ScheduledTabletNum",
                    "unit": "Count",
                    "value": "ScheduledTabletNum",
                    "checked": false
                },
                {
                    "key": "TxnCounterBegin",
                    "label": "开始的事务数量",
                    "alias": "TxnCounterBegin",
                    "unit": "Count/s",
                    "value": "TxnCounterBegin",
                    "checked": false
                },
                {
                    "key": "TxnCounterSuccess",
                    "label": "成功的事务数量",
                    "alias": "TxnCounterSuccess",
                    "unit": "Count/s",
                    "value": "TxnCounterSuccess",
                    "checked": false
                },
                {
                    "key": "ReportQueueSize",
                    "label": "ComputeNode定期汇报任务在 LeaderNode端的队列长度",
                    "alias": "ReportQueueSize",
                    "unit": "Count",
                    "value": "ReportQueueSize",
                    "checked": false
                },
                {
                    "key": "MaxTabletCompactionScore",
                    "label": "Tablet Compaction 最高分",
                    "alias": "MaxTabletCompactionScore",
                    "unit": "Count",
                    "value": "MaxTabletCompactionScore",
                    "checked": false
                },
                {
                    "key": "TxnStatusUnknown",
                    "label": "处于Unknown状态的事务个数",
                    "alias": "TxnStatusUnknown",
                    "unit": "Count",
                    "value": "TxnStatusUnknown",
                    "checked": false
                },
                {
                    "key": "TxnStatusPrepare",
                    "label": "处于Prepare状态的事务个数",
                    "alias": "TxnStatusPrepare",
                    "unit": "Count",
                    "value": "TxnStatusPrepare",
                    "checked": false
                },
                {
                    "key": "TxnStatusCommitted",
                    "label": "处于Committed状态的事务个数",
                    "alias": "TxnStatusCommitted",
                    "unit": "Count",
                    "value": "TxnStatusCommitted",
                    "checked": false
                },
                {
                    "key": "TxnStatusVisible",
                    "label": "处于Visible状态的事务个数",
                    "alias": "TxnStatusVisible",
                    "unit": "Count",
                    "value": "TxnStatusVisible",
                    "checked": false
                },
                {
                    "key": "TxnStatusAborted",
                    "label": "处于Aborted状态的事务个数",
                    "alias": "TxnStatusAborted",
                    "unit": "Count",
                    "value": "TxnStatusAborted",
                    "checked": false
                },
                {
                    "key": "TxnStatusPrecommitted",
                    "label": "处于Precommitted状态的事务个数",
                    "alias": "TxnStatusPrecommitted",
                    "unit": "Count",
                    "value": "TxnStatusPrecommitted",
                    "checked": false
                }
            ]
        },
        [FE_SECOND_LABLE.jvmMonitor]: {
            label: 'JVM监控',
            indicators: [
                {
                    "key": "JvmOldUsedPercent",
                    "label": "JVM 老年代使用内存率",
                    "alias": "jvmOldUsage",
                    "unit": "%",
                    "value": "JvmOldUsedPercent",
                    "checked": false
                },
                {
                    "key": "JvmYoungUsedPercent",
                    "label": "JVM 新年代使用内存率",
                    "alias": "jvmYoungUsage",
                    "unit": "%",
                    "value": "JvmYoungUsedPercent",
                    "checked": false
                },
                {
                    "key": "JvmThread",
                    "label": "JVM 总线程数",
                    "alias": "JvmThread",
                    "unit": "Count",
                    "value": "JvmThread",
                    "checked": false
                },
                {
                    "key": "JvmOldGcCount",
                    "label": "JVM Old GC 次数",
                    "alias": "JvmOldGcCount",
                    "unit": "Count",
                    "value": "JvmOldGcCount",
                    "checked": false
                },
                {
                    "key": "JvmOldGcTime",
                    "label": "Old GC 时间",
                    "alias": "JvmOldGcTime",
                    "unit": "ms",
                    "value": "JvmOldGcTime",
                    "checked": false
                },
                {
                    "key": "JvmOldGcRate",
                    "label": "Old GC 单次平均耗时",
                    "alias": "JvmOldGcRate",
                    "unit": "ms",
                    "value": "JvmOldGcRate",
                    "checked": false
                },
                {
                    "key": "JvmYoungGcCount",
                    "label": "Young GC 次数",
                    "alias": "JvmYoungGcCount",
                    "unit": "Count",
                    "value": "JvmYoungGcCount",
                    "checked": false
                },
                {
                    "key": "JvmYoungGcTime",
                    "label": "Young GC 时间",
                    "alias": "JvmYoungGcTime",
                    "unit": "ms",
                    "value": "JvmYoungGcTime",
                    "checked": false
                },
                {
                    "key": "JvmYoungGcRate",
                    "label": "Young GC 单次平均耗时",
                    "alias": "JvmYoungGcRate",
                    "unit": "ms",
                    "value": "JvmYoungGcRate",
                    "checked": false
                },
                {
                    "key": "JvmYoungSizeBytesPeakUsed",
                    "label": "JVM 新年代使用峰值",
                    "alias": "JvmYoungSizeBytesPeakUsed",
                    "unit": "Byte",
                    "value": "JvmYoungSizeBytesPeakUsed",
                    "checked": false
                },
                {
                    "key": "JvmYoungSizeBytesMax",
                    "label": "JVM 新年代内存最大值",
                    "alias": "JvmYoungSizeBytesMax",
                    "unit": "Byte",
                    "value": "JvmYoungSizeBytesMax",
                    "checked": false
                },
                {
                    "key": "JvmHeapSizeBytesMax",
                    "label": "最大值 JVM 内存",
                    "alias": "JvmHeapSizeBytesMax",
                    "unit": "Byte",
                    "value": "JvmHeapSizeBytesMax",
                    "checked": false
                },
                {
                    "key": "JvmHeapSizeBytesCommitted",
                    "label": "已申请 JVM 内存",
                    "alias": "JvmHeapSizeBytesCommitted",
                    "unit": "Byte",
                    "value": "JvmHeapSizeBytesCommitted",
                    "checked": false
                },
                {
                    "key": "JvmHeapSizeBytesUsed",
                    "label": "已使用 JVM 内存",
                    "alias": "JvmHeapSizeBytesUsed",
                    "unit": "Byte",
                    "value": "JvmHeapSizeBytesUsed",
                    "checked": true
                },
                {
                    "key": "JvmNonHeapSizeBytesCommitted",
                    "label": "已申请 JVM 堆外内存",
                    "alias": "JvmNonHeapSizeBytesCommitted",
                    "unit": "Byte",
                    "value": "JvmNonHeapSizeBytesCommitted",
                    "checked": false
                },
                {
                    "key": "JvmNonHeapSizeBytesUsed",
                    "label": "已使用 JVM 堆外内存",
                    "alias": "JvmNonHeapSizeBytesUsed",
                    "unit": "Byte",
                    "value": "JvmNonHeapSizeBytesUsed",
                    "checked": true
                },
                {
                    "key": "JvmOldSizeBytesPeakUsed",
                    "label": "JVM 老年代使用峰值",
                    "alias": "JvmOldSizeBytesPeakUsed",
                    "unit": "Byte",
                    "value": "JvmOldSizeBytesPeakUsed",
                    "checked": false
                },
                {
                    "key": "JvmOldSizeBytesMax",
                    "label": "JVM 老年代内存最大值",
                    "alias": "JvmOldSizeBytesMax",
                    "unit": "Byte",
                    "value": "JvmOldSizeBytesMax",
                    "checked": false
                },
            ]
        },
        [FE_SECOND_LABLE.metaMonitor]: {
            label: '元数据监控',
            indicators: [
                {
                    "key": "EditLogWrite",
                    "label": "元数据日志写入次数",
                    "alias": "EditLogWrite",
                    "unit": "Count/s",
                    "value": "EditLogWrite",
                    "checked": true
                },
                {
                    "key": "EditLogRead",
                    "label": "元数据日志读取次数",
                    "alias": "EditLogRead",
                    "unit": "Count/s",
                    "value": "EditLogRead",
                    "checked": false
                },
                {
                    "key": "EditLogCurrentBytes",
                    "label": "元数据日志当前大小",
                    "alias": "EditLogCurrentBytes",
                    "unit": "Byte/s",
                    "value": "EditLogCurrentBytes",
                    "checked": false
                },
                {
                    "key": "EditlogWriteLatencyMs_0_99",
                    "label": "元数据日志写入延迟的99分位统计",
                    "alias": "EditlogWriteLatencyMs_0_99",
                    "unit": "ms",
                    "value": "EditlogWriteLatencyMs_0_99",
                    "checked": true
                },
                {
                    "key": "EditLogCleanSuccess",
                    "label": "清理历史元数据日志成功次数",
                    "alias": "EditLogCleanSuccess",
                    "unit": "Count",
                    "value": "EditLogCleanSuccess",
                    "checked": false
                },
                {
                    "key": "EditLogCleanFailed",
                    "label": "清理历史元数据日志失败次数",
                    "alias": "EditLogCleanFailed",
                    "unit": "Count",
                    "value": "EditLogCleanFailed",
                    "checked": false
                },
                {
                    "key": "ImageCleanSuccess",
                    "label": "清理历史元数据镜像文件成功次数",
                    "alias": "ImageCleanSuccess",
                    "unit": "Count",
                    "value": "ImageCleanSuccess",
                    "checked": false
                },
                {
                    "key": "ImageCleanFailed",
                    "label": "清理历史元数据镜像文件失败次数",
                    "alias": "ImageCleanFailed",
                    "unit": "Count",
                    "value": "ImageCleanFailed",
                    "checked": false
                },
                {
                    "key": "ImagePushSuccess",
                    "label": "将元数据镜像文件推送给其他FE节点的失败次数",
                    "alias": "ImagePushSuccess",
                    "unit": "Count",
                    "value": "ImagePushSuccess",
                    "checked": false
                },
                {
                    "key": "ImagePushFailed",
                    "label": "将元数据镜像文件推送给其他FE节点的成功次数",
                    "alias": "ImagePushFailed",
                    "unit": "Count",
                    "value": "ImagePushFailed",
                    "checked": false
                },
                {
                    "key": "ImageWriteSuccess",
                    "label": "生成元数据镜像文件成功次数",
                    "alias": "ImageWriteSuccess",
                    "unit": "Count",
                    "value": "ImageWriteSuccess",
                    "checked": false
                },
                {
                    "key": "ImageWriteFailed",
                    "label": "生成元数据镜像文件失败次数",
                    "alias": "ImageWriteFailed",
                    "unit": "Count",
                    "value": "ImageWriteFailed",
                    "checked": false
                },
            ]
        }
}

export const feDecoupledIndicator = {
    [FE_SECOND_LABLE.baseMonitor]: {
        label: '基础监控',
        indicators: [
            {
                "key": "ConnectionTotal",
                "label": "当前连接数",
                "alias": "ConnectionTotal",
                "unit": "",
                "value": "ConnectionTotal",
                "checked": true
            },
            {
                "key": "MaxTabletCompactionScore",
                "label": "数据分片合并情况",
                "alias": "MaxTabletCompactionScore",
                "unit": "",
                "value": "MaxTabletCompactionScore",
                "checked": true
            },
            {
                "key": "BrokerDeadNum",
                "label": "Broker异常个数",
                "alias": "BrokerDeadNum",
                "unit": "",
                "value": "BrokerDeadNum",
                "checked": false
            },
            {
                "key": "BackendDeadNum",
                "label": "ComputeNode异常个数",
                "alias": "BackendDeadNum",
                "unit": "",
                "value": "BackendDeadNum",
                "checked": false
            },
            {
                "key": "FrontendDeadNum",
                "label": "LeaderNode异常个数",
                "alias": "FrontendDeadNum",
                "unit": "",
                "value": "FrontendDeadNum",
                "checked": false
            },
            {
                "key": "S3FileReaderTotal",
                "label": "远程存储读次数（QPS）",
                "alias": "S3FileReaderTotal",
                "unit": "Count/s",
                "value": "S3FileReaderTotal",
                "checked": false
            },
            {
                "key": "S3FileWriterTotal",
                "label": "远程存储写次数（QPS）",
                "alias": "S3FileWriterTotal",
                "unit": "Count/s",
                "value": "S3FileWriterTotal",
                "checked": false
            },
            {
                "key": "CacheHitRatio",
                "label": "缓存命中率",
                "alias": "CacheHitRatio",
                "unit": "%",
                "value": "CacheHitRatio",
                "checked": false
            },
            {
                "key": "TableDataSizeTotal",
                "label": "对象存储容量",
                "alias": "TableDataSizeTotal",
                "unit": "Byte",
                "value": "TableDataSizeTotal",
                "checked": false
            },
        ]
    },
    [FE_SECOND_LABLE.queryMonitor]: {
        label: '查询监控',
        indicators: [
            {
                "key": "QuerySuccRate",
                "label": "查询成功率",
                "alias": "QuerySuccRate",
                "unit": "%",
                "value": "QuerySuccRate",
                "checked": false
            },
            {
                "key": "AverageQueryLatency",
                "label": "查询平均耗时",
                "alias": "AverageQueryLatency",
                "unit": "ms",
                "value": "AverageQueryLatency",
                "checked": false
            },
            {
                "key": "QueryLatencyMs_0_99",
                "label": "查询延时99分位数",
                "alias": "QueryLatencyMs_0_99",
                "unit": "ms",
                "value": "QueryLatencyMs_0_99",
                "checked": false
            },
            {
                "key": "RequestTotal",
                "label": "累计操作请求数",
                "alias": "RequestTotal",
                "unit": "Count",
                "value": "RequestTotal",
                "checked": false
            },
            {
                "key": "QueryErrRate",
                "label": "每秒查询错误数",
                "alias": "QueryErrRate",
                "unit": "Count/s",
                "value": "QueryErrRate",
                "checked": false
            },
            {
                "key": "Rps",
                "label": "每秒请求数",
                "alias": "Rps",
                "unit": "Count/s",
                "value": "Rps",
                "checked": false
            },
            {
                "key": "MaxJournalId",
                "label": "当前元数据日志id",
                "alias": "MaxJournalId",
                "unit": "",
                "value": "MaxJournalId",
                "checked": false
            },
            {
                "key": "Qps",
                "label": "每秒查询数",
                "alias": "Qps",
                "unit": "Count/s",
                "value": "Qps",
                "checked": false
            },
        ]
    },
    [FE_SECOND_LABLE.importMonitor]: {
        label: '导入任务监控',
        indicators: [
            {
                "key": "RoutineLoadRows",
                "label": "Routine Load 的行数",
                "alias": "RoutineLoadRows",
                "unit": "Count/s",
                "value": "RoutineLoadRows",
                "checked": false
            },
            {
                "key": "RoutineLoadErrorRows",
                "label": "Routine Load 错误的行数",
                "alias": "RoutineLoadErrorRows",
                "unit": "Count/s",
                "value": "RoutineLoadErrorRows",
                "checked": false
            },
            {
                "key": "JobAlterRunningRollup",
                "label": "Running状态Rollup任务数",
                "alias": "JobAlterRunningRollup",
                "unit": "Count",
                "value": "JobAlterRunningRollup",
                "checked": false
            },
            {
                "key": "JobAlterRunningSchemaChange",
                "label": "Running状态SchemaChange任务数",
                "alias": "JobAlterRunningSchemaChange",
                "unit": "Count",
                "value": "JobAlterRunningSchemaChange",
                "checked": false
            },
            {
                "key": "JobLoadPendingSpark",
                "label": "Pending状态Spark导入任务数",
                "alias": "JobLoadPendingSpark",
                "unit": "Count",
                "value": "JobLoadPendingSpark",
                "checked": false
            },
            {
                "key": "JobLoadPendingSpark",
                "label": "Pending状态Spark导入任务数",
                "alias": "JobLoadPendingSpark",
                "unit": "Count",
                "value": "JobLoadPendingSpark",
                "checked": false
            },
            {
                "key": "JobLoadLoadingSpark",
                "label": "Loading状态Spark导入任务数",
                "alias": "JobLoadLoadingSpark",
                "unit": "Count",
                "value": "JobLoadLoadingSpark",
                "checked": false,
            },
            {
                "key": "JobLoadPendingInsert",
                "label": "Pending状态Insert导入任务数",
                "alias": "JobLoadPendingInsert",
                "unit": "Count",
                "value": "JobLoadPendingInsert",
                "checked": false,
            },
            {
                "key": "JobLoadLoadingInsert",
                "label": "Loading状态Insert导入任务数",
                "alias": "JobLoadLoadingInsert",
                "unit": "Count",
                "value": "JobLoadLoadingInsert",
                "checked": false,
            },
            {
                "key": "JobLoadFinishedInsert",
                "label": "Finished状态Insert导入任务数",
                "alias": "JobLoadFinishedInsert",
                "unit": "Count",
                "value": "JobLoadFinishedInsert",
                "checked": false,
            },
            {
                "key": "JobLoadCancelledInsert",
                "label": "Cancelled状态Insert导入任务数",
                "alias": "JobLoadCancelledInsert",
                "unit": "Count",
                "value": "JobLoadCancelledInsert",
                "checked": false,
            },
            {
                "key": "JobLoadPendingBroker",
                "label": "Pending状态Broker导入个数",
                "alias": "JobLoadPendingBroker",
                "unit": "Count",
                "value": "JobLoadPendingBroker",
                "checked": false,
            },
            {
                "key": "JobLoadFinishedBroker",
                "label": "Finished状态Broker导入个数",
                "alias": "JobLoadFinishedBroker",
                "unit": "Count",
                "value": "JobLoadFinishedBroker",
                "checked": false,
            },
            {
                "key": "JobLoadLoadingBroker",
                "label": "Loading状态Broker导入个数",
                "alias": "JobLoadLoadingBroker",
                "unit": "Count",
                "value": "JobLoadLoadingBroker",
                "checked": false,
            },
            {
                "key": "JobLoadCancelledBroker",
                "label": "Cancelled状态Broker导入个数",
                "alias": "JobLoadCancelledBroker",
                "unit": "Count",
                "value": "JobLoadCancelledBroker",
                "checked": false,
            },
            {
                "key": "JobLoadNeedScheduleRoutineLoad",
                "label": "NeedSchedule状态Routine导入个数",
                "alias": "JobLoadNeedScheduleRoutineLoad",
                "unit": "Count",
                "value": "JobLoadNeedScheduleRoutineLoad",
                "checked": false,
            },
            {
                "key": "JobLoadRunningRoutineLoad",
                "label": "Running状态Routine导入个数",
                "alias": "JobLoadRunningRoutineLoad",
                "unit": "Count",
                "value": "JobLoadRunningRoutineLoad",
                "checked": false,
            },
            {
                "key": "JobLoadPausedRoutineLoad",
                "label": "Paused状态Routine导入个数",
                "alias": "JobLoadPausedRoutineLoad",
                "unit": "Count",
                "value": "JobLoadPausedRoutineLoad",
                "checked": false,
            },
            {
                "key": "JobLoadStoppedRoutineLoad",
                "label": "Stopped状态Routine导入个数",
                "alias": "JobLoadStoppedRoutineLoad",
                "unit": "Count",
                "value": "JobLoadStoppedRoutineLoad",
                "checked": false,
            },
            {
                "key": "JobLoadCancelledRoutineLoad",
                "label": "Cancelled状态Routine导入个数",
                "alias": "JobLoadCancelledRoutineLoad",
                "unit": "Count",
                "value": "JobLoadCancelledRoutineLoad",
                "checked": false,
            },
        ]
    },
    [FE_SECOND_LABLE.scheduleMonitor]: {
        label: '调度与事务数量监控',
        indicators: [
            {
                "key": "TxnCounterFailed",
                "label": "失败的事务数量",
                "alias": "TxnCounterFailed",
                "unit": "Count/s",
                "value": "TxnCounterFailed",
                "checked": false
            },
            {
                "key": "TxnCounterReject",
                "label": "被拒绝的事务数量",
                "alias": "TxnCounterReject",
                "unit": "Count/s",
                "value": "TxnCounterReject",
                "checked": false
            },
            {
                "key": "ScheduledTabletNum",
                "label": "Master节点正在调度的Tablet数量",
                "alias": "ScheduledTabletNum",
                "unit": "Count",
                "value": "ScheduledTabletNum",
                "checked": false
            },
            {
                "key": "TxnCounterBegin",
                "label": "开始的事务数量",
                "alias": "TxnCounterBegin",
                "unit": "Count/s",
                "value": "TxnCounterBegin",
                "checked": false
            },
            {
                "key": "TxnCounterSuccess",
                "label": "成功的事务数量",
                "alias": "TxnCounterSuccess",
                "unit": "Count/s",
                "value": "TxnCounterSuccess",
                "checked": false
            },
            {
                "key": "ReportQueueSize",
                "label": "ComputeNode定期汇报任务在 LeaderNode端的队列长度",
                "alias": "ReportQueueSize",
                "unit": "Count",
                "value": "ReportQueueSize",
                "checked": false
            },
            {
                "key": "MaxTabletCompactionScore",
                "label": "Tablet Compaction 最高分",
                "alias": "MaxTabletCompactionScore",
                "unit": "Count",
                "value": "MaxTabletCompactionScore",
                "checked": false
            },
            {
                "key": "TxnStatusUnknown",
                "label": "处于Unknown状态的事务个数",
                "alias": "TxnStatusUnknown",
                "unit": "Count",
                "value": "TxnStatusUnknown",
                "checked": false
            },
            {
                "key": "TxnStatusPrepare",
                "label": "处于Prepare状态的事务个数",
                "alias": "TxnStatusPrepare",
                "unit": "Count",
                "value": "TxnStatusPrepare",
                "checked": false
            },
            {
                "key": "TxnStatusCommitted",
                "label": "处于Committed状态的事务个数",
                "alias": "TxnStatusCommitted",
                "unit": "Count",
                "value": "TxnStatusCommitted",
                "checked": false
            },
            {
                "key": "TxnStatusVisible",
                "label": "处于Visible状态的事务个数",
                "alias": "TxnStatusVisible",
                "unit": "Count",
                "value": "TxnStatusVisible",
                "checked": false
            },
            {
                "key": "TxnStatusAborted",
                "label": "处于Aborted状态的事务个数",
                "alias": "TxnStatusAborted",
                "unit": "Count",
                "value": "TxnStatusAborted",
                "checked": false
            },
            {
                "key": "TxnStatusPrecommitted",
                "label": "处于Precommitted状态的事务个数",
                "alias": "TxnStatusPrecommitted",
                "unit": "Count",
                "value": "TxnStatusPrecommitted",
                "checked": false
            }
        ]
    },
    [FE_SECOND_LABLE.metaMonitor]: {
        label: '元数据监控',
        indicators: [
            {
                "key": "EditLogWrite",
                "label": "元数据日志写入次数",
                "alias": "EditLogWrite",
                "unit": "Count/s",
                "value": "EditLogWrite",
                "checked": true
            },
            {
                "key": "EditLogRead",
                "label": "元数据日志读取次数",
                "alias": "EditLogRead",
                "unit": "Count/s",
                "value": "EditLogRead",
                "checked": false
            },
            {
                "key": "EditLogCurrentBytes",
                "label": "元数据日志当前大小",
                "alias": "EditLogCurrentBytes",
                "unit": "Byte/s",
                "value": "EditLogCurrentBytes",
                "checked": false
            },
            {
                "key": "EditlogWriteLatencyMs_0_99",
                "label": "元数据日志写入延迟的99分位统计",
                "alias": "EditlogWriteLatencyMs_0_99",
                "unit": "ms",
                "value": "EditlogWriteLatencyMs_0_99",
                "checked": true
            },
            {
                "key": "EditLogCleanSuccess",
                "label": "清理历史元数据日志成功次数",
                "alias": "EditLogCleanSuccess",
                "unit": "Count",
                "value": "EditLogCleanSuccess",
                "checked": false
            },
            {
                "key": "EditLogCleanFailed",
                "label": "清理历史元数据日志失败次数",
                "alias": "EditLogCleanFailed",
                "unit": "Count",
                "value": "EditLogCleanFailed",
                "checked": false
            },
            {
                "key": "ImageCleanSuccess",
                "label": "清理历史元数据镜像文件成功次数",
                "alias": "ImageCleanSuccess",
                "unit": "Count",
                "value": "ImageCleanSuccess",
                "checked": false
            },
            {
                "key": "ImageCleanFailed",
                "label": "清理历史元数据镜像文件失败次数",
                "alias": "ImageCleanFailed",
                "unit": "Count",
                "value": "ImageCleanFailed",
                "checked": false
            },
            {
                "key": "ImagePushSuccess",
                "label": "将元数据镜像文件推送给其他FE节点的失败次数",
                "alias": "ImagePushSuccess",
                "unit": "Count",
                "value": "ImagePushSuccess",
                "checked": false
            },
            {
                "key": "ImagePushFailed",
                "label": "将元数据镜像文件推送给其他FE节点的成功次数",
                "alias": "ImagePushFailed",
                "unit": "Count",
                "value": "ImagePushFailed",
                "checked": false
            },
            {
                "key": "ImageWriteSuccess",
                "label": "生成元数据镜像文件成功次数",
                "alias": "ImageWriteSuccess",
                "unit": "Count",
                "value": "ImageWriteSuccess",
                "checked": false
            },
            {
                "key": "ImageWriteFailed",
                "label": "生成元数据镜像文件失败次数",
                "alias": "ImageWriteFailed",
                "unit": "Count",
                "value": "ImageWriteFailed",
                "checked": false
            },
        ]
    }
}
// Compute Node 监控指标
export enum BE_SECOND_LABLE {
    // 基础监控
    baseMonitor = 'baseMonitor',
    // 数据导入及写入
    dataMonitor = 'dataMonitor',
    // 查询监控
    queryMonitor = 'queryMonitor',
    // 任务信息监控
    infoMonitor = 'infoMonitor',
    // 元数据读写监控
    metaMonitor = 'metaMonitor'
}
export const beIndicator = {
    [BE_SECOND_LABLE.baseMonitor]: {
        label: '基础监控',
        key: BE_SECOND_LABLE.baseMonitor,
        indicators: [
            {
                "key": "CpuSystemPercent",
                "label": "CPU使用率",
                "alias": "cpu",
                "unit": "%",
                "value": "CpuSystemPercent",
                "checked": true
            },
            {
                "key": "CpuStolenPercent",
                "label": "CPU抢占率",
                "alias": "cpuStl",
                "unit": "%",
                "value": "CpuStolenPercent",
                "checked": false
            },
            {
                "key": "MemoryUsedPercent",
                "label": "内存使用率",
                "alias": "memPct",
                "unit": "%",
                "value": "MemoryUsedPercent",
                "checked": true
            },
            {
                "key": "MemoryUsedGBytes",
                "label": "内存使用量",
                "alias": "mem",
                "unit": "GB",
                "value": "MemoryUsedGBytes",
                "checked": false
            },
            {
                "key": "DiskUsedPercent",
                "label": "磁盘空间使用率",
                "alias": "diskUsedPct",
                "unit": "%",
                "value": "DiskUsedPercent",
                "checked": true
            },
            {
                "key": "DiskTotalGBytes",
                "label": "磁盘空间使用量",
                "alias": "diskUsed",
                "unit": "GB",
                "value": "DiskTotalGBytes",
                "checked": false
            },
            {
                "key": "MaxDiskIoUtilPercent",
                "label": "磁盘IO利用率",
                "alias": "maxDiskIoUtilPercent",
                "unit": "%",
                "value": "MaxDiskIoUtilPercent",
                "checked": true
            },
            {
                "key": "DiskPartitionMaxUsedPercent",
                "label": "数据盘最大使用率",
                "alias": "diskMaxPct",
                "unit": "%",
                "value": "DiskPartitionMaxUsedPercent",
                "checked": false
            },
            {
                "key": "MemoryAllocatedBytes",
                "label": "Memory Allocated 大小",
                "alias": "MemoryAllocatedBytes",
                "unit": "Byte",
                "value": "MemoryAllocatedBytes",
                "checked": true
            },
            {
                "key": "ProcessFdNumLimitSoft",
                "label": "进程文件句柄 Soft 限制数量",
                "alias": "ProcessFdNumLimitSoft",
                "unit": "Count",
                "value": "ProcessFdNumLimitSoft",
                "checked": false
            },
            {
                "key": "TabletNum",
                "label": "Tablet 总数",
                "alias": "TabletNum",
                "unit": "Count",
                "value": "TabletNum",
                "checked": false
            },
            {
                "key": "DisksAvailCapacity",
                "label": "磁盘的剩余空间",
                "alias": "DisksAvailCapacity",
                "unit": "Byte",
                "value": "DisksAvailCapacity",
                "checked": false
            },
            {
                "key": "MaxNetworkReceiveBytesRate",
                "label": "网络最大接收速率",
                "alias": "maxNetworkReceiveBytesRate",
                "unit": "Bytes/s",
                "value": "MaxNetworkReceiveBytesRate",
                "checked": false
            },
            {
                "key": "MaxNetworkSendBytesRate",
                "label": "网络最大发送速率",
                "alias": "maxNetworkSendBytesRate",
                "unit": "Bytes/s",
                "value": "MaxNetworkSendBytesRate",
                "checked": false
            },
            {
                "key": "ProcessFdNumUsed",
                "label": "进程使用文件句柄数量",
                "alias": "fdNum",
                "unit": "Count",
                "value": "ProcessFdNumUsed",
                "checked": false
            },
            {
                "key": "ProcessThreadNum",
                "label": "进程运行的线程个数",
                "alias": "threadNum",
                "unit": "Count",
                "value": "ProcessThreadNum",
                "checked": false
            },
            {
                "key": "TabletSchemaCacheMemoryBytes",
                "label": "TabletSchema内存缓存使用量",
                "alias": "tabletSchemaCacheMemoryBytes",
                "unit": "Byte",
                "value": "TabletSchemaCacheMemoryBytes",
                "checked": false
            },
            {
                "key": "LruCacheMemoryBytes",
                "label": "LRU内存缓存大小使用量",
                "alias": "lruCacheMemoryBytes",
                "unit": "Byte",
                "value": "LruCacheMemoryBytes",
                "checked": false
            },
            {
                "key": "S3BytesReadTotal",
                "label": "S3FileReader 读取字节数累计值",
                "alias": "S3BytesReadTotal",
                "unit": "Byte/s",
                "value": "S3BytesReadTotal",
                "checked": false
            },
            {
                "key": "UploadTotalByte",
                "label": "上传到远端存储成功的Rowset数据量累计值",
                "alias": "UploadTotalByte",
                "unit": "Byte/s",
                "value": "UploadTotalByte",
                "checked": false
            },
            {
                "key": "UploadRowsetCount",
                "label": "上传到远端存储成功的Rowset的次数累计值",
                "alias": "UploadRowsetCount",
                "unit": "Count/s",
                "value": "UploadRowsetCount",
                "checked": false
            },
            {
                "key": "UploadFailCount",
                "label": "上传到远端存储失败的Rowset的次数累计值",
                "alias": "UploadFailCount",
                "unit": "Count",
                "value": "UploadFailCount",
                "checked": false
            },
            {
                "key": "NetworkSendBytes",
                "label": "各个网卡的发送字节累计值",
                "alias": "NetworkSendBytes",
                "unit": "Byte/s",
                "value": "NetworkSendBytes",
                "checked": false
            },
            {
                "key": "NetworkReceiveBytes",
                "label": "各个网卡的接收字节累计值",
                "alias": "NetworkReceiveBytes",
                "unit": "Byte/s",
                "value": "NetworkReceiveBytes",
                "checked": false
            }
        ]
    },
    [BE_SECOND_LABLE.dataMonitor]: {
        label: '数据导入及写入',
        indicators: [
            {
                "key": "StreamLoadReceiveBytes",
                "label": "Stream Load 接收的字节数累计值",
                "alias": "StreamLoadReceiveBytes",
                "unit": "Byte/s",
                "value": "StreamLoadReceiveBytes",
                "checked": false
            },
            {
                "key": "StreamLoadLoadRows",
                "label": "Stream Load 最终导入的行数累计值",
                "alias": "StreamLoadLoadRows",
                "unit": "Row/s",
                "value": "StreamLoadLoadRows",
                "checked": false
            },
            {
                "key": "PushRequestWriteBytes",
                "label": "Push请求写入字节累计值",
                "alias": "PushRequestWriteBytes",
                "unit": "Row/s",
                "value": "PushRequestWriteBytes",
                "checked": false
            },
            {
                "key": "PushRequestWriteRows",
                "label": "Push请求写入行数累计值",
                "alias": "PushRequestWriteRows",
                "unit": "Count/s",
                "value": "PushRequestWriteRows",
                "checked": false
            },
            {
                "key": "PushRequestsTotalSuccess",
                "label": "Push请求成功次数累计值",
                "alias": "PushRequestsTotalSuccess",
                "unit": "Count/s",
                "value": "PushRequestsTotalSuccess",
                "checked": false
            },
            {
                "key": "PushRequestsTotalFail",
                "label": "Push请求失败次数累计值",
                "alias": "PushRequestsTotalFail",
                "unit": "Count",
                "value": "PushRequestsTotalFail",
                "checked": false
            },
            {
                "key": "PushRequestDurationUs",
                "label": "Push请求耗时累计",
                "alias": "PushRequestDurationUs",
                "unit": "us",
                "value": "PushRequestDurationUs",
                "checked": false
            }
        ]
    },
    [BE_SECOND_LABLE.queryMonitor]: {
        label: '查询监控',
        indicators: [
            {
                "key": "QueryScanRows",
                "label": "读取行数的数量",
                "alias": "QueryScanRows",
                "unit": "Count/s",
                "value": "QueryScanRows",
                "checked": true
            },
            {
                "key": "QueryCacheMemoryTotalByte",
                "label": "Query Cache 占用字节数",
                "alias": "QueryCacheMemoryTotalByte",
                "unit": "Byte",
                "value": "QueryCacheMemoryTotalByte",
                "checked": false
            },
            {
                "key": "QueryScanBytesPerSecond",
                "label": "读取数据速率",
                "alias": "scanRate",
                "unit": "Bytes/s",
                "value": "QueryScanBytesPerSecond",
                "checked": false
            },
            {
                "key": "QueryCachePartitionTotalCount",
                "label": "当前 Partition Cache 缓存个数",
                "alias": "QueryCachePartitionTotalCount",
                "unit": "Count",
                "value": "QueryCachePartitionTotalCount",
                "checked": false
            },
            {
                "key": "QueryCacheSqlTotalCount",
                "label": "当前 SQL Cache 缓存个数",
                "alias": "QueryCacheSqlTotalCount",
                "unit": "Count",
                "value": "QueryCacheSqlTotalCount",
                "checked": false
            },
        ]
    },
    [BE_SECOND_LABLE.infoMonitor]: {
        label: '任务信息监控',
        indicators: [
            {
                "key": "EngineRequestsTotalTotalPublish",
                "label": "Publish任务总累计值",
                "alias": "EngineRequestsTotalTotalPublish",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalPublish",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedPublish",
                "label": "Publish任务失败累计值",
                "alias": "EngineRequestsTotalFailedPublish",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedPublish",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalReportAllTablets",
                "label": "ReportAllTablets任务总累计值",
                "alias": "EngineRequestsTotalTotalReportAllTablets",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalReportAllTablets",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedReportAllTablets",
                "label": "ReportAllTablets任务失败累计值",
                "alias": "EngineRequestsTotalFailedReportAllTablets",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedReportAllTablets",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalReportTablet",
                "label": "ReportTablet任务总累计值",
                "alias": "EngineRequestsTotalTotalReportTablet",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalReportTablet",
                "checked": false
            },
            {
                "label": "ReportTablet任务失败累计值",
                "alias": "EngineRequestsTotalFailedReportTablet",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedReportTablet",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalFinishTask",
                "label": "FinishTask任务总累计值",
                "alias": "EngineRequestsTotalTotalFinishTask",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalFinishTask",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedFinishTask",
                "label": "FinishTask任务失败累计值",
                "alias": "EngineRequestsTotalFailedFinishTask",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedFinishTask",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalDelete",
                "label": "Delete任务总累计值",
                "alias": "EngineRequestsTotalTotalDelete",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalDelete",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedDelete",
                "label": "Delete任务失败累计值",
                "alias": "EngineRequestsTotalFailedDelete",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedDelete",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalBaseCompaction",
                "label": "BaseCompaction任务总累计值",
                "alias": "EngineRequestsTotalTotalBaseCompaction",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalBaseCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedBaseCompaction",
                "label": "BaseCompaction任务失败累计值",
                "alias": "EngineRequestsTotalFailedBaseCompaction",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedBaseCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalCumulativeCompaction",
                "label": "CumulativeCompaction任务总累计值",
                "alias": "EngineRequestsTotalTotalCumulativeCompaction",
                "unit": "Count",
                "value": "EngineRequestsTotalTotalCumulativeCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedCumulativeCompaction",
                "label": "CumulativeCompaction任务失败累计值",
                "alias": "EngineRequestsTotalFailedCumulativeCompaction",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedCumulativeCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalClone",
                "label": "Clone任务总累计值",
                "alias": "EngineRequestsTotalTotalClone",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalClone",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedClone",
                "label": "Clone任务失败累计值",
                "alias": "EngineRequestsTotalFailedClone",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedClone",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalCreateRollup",
                "label": "CreateRollup任务总累计值",
                "alias": "EngineRequestsTotalTotalCreateRollup",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalCreateRollup",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedCreateRollup",
                "label": "CreateRollup任务失败累计值",
                "alias": "EngineRequestsTotalFailedCreateRollup",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedCreateRollup",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalSchemaChange",
                "label": "SchemaChange任务总累计值",
                "alias": "EngineRequestsTotalTotalSchemaChange",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalSchemaChange",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedSchemaChange",
                "label": "SchemaChange任务失败累计值",
                "alias": "EngineRequestsTotalFailedSchemaChange",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedSchemaChange",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalCreateTablet",
                "label": "CreateTablet任务总累计值",
                "alias": "EngineRequestsTotalTotalCreateTablet",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalCreateTablet",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedCreateTablet",
                "label": "CreateTablet任务失败累计值",
                "alias": "EngineRequestsTotalFailedCreateTablet",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedCreateTablet",
                "checked": false
            },
            {
                "key": "CompactionBytesTotalBase",
                "label": "Base compaction 的数据量",
                "alias": "CompactionBytesTotalBase",
                "unit": "Byte/s",
                "value": "CompactionBytesTotalBase",
                "checked": false
            },
            {
                "key": "CompactionBytesTotalCumulative",
                "label": "Cumulative compaction 的数据量",
                "alias": "CompactionBytesTotalCumulative",
                "unit": "Byte/s",
                "value": "CompactionBytesTotalCumulative",
                "checked": false
            },
            {
                "key": "TabletMaxCompactionScore",
                "label": "Tablet最大Compaction Score ",
                "alias": "TabletMaxCompactionScore",
                "unit": "Count",
                "value": "TabletMaxCompactionScore",
                "checked": false
            },
        ]
    },
    [BE_SECOND_LABLE.metaMonitor]: {
        label: '元数据读写监控',
        indicators: [
            {
                "key": "MetaRequestTotalRead",
                "label": "元数据读请求次数累计值",
                "alias": "MetaRequestTotalRead",
                "unit": "Count/s",
                "value": "MetaRequestTotalRead",
                "checked": false
            },
            {
                "key": "MetaRequestTotalWrite",
                "label": "元数据写请求次数累计值",
                "alias": "MetaRequestTotalWrite",
                "unit": "Count/s",
                "value": "MetaRequestTotalWrite",
                "checked": false
            },
            {
                "key": "MetaRequestDurationRead",
                "label": "元数据读请求耗时累计",
                "alias": "MetaRequestDurationRead",
                "unit": "us",
                "value": "MetaRequestDurationRead",
                "checked": false
            },
            {
                "key": "MetaRequestDurationWrite",
                "label": "元数据写请求耗时累计",
                "alias": "MetaRequestDurationWrite",
                "unit": "us",
                "value": "MetaRequestDurationWrite",
                "checked": false
            },
            {
                "key": "MetaReadRequestRate",
                "label": "元数据单次读请求平均耗时",
                "alias": "MetaReadRequestRate",
                "unit": "us",
                "value": "MetaReadRequestRate",
                "checked": false
            },
            {
                "key": "MetaWriteRequestRate",
                "label": "元数据单次写请求平均耗时",
                "alias": "MetaWriteRequestRate",
                "unit": "us",
                "value": "MetaWriteRequestRate",
                "checked": false
            }
        ]
    }
};
export const beDecoupledIndicator = {
    [BE_SECOND_LABLE.baseMonitor]: {
        label: '基础监控',
        indicators: [
            {
                "key": "CpuSystemPercent",
                "label": "CPU使用率",
                "alias": "cpu",
                "unit": "%",
                "value": "CpuSystemPercent",
                "checked": true
            },
            {
                "key": "CpuStolenPercent",
                "label": "CPU抢占率",
                "alias": "cpuStl",
                "unit": "%",
                "value": "CpuStolenPercent",
                "checked": false
            },
            {
                "key": "MemoryUsedPercent",
                "label": "内存使用率",
                "alias": "memPct",
                "unit": "%",
                "value": "MemoryUsedPercent",
                "checked": true
            },
            {
                "key": "MemoryUsedGBytes",
                "label": "内存使用量",
                "alias": "mem",
                "unit": "GB",
                "value": "MemoryUsedGBytes",
                "checked": false
            },
            {
                "key": "DiskUsedPercent",
                "label": "磁盘空间使用率",
                "alias": "diskUsedPct",
                "unit": "%",
                "value": "DiskUsedPercent",
                "checked": true
            },
            {
                "key": "DiskTotalGBytes",
                "label": "磁盘空间使用量",
                "alias": "diskUsed",
                "unit": "GB",
                "value": "DiskTotalGBytes",
                "checked": false
            },
            {
                "key": "MaxDiskIoUtilPercent",
                "label": "磁盘IO利用率",
                "alias": "maxDiskIoUtilPercent",
                "unit": "%",
                "value": "MaxDiskIoUtilPercent",
                "checked": true
            },
            {
                "key": "DiskPartitionMaxUsedPercent",
                "label": "数据盘最大使用率",
                "alias": "diskMaxPct",
                "unit": "%",
                "value": "DiskPartitionMaxUsedPercent",
                "checked": false
            },
            {
                "key": "MemoryAllocatedBytes",
                "label": "Memory Allocated 大小",
                "alias": "MemoryAllocatedBytes",
                "unit": "Byte",
                "value": "MemoryAllocatedBytes",
                "checked": true
            },
            {
                "key": "ProcessFdNumLimitSoft",
                "label": "进程文件句柄 Soft 限制数量",
                "alias": "ProcessFdNumLimitSoft",
                "unit": "Count",
                "value": "ProcessFdNumLimitSoft",
                "checked": false
            },
            // {
            //     "key": "TabletNum",
            //     "label": "Tablet 总数",
            //     "alias": "TabletNum",
            //     "unit": "Count",
            //     "value": "TabletNum",
            //     "checked": false
            // },
            {
                "key": "DisksAvailCapacity",
                "label": "磁盘的剩余空间",
                "alias": "DisksAvailCapacity",
                "unit": "Byte",
                "value": "DisksAvailCapacity",
                "checked": false
            },
            {
                "key": "MaxNetworkReceiveBytesRate",
                "label": "网络最大接收速率",
                "alias": "maxNetworkReceiveBytesRate",
                "unit": "Bytes/s",
                "value": "MaxNetworkReceiveBytesRate",
                "checked": false
            },
            {
                "key": "MaxNetworkSendBytesRate",
                "label": "网络最大发送速率",
                "alias": "maxNetworkSendBytesRate",
                "unit": "Bytes/s",
                "value": "MaxNetworkSendBytesRate",
                "checked": false
            },
            {
                "key": "NetworkSendBytes",
                "label": "各个网卡的发送字节累计值",
                "alias": "NetworkSendBytes",
                "unit": "Byte/s",
                "value": "NetworkSendBytes",
                "checked": false
            },
            {
                "key": "NetworkReceiveBytes",
                "label": "各个网卡的接收字节累计值",
                "alias": "NetworkReceiveBytes",
                "unit": "Byte/s",
                "value": "NetworkReceiveBytes",
                "checked": false
            },
            {
                "key": "ProcessFdNumUsed",
                "label": "进程使用文件句柄数量",
                "alias": "fdNum",
                "unit": "Count",
                "value": "ProcessFdNumUsed",
                "checked": false
            },
            {
                "key": "ProcessThreadNum",
                "label": "进程运行的线程个数",
                "alias": "threadNum",
                "unit": "Count",
                "value": "ProcessThreadNum",
                "checked": false
            },
            // {
            //     "key": "TabletSchemaCacheMemoryBytes",
            //     "label": "TabletSchema内存缓存使用量",
            //     "alias": "tabletSchemaCacheMemoryBytes",
            //     "unit": "Byte",
            //     "value": "TabletSchemaCacheMemoryBytes",
            //     "checked": false
            // },
            // {
            //     "key": "LruCacheMemoryBytes",
            //     "label": "LRU内存缓存大小使用量",
            //     "alias": "lruCacheMemoryBytes",
            //     "unit": "Byte",
            //     "value": "LruCacheMemoryBytes",
            //     "checked": false
            // },
            {
                "key": "S3BytesReadTotal",
                "label": "S3FileReader 读取字节数累计值",
                "alias": "S3BytesReadTotal",
                "unit": "Byte/s",
                "value": "S3BytesReadTotal",
                "checked": false
            },
            {
                "key": "UploadTotalByte",
                "label": "上传到远端存储成功的Rowset数据量累计值",
                "alias": "UploadTotalByte",
                "unit": "Byte/s",
                "value": "UploadTotalByte",
                "checked": false
            },
            {
                "key": "UploadRowsetCount",
                "label": "上传到远端存储成功的Rowset的次数累计值",
                "alias": "UploadRowsetCount",
                "unit": "Count/s",
                "value": "UploadRowsetCount",
                "checked": false
            },
            {
                "key": "UploadFailCount",
                "label": "上传到远端存储失败的Rowset的次数累计值",
                "alias": "UploadFailCount",
                "unit": "Count",
                "value": "UploadFailCount",
                "checked": false
            },
        ]
    },
    [BE_SECOND_LABLE.dataMonitor]: {
        label: '数据导入及写入',
        indicators: [
            {
                "key": "LoadRowsPerSecond",
                "label": "导入速度",
                "alias": "LoadRowsPerSecond",
                "unit": "Row/s",
                "value": "LoadRowsPerSecond",
                "checked": false
            },
            {
                "key": "LoadBytesPerSecond",
                "label": "导入数据量",
                "alias": "LoadBytesPerSecond",
                "unit": "Byte/s",
                "value": "LoadBytesPerSecond",
                "checked": false
            },
            {
                "key": "StreamLoadReceiveBytes",
                "label": "Stream Load 接收的字节数累计值",
                "alias": "StreamLoadReceiveBytes",
                "unit": "Byte/s",
                "value": "StreamLoadReceiveBytes",
                "checked": false
            },
            {
                "key": "StreamLoadLoadRows",
                "label": "Stream Load 最终导入的行数累计值",
                "alias": "StreamLoadLoadRows",
                "unit": "Row/s",
                "value": "StreamLoadLoadRows",
                "checked": false
            },
            {
                "key": "StreamingLoadRequestsPerSecond",
                "label": "Stream Load 作业导入频率",
                "alias": "StreamingLoadRequestsPerSecond",
                "unit": "Row/s",
                "value": "StreamingLoadRequestsPerSecond",
                "checked": false
            },
            // {
            //     "key": "PushRequestWriteBytes",
            //     "label": "Push请求写入字节累计值",
            //     "alias": "PushRequestWriteBytes",
            //     "unit": "Row/s",
            //     "value": "PushRequestWriteBytes",
            //     "checked": false
            // },
            {
                "key": "PushRequestWriteRows",
                "label": "Push请求写入行数累计值",
                "alias": "PushRequestWriteRows",
                "unit": "Count/s",
                "value": "PushRequestWriteRows",
                "checked": false
            },
            {
                "key": "PushRequestsTotalSuccess",
                "label": "Push请求成功次数累计值",
                "alias": "PushRequestsTotalSuccess",
                "unit": "Count/s",
                "value": "PushRequestsTotalSuccess",
                "checked": false
            },
            {
                "key": "PushRequestsTotalFail",
                "label": "Push请求失败次数累计值",
                "alias": "PushRequestsTotalFail",
                "unit": "Count",
                "value": "PushRequestsTotalFail",
                "checked": false
            },
            {
                "key": "PushRequestDurationUs",
                "label": "Push请求耗时累计",
                "alias": "PushRequestDurationUs",
                "unit": "us",
                "value": "PushRequestDurationUs",
                "checked": false
            }
        ]
    }
    ,
    [BE_SECOND_LABLE.queryMonitor]: {
        label: '查询监控',
        indicators: [
            {
                "key": "QueryScanRows",
                "label": "读取行数的数量",
                "alias": "QueryScanRows",
                "unit": "Count/s",
                "value": "QueryScanRows",
                "checked": true
            },
            {
                "key": "QueryCacheMemoryTotalByte",
                "label": "Query Cache 占用字节数",
                "alias": "QueryCacheMemoryTotalByte",
                "unit": "Byte",
                "value": "QueryCacheMemoryTotalByte",
                "checked": false
            },
            {
                "key": "QueryScanBytesPerSecond",
                "label": "读取数据速率",
                "alias": "scanRate",
                "unit": "Bytes/s",
                "value": "QueryScanBytesPerSecond",
                "checked": false
            },
            {
                "key": "QueryCachePartitionTotalCount",
                "label": "当前 Partition Cache 缓存个数",
                "alias": "QueryCachePartitionTotalCount",
                "unit": "Count",
                "value": "QueryCachePartitionTotalCount",
                "checked": false
            },
            {
                "key": "QueryCacheSqlTotalCount",
                "label": "当前 SQL Cache 缓存个数",
                "alias": "QueryCacheSqlTotalCount",
                "unit": "Count",
                "value": "QueryCacheSqlTotalCount",
                "checked": false
            },
        ]
    },
    [BE_SECOND_LABLE.infoMonitor]: {
        label: '任务信息监控',
        indicators: [
            {
                "key": "EngineRequestsTotalTotalPublish",
                "label": "Publish任务总累计值",
                "alias": "EngineRequestsTotalTotalPublish",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalPublish",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedPublish",
                "label": "Publish任务失败累计值",
                "alias": "EngineRequestsTotalFailedPublish",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedPublish",
                "checked": false
            },
            // {
            //     "key": "EngineRequestsTotalTotalReportAllTablets",
            //     "label": "ReportAllTablets任务总累计值",
            //     "alias": "EngineRequestsTotalTotalReportAllTablets",
            //     "unit": "Count/s",
            //     "value": "EngineRequestsTotalTotalReportAllTablets",
            //     "checked": false
            // },
            // {
            //     "key": "EngineRequestsTotalFailedReportAllTablets",
            //     "label": "ReportAllTablets任务失败累计值",
            //     "alias": "EngineRequestsTotalFailedReportAllTablets",
            //     "unit": "Count",
            //     "value": "EngineRequestsTotalFailedReportAllTablets",
            //     "checked": false
            // },
            // {
            //     "key": "EngineRequestsTotalTotalReportTablet",
            //     "label": "ReportTablet任务总累计值",
            //     "alias": "EngineRequestsTotalTotalReportTablet",
            //     "unit": "Count/s",
            //     "value": "EngineRequestsTotalTotalReportTablet",
            //     "checked": false
            // },
            // {
            //     "label": "ReportTablet任务失败累计值",
            //     "alias": "EngineRequestsTotalFailedReportTablet",
            //     "unit": "Count",
            //     "value": "EngineRequestsTotalFailedReportTablet",
            //     "checked": false
            // },
            {
                "key": "EngineRequestsTotalTotalFinishTask",
                "label": "FinishTask任务总累计值",
                "alias": "EngineRequestsTotalTotalFinishTask",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalFinishTask",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedFinishTask",
                "label": "FinishTask任务失败累计值",
                "alias": "EngineRequestsTotalFailedFinishTask",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedFinishTask",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalDelete",
                "label": "Delete任务总累计值",
                "alias": "EngineRequestsTotalTotalDelete",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalDelete",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedDelete",
                "label": "Delete任务失败累计值",
                "alias": "EngineRequestsTotalFailedDelete",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedDelete",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalBaseCompaction",
                "label": "BaseCompaction任务总累计值",
                "alias": "EngineRequestsTotalTotalBaseCompaction",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalBaseCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedBaseCompaction",
                "label": "BaseCompaction任务失败累计值",
                "alias": "EngineRequestsTotalFailedBaseCompaction",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedBaseCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalCumulativeCompaction",
                "label": "CumulativeCompaction任务总累计值",
                "alias": "EngineRequestsTotalTotalCumulativeCompaction",
                "unit": "Count",
                "value": "EngineRequestsTotalTotalCumulativeCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedCumulativeCompaction",
                "label": "CumulativeCompaction任务失败累计值",
                "alias": "EngineRequestsTotalFailedCumulativeCompaction",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedCumulativeCompaction",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalClone",
                "label": "Clone任务总累计值",
                "alias": "EngineRequestsTotalTotalClone",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalClone",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedClone",
                "label": "Clone任务失败累计值",
                "alias": "EngineRequestsTotalFailedClone",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedClone",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalCreateRollup",
                "label": "CreateRollup任务总累计值",
                "alias": "EngineRequestsTotalTotalCreateRollup",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalCreateRollup",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedCreateRollup",
                "label": "CreateRollup任务失败累计值",
                "alias": "EngineRequestsTotalFailedCreateRollup",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedCreateRollup",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalSchemaChange",
                "label": "SchemaChange任务总累计值",
                "alias": "EngineRequestsTotalTotalSchemaChange",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalSchemaChange",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedSchemaChange",
                "label": "SchemaChange任务失败累计值",
                "alias": "EngineRequestsTotalFailedSchemaChange",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedSchemaChange",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalTotalCreateTablet",
                "label": "CreateTablet任务总累计值",
                "alias": "EngineRequestsTotalTotalCreateTablet",
                "unit": "Count/s",
                "value": "EngineRequestsTotalTotalCreateTablet",
                "checked": false
            },
            {
                "key": "EngineRequestsTotalFailedCreateTablet",
                "label": "CreateTablet任务失败累计值",
                "alias": "EngineRequestsTotalFailedCreateTablet",
                "unit": "Count",
                "value": "EngineRequestsTotalFailedCreateTablet",
                "checked": false
            },
            {
                "key": "CompactionBytesTotalBase",
                "label": "Base compaction 的数据量",
                "alias": "CompactionBytesTotalBase",
                "unit": "Byte/s",
                "value": "CompactionBytesTotalBase",
                "checked": false
            },
            {
                "key": "CompactionBytesTotalCumulative",
                "label": "Cumulative compaction 的数据量",
                "alias": "CompactionBytesTotalCumulative",
                "unit": "Byte/s",
                "value": "CompactionBytesTotalCumulative",
                "checked": false
            },
            // {
            //     "key": "TabletMaxCompactionScore",
            //     "label": "Tablet最大Compaction Score ",
            //     "alias": "TabletMaxCompactionScore",
            //     "unit": "Count",
            //     "value": "TabletMaxCompactionScore",
            //     "checked": false
            // },
        ]
    },
    // [BE_SECOND_LABLE.metaMonitor]: {
    //     label: '元数据读写监控',
    //     indicators: [
            // {
            //     "key": "MetaRequestTotalRead",
            //     "label": "元数据读请求次数累计值",
            //     "alias": "MetaRequestTotalRead",
            //     "unit": "Count/s",
            //     "value": "MetaRequestTotalRead",
            //     "checked": false
            // },
            // {
            //     "key": "MetaRequestTotalWrite",
            //     "label": "元数据写请求次数累计值",
            //     "alias": "MetaRequestTotalWrite",
            //     "unit": "Count/s",
            //     "value": "MetaRequestTotalWrite",
            //     "checked": false
            // },
            // {
            //     "key": "MetaRequestDurationRead",
            //     "label": "元数据读请求耗时累计",
            //     "alias": "MetaRequestDurationRead",
            //     "unit": "us",
            //     "value": "MetaRequestDurationRead",
            //     "checked": false
            // },
            // {
            //     "key": "MetaRequestDurationWrite",
            //     "label": "元数据写请求耗时累计",
            //     "alias": "MetaRequestDurationWrite",
            //     "unit": "us",
            //     "value": "MetaRequestDurationWrite",
            //     "checked": false
            // },
            // {
            //     "key": "MetaReadRequestRate",
            //     "label": "元数据单次读请求平均耗时",
            //     "alias": "MetaReadRequestRate",
            //     "unit": "us",
            //     "value": "MetaReadRequestRate",
            //     "checked": false
            // },
            // {
            //     "key": "MetaWriteRequestRate",
            //     "label": "元数据单次写请求平均耗时",
            //     "alias": "MetaWriteRequestRate",
            //     "unit": "us",
            //     "value": "MetaWriteRequestRate",
            //     "checked": false
            // }
        // ]
    // }
}
export const SlowQueryRecordsCount = {
    "key": "SlowQueryRecordsCount",
    "label": "慢查询数",
    "alias": "SlowQueryRecordsCount",
    "unit": "个",
    "value": "SlowQueryRecordsCount",
    "checked": true
};
export const UnhealthyTabletCount = {
    "key": "UnhealthyTabletCount",
    "label": "异常tablet数",
    "alias": "UnhealthyTabletCount",
    "unit": "个",
    "value": "UnhealthyTabletCount",
    "checked": true
};
export const SingleReplicaTableCount = {
    "key": "SingleReplicaTableCount",
    "label": "单副本表",
    "alias": "SingleReplicaTableCount",
    "unit": "个",
    "value": "SingleReplicaTableCount",
    "checked": true
};
export enum OP_SECOND_LABLE {
    indexMonitor = 'indexMonitor',
}
export const opIndicator = {
    [OP_SECOND_LABLE.indexMonitor]: {
        label: '业务指标监控',
        // 版本控制指标列表
        indicators: []
    }
}

const getLabelDatasource = datasource => {
    return _.map(datasource, (value, key) => ({
        label: value?.label,
        value: key,
        alias: value?.alias
    }))
}
export enum INDICATOR_TYPE {
    palofe = 'palofe',
    palobe = 'palobe',
    other = 'other'
}
// 集群监控页面配置
export const monitorMap = {
    [DETAIL_TYPE.united]: {
        tabSource: [
            {label: 'Leader Node', key: INDICATOR_TYPE.palofe},
            {label: 'Compute Node', key: INDICATOR_TYPE.palobe},
            {label: '业务监控', key: INDICATOR_TYPE.other}
        ],
        indicatorMap: {
            [INDICATOR_TYPE.palofe] : feIndicator,
            [INDICATOR_TYPE.palobe]: beIndicator,
            [INDICATOR_TYPE.other]: opIndicator
        },
        feLabelDatasource: getLabelDatasource(feIndicator),
        beLabelDatasource: getLabelDatasource(beIndicator),
        opLabelDatasource: getLabelDatasource(opIndicator)
    },
    [DETAIL_TYPE.decoupled]: {
        tabSource: [
            {label: '集群监控', key: INDICATOR_TYPE.palofe},
            {label: '计算组监控', key: INDICATOR_TYPE.palobe},
            {label: '业务监控', key: INDICATOR_TYPE.other}
        ],
        indicatorMap: {
            [INDICATOR_TYPE.palofe]: feDecoupledIndicator,
            [INDICATOR_TYPE.palobe]: beDecoupledIndicator,
            [INDICATOR_TYPE.other]: opIndicator
        },
        feLabelDatasource: getLabelDatasource(feDecoupledIndicator),
        beLabelDatasource: getLabelDatasource(beDecoupledIndicator),
        opLabelDatasource: getLabelDatasource(opIndicator)
    }
}
