import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Radio, Checkbox, Search} from '@baidu/sui';

import {monitorMap} from './config';

import './monitor.less';

const klass = 'monitor-filter';

export default class IndicatorDialog extends Component{
    static template = html`
    <template>
        <s-dialog
            title="指标筛选"
            open="{{open}}"
            mask="{{true}}"
            class="${klass}"
            height="500"
            width="1000"
            widthStrictly
            on-close="onClose">
            <div class="filter-container">
                <s-radio-group 
                    enhanced 
                    radioType="button" 
                    value="{= filterTab =}" 
                    on-change="handleChangeTab"
                    datasource="{{labelDatasource}}"/>
                <!--<s-search value="{=filterValue=}" placeholder="{{placeholder}}" on-search="handleSearch"/>-->
            </div>
            <s-checkbox
                label="全选"
                class="mt16"
                checked="{=checkAll.value=}"
                indeterminate="{{checkAll.indeterminate}}"
                on-change="onAllSelectChange"
            />
            <div class="checkbox-area">
                <div s-for="item in checkDatasource" class="checkbox-item">
                    <s-checkbox 
                        checked="{{item.checked}}" 
                        disabled="{{item.disabled}}" 
                        value="{{item}}" 
                        on-change="onCheckBoxChange($event, item.value)">
                        {{item.label}}
                    </s-checkbox>
                    <p class="alias">{{item.value}}</p>
                </div>
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button on-click="onConfirm" skin="primary">确定</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-radio-group': Radio.RadioGroup,
        's-search': Search,
    };

    static computed = {
        labelDatasource(): Array<object> {
            const tab = this.data.get('tab');
            const currentPageConfig = this.data.get('currentPageConfig');
            const datasourceMap: {[key: string]: Array<object>} = {
                'palofe': currentPageConfig.feLabelDatasource,
                'palobe': currentPageConfig.beLabelDatasource,
                'other': currentPageConfig.opLabelDatasource
            }
            return datasourceMap[tab as string];
        },
        currentPageConfig(): object {
            const type = this.data.get('type') as keyof typeof monitorMap;
            return monitorMap[type];
        },
        indicatorMap(): object {
            const currentPageConfig = this.data.get('currentPageConfig');
            return currentPageConfig.indicatorMap;
        }
    };

    initData() {
        return {
            open: true,
            placeholder: '请输入实例名称搜索',

            filterTab: '',
            filterValue: '',
            // 当前tab的所有二级分类及对应指标 确认时更新 (深拷贝自config) - 存储所有选择结果
            indicatorObject: {},
            // 当前二级分类下的指标列表（浅拷贝自indicatorObject)
            checkDatasource: [],
            checkAll: {
                value: false,
                indeterminate: false,
            }
        }
    }

    attached() {
        const {tab, labelDatasource, indicatorMap} = this.data.get('');
        const filterTab = labelDatasource[0]?.value;
        this.data.set('filterTab', filterTab);

        let indicatorObject = {};
        indicatorObject = _.cloneDeep(indicatorMap[tab])
        this.data.set('indicatorObject', indicatorObject);
        this.handleChangeTab({value: filterTab});
    }

    // 确认
    async onConfirm() {
        const indicatorObject = this.data.get('indicatorObject');
        this.fire('success', indicatorObject);
        this.onClose();
    }

    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    // 切换tab - 暂存当前tab选中项
    handleChangeTab(e: {value: string}){
        const {indicatorObject} = this.data.get('');
        this.data.set('checkDatasource', indicatorObject[e.value].indicators);
        this.handleJudgeSelectAll();
    }
    // 判断全选选择框状态
    handleJudgeSelectAll() {
        const checkDatasource = this.data.get('checkDatasource');
        this.data.set('checkAll', {
            value: _.every(checkDatasource, item => item.checked),
            indeterminate: _.some(checkDatasource, item => item.checked) && !_.every(checkDatasource, item => item.checked),
        });
    }
    // 全选操作
    onAllSelectChange(target: {value: string}) {
        const {indicatorObject, filterTab} = this.data.get('');
        indicatorObject[filterTab].indicators = _.map(indicatorObject[filterTab].indicators, (item :object) => ({
            ...item,
            checked: target.value
        }));
        this.handleChangeTab({value: filterTab});
    }
    // 多选框操作 
    onCheckBoxChange(e: {value: boolean}, value: string) {
        const {checkDatasource} = this.data.get('');
        const index = _.findIndex(checkDatasource, item => item.value === value);
        checkDatasource[index].checked = e.value;
        this.handleJudgeSelectAll();
    }
}