// 集群监控页
import _ from 'lodash';
import {Component, DataTypes} from 'san';
import {html} from '@baiducloud/runtime';
import {Tabs, Loading} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import WarnStatus from './warn-status';
import FilterPanel from './filter-panel';
import ChartPanel from './chart-panel';
import MonitorTree from './tour-tree';
import TimePanel from './time-panel';
import {DETAIL_TYPE, monitorMap, SlowQueryRecordsCount, UnhealthyTabletCount, SingleReplicaTableCount, INDICATOR_TYPE} from './config';
import m from 'moment';
import {formatUtcTime, compareVersions} from '@common/utils';

export let otherIndicator = [
    SlowQueryRecordsCount, UnhealthyTabletCount, SingleReplicaTableCount
];
export default class Monitor extends Component {
    static dataTypes = {
        deployId: DataTypes.string,
        detail: DataTypes.object,
        isLoading: DataTypes.bool,
        // 详情页类型 united - 存算一体 decoupled - 存算分离
        type: DETAIL_TYPE
    };
    static template = html`
        <div class="cluster-monitor">
            <s-tabs
                active="{{active}}"
                on-change="changeActive"
            >
                <s-tabpane
                    s-for="i in currentPageConfig.tabSource"
                    label="{{i.label}}"
                    key="{{i.key}}"
                >
                </s-tabpane>
                <div class="monitor-content">
                   <time-panel
                        on-time-change="changeTime"
                   />
                    <warn-status
                        deployId="{{deployId}}"
                        tab="{{active}}"
                        type="{{type}}"
                        dimensions="{{dimensions}}"
                        firstId="{{selectedList[0] || instances[0].value}}"
                    ></warn-status>
                    <filter-panel
                        instances="{{instances}}"
                        selectedList="{{selectedList}}"
                        tab="{{active}}"
                        type="{{type}}"
                        decoupledDatasource="{{decoupledDatasource}}"
                        decoupledValue="{= decoupledValue =}"
                        isLowVersion="{{isLowVersion}}"
                        loading="{{isLoading}}"
                        on-change-instance="changeInstance"
                        on-change-indicator="handleIndicatorFilter"
                        on-change-indicator-type="changeIndicatorType"
                        on-change-num-per-line="changeNumPerLine"
                        on-refresh="refresh"
                    ></filter-panel>
                    <s-loading loading="{{indicatorLoading}}" style="width: 100%;">
                        <div class="chart-container mt8">
                            <monitor-tree 
                                s-ref="monitor-tree"
                                class="mr16"
                                indicatorObj="{{checkedIndicatorsObj}}" 
                                indicatorList="{{indicatorList}}"
                                activeKey="{{activeKey}}"
                                on-select="handleSelectNode"
                            />
                            <chart-panel
                                dimensions="{{dimensions}}"
                                indicatorList="{{indicatorList}}"
                                deployId="{{deployId}}"
                                loading="{{isLoading}}"
                                time="{{time}}"
                                tab="{{active}}"
                                type="{{type}}"
                                hasNotInstallAuditPlugin="{{hasNotInstallAuditPlugin}}"
                                statistics="{{statistics}}"
                                numPerLine="{{numPerLine}}"
                                on-scroll="handleScrollMonitor"
                                s-ref="chart-panel"
                            ></chart-panel>
                        </div>
                    </s-loading>
                </div>
            </s-tabs>
        </div>
    `;

    static components = {
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-loading': Loading,
        'sui-icon': OutlinedRefresh,
        'warn-status': WarnStatus,
        'filter-panel': FilterPanel,
        'chart-panel': ChartPanel,
        'monitor-tree': MonitorTree,
        'time-panel': TimePanel
    };
    static computed = {
        currentPageConfig(): object {
            const type = this.data.get('type') as keyof typeof monitorMap;
            return monitorMap[type];
        },
        indicatorMap(): object {
            const currentPageConfig = this.data.get('currentPageConfig');
            return currentPageConfig.indicatorMap;
        },
        // 是否为存算分离-计算组监控
        isDecoupledBe(): boolean {
            const type = this.data.get('type');
            const active = this.data.get('active');
            return type === DETAIL_TYPE.decoupled && active === INDICATOR_TYPE.palobe;
        },
        dimensions(): Array<string> {
            const selectedList = this.data.get('selectedList');
            const cg_selecteds = this.data.get('decoupledValue');
            const deployId = this.data.get('deployId');
            const masterInstanceId = this.data.get('masterInstanceId');
            const productRegion = this.data.get('productRegion');
            const type = this.data.get('type');
            const tab = this.data.get('active');
            if (type === DETAIL_TYPE.united) {
                switch (tab) {
                    case INDICATOR_TYPE.other:
                        return selectedList.map((selectItem: string) => `DeployId:${deployId};ProductName:palo;ProductRegion:${productRegion}`);
                    case INDICATOR_TYPE.palofe:
                    case INDICATOR_TYPE.palobe:
                        return selectedList.map((selectItem: string) => `DeployId:${deployId};InstanceId:${selectItem};ProductName:palo;ProductRegion:${productRegion}`);
                }
            } else if (type === DETAIL_TYPE.decoupled) {
                switch (tab) {
                    case INDICATOR_TYPE.palofe:
                    case INDICATOR_TYPE.other:
                        return [`DeployId:${deployId};ProductName:palo;ProductRegion:${productRegion}`];
                    case INDICATOR_TYPE.palobe:
                        return cg_selecteds.map((selectItem: Array<string>) => `ComputeGroupId:${selectItem[0]};InstanceId:${selectItem[1]};ProductName:palo;ProductRegion:${productRegion}`);
                }
            }
            return [];
        }
    };

    initData() {
        return {
            isLoading: true,
            active: INDICATOR_TYPE.palofe,
            selectedList: [],
            indicatorList: [],
            instances: [],
            time: {},
            checkedIndicatorsObj: {},
            activeKey: '',
            ignoreScroll: false,
            scrollTimer: null,
            // 计算组数据
            decoupledDatasource: [],
            // 计算组级联选择框数据
            decoupledValue: [],
            // 存算分离总Id
            masterInstanceId: '',
            productRegion: window?.$context.getCurrentRegion().id,
            statistics: 'average',
            numPerLine: 2,
            indicatorLoading: true,
            hasNotInstallAuditPlugin: ''
        };
    }

    async attached() {
        const {active} = this.data.get('');
        
        !this.data.get('isLoading') && this.changeActive({value: {key: active}});
        this.watch('isLoading', val => !val && this.changeActive({ value: { key: active } }));
        
        const {type, detail} = this.data.get('');
        const modules = detail?.modules;
        const version = type === DETAIL_TYPE.decoupled
            ? detail?.engineVersion
            : modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
        const isLowVersionWith110 = compareVersions('1.1.0', version);
        const isLowVersionWith118 = compareVersions('1.1.8', version);
        if (type === DETAIL_TYPE.united && !isLowVersionWith110 && !isLowVersionWith118) {
            this.data.set('hasNotInstallAuditPlugin', '');
        }
        else {
            await this.checkHasNotInstallAuditPlugin();
        }
    }
    detached() {
        clearTimeout(this.data.get('scrollTimer'));
    }

    refresh() {
        this.ref('chart-panel')?.refresh();
    }

    async changeActive(target: {value: {key: string}}) {
        this.changeInstance({selected: []});
        const {type} = this.data.get('');
        this.data.set('active', target.value.key);
        if (type === DETAIL_TYPE.united) {
            this.getInstances(target.value.key);
            this.data.set('indicatorLoading', false);
        }
        else {
            if (!this.data.get('decoupledDatasource').length) {
                this.getComputeGroups();
            }
            else {
                this.refreshCgIndicator();
            }
        }
    }
    getInstances(key: string) {
        const {detail} = this.data.get('');
        let instances = [];
        if (key === INDICATOR_TYPE.other) {
            instances = detail.instances?.filter(item => item.moduleType === 'palofe')?.map(i => ({
                label: i.instanceId,
                value: i.instanceId
            })) || [];
        }
        else {
            instances = detail.instances?.filter(item => item.moduleType === key)?.map(i => ({
                label: i.instanceId,
                value: i.instanceId
            })) || [];
        }
        this.data.set('instances', instances);
        this.nextTick(() => {
            this.changeIndicator();
            if (instances.length) {
                this.changeInstance({ selected: [instances[0]?.value] });
            }
        });
    }
    changeInstance(data: {selected: string[]}) {
        this.data.set('selectedList', [...data.selected]);
    }
    
    handleIndicatorFilter(indicatorObject: object) {
        const {active, indicatorMap} = this.data.get('');
        indicatorMap[active] = _.cloneDeep(indicatorObject);
        const checkedIndicatorsObj = {};
        _.forEach(indicatorObject, (value: {label: string, indicators: Array<object>}, key) => {
            const indicators = _.filter(value.indicators, item => item.checked);
            if (indicators?.length) {
                checkedIndicatorsObj[key] = {
                    label: value.label,
                    indicators
                };
            }
        })
        this.data.set('checkedIndicatorsObj', checkedIndicatorsObj);
        const checkedIndicators = _.flatten(_.map(_.values(checkedIndicatorsObj), v => v.indicators))
        this.data.set('indicatorList', checkedIndicators);
        this.data.set('activeKey', checkedIndicators[0]?.key);
    }
    // 切换一级分类时拿到初始指标
    changeIndicator() {
        const {active, indicatorMap, type, detail} = this.data.get('');
        const indicatorObject = indicatorMap[active];
        if (active === INDICATOR_TYPE.other) {
            const modules = detail?.modules;
            let version = '';
            if (type === DETAIL_TYPE.decoupled) {
                version = detail?.engineVersion;
            }
            else if (type === DETAIL_TYPE.united) {
                version = modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
            }
            const isLowVersionWith110 = compareVersions('1.1.0', version);
            const isLowVersionWith118 = compareVersions('1.1.8', version);
            const indexMonidor = indicatorObject.indexMonitor;
            // console.log('当前version值:', version);
            otherIndicator = type === DETAIL_TYPE.decoupled ? [SlowQueryRecordsCount] : isLowVersionWith110 ? [SingleReplicaTableCount] :
                isLowVersionWith118 ? [UnhealthyTabletCount, SingleReplicaTableCount] : [SlowQueryRecordsCount, UnhealthyTabletCount, SingleReplicaTableCount];
            if(!indexMonidor.indicators.length) {
                indexMonidor.indicators = _.cloneDeep(otherIndicator);
            }
        }
        this.handleIndicatorFilter(indicatorObject);
        // 图表面板滚动回顶部
        const chartContainer = this.ref('chart-panel')?.ref('chart-panel');
        chartContainer && chartContainer.scrollTo({top: 0, behavior: 'smooth'})
    }

    changeIndicatorType(target: { value: string }) {
        this.data.set('statistics', target.value);
    }

    changeNumPerLine(target: { value: number }) {
        this.data.set('numPerLine', target.value);
    }
    // 点击树节点 - 监控面板定位到对应位置
    handleSelectNode(key: string) {
        this.data.set('ignoreScroll', true);
        const chartContainer = this.ref('chart-panel')?.ref(`chart-${key}`).el;
        chartContainer && chartContainer.scrollIntoView({behavior: 'smooth'});
    }
    // 滚动监控面板 - 右侧tree定位到对应位置
    handleScrollMonitor({key, el}) {
        // scrollIntoView触发的scroll事件不做处理
        if (this.data.get('ignoreScroll')) {
            if (this.data.get('scrollTimer')) {
                clearTimeout(this.data.get('scrollTimer'));
            }
            const timer = setTimeout(() => this.data.set('ignoreScroll', false), 20);
            this.data.set('scrollTimer', timer);
            return;
        }
        // 选中tree对应节点
        this.data.set('activeKey', key);
        // 控制line位置
        const dom = document.getElementsByClassName('s-tree2-treenode-selected');
        if (dom.length) {
            this.ref('monitor-tree')?.handleScrollLine(dom[0].offsetTop);
        }
    }

    changeTime(target: {data: any}) {
        this.data.set('time', {
            endTime: formatUtcTime(m(target.data.end)),
            startTime: formatUtcTime(m(target.data.begin))
        });
        this.refresh();
    }
    // 获取计算组
    async getComputeGroups() {
        this.data.set('indicatorLoading', true);
        const {deployId} = this.data.get('');
        if (!deployId) {
            Notification.error('缺少集群ID，请返回列表页重试');
            return;
        }
        try {
            const {cgInfoForDecoupledMonitor: result, masterInstanceId} = await this.$http.paloPost('paloMonitorComputeGroup', {deployId});
            this.data.set('masterInstanceId', masterInstanceId);
            const decoupledDatasource = _.map(result, (item: object) => ({
                text: item.cgName,
                value: item.cgId,
                children: _.map(item.instanceIds, id => ({
                    text: id,
                    value: id
                }))
            }))
            this.data.set('decoupledDatasource', decoupledDatasource);
            this.refreshCgIndicator();
        }
        catch (e) {
            console.error('拉取计算组失败', e);
        }
        this.data.set('indicatorLoading', false);
    }
    refreshCgIndicator() {
        const decoupledDatasource = this.data.get('decoupledDatasource');
        if (decoupledDatasource.length) {
            const decoupledValue = _.map(decoupledDatasource[0].children, item => ([decoupledDatasource[0].value, item.value]));
            this.data.set('decoupledValue', decoupledValue);
            this.nextTick(() => {
                this.changeIndicator();
            })
        }
    }

    async checkHasNotInstallAuditPlugin() {
        const {type, detail, deployId} = this.data.get('');
        const modules = detail?.modules;
        const version = type === DETAIL_TYPE.decoupled
            ? detail?.engineVersion
            : modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
        const result = await this.$http.paloPost('slowAndLargeQuery', {
            deployId,
            engineVersion: version,
            isCheck: 'true'
        });
        const needTip = _.includes([501, 502, 503, 504], result.code);
        this.data.set('hasNotInstallAuditPlugin', needTip ? result.message: '');
    }
}