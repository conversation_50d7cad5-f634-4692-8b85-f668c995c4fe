import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {DatePicker, Radio} from '@baidu/sui';
import _ from 'lodash';
import './monitor.less';

const ONE_HOUR = 1000 * 60 * 60;
const timeRange = [
    { text: '1小时', value: ONE_HOUR },
    { text: '6小时', value: ONE_HOUR * 6 },
    { text: '1天', value: ONE_HOUR * 24 },
    { text: '7天', value: ONE_HOUR * 24 * 7 },
    { text: '14天', value: ONE_HOUR * 24 * 14 },
    { text: '40天', value: ONE_HOUR * 24 * 40 }
];
export default class TimePanel extends Component{

    static template = html`
        <div class="palo-monitor-time-panel">
            时间范围：
            <s-radio-group
                on-change="handleRadioGroupChange"
                radioType="button"
                class="mr16"
                datasource="{{timeSource}}"
                value="{=time=}"
            />
            <s-date-range-picker
                value="{= date =}"
                range="{{range}}"
                shortcut="{{shortcutDateRange}}"
                mode="second"
                on-change="onTimeChange"
            />
        </div>
    `;

    static components = {
        's-date-range-picker': DatePicker.DateRangePicker,
        's-radio-group': Radio.RadioGroup,
    }

    initData() {
        return {
            shortcutDateRange: [],
            time: 60 * 60 * 1000,
            timeSource: [
                {
                    text: '1小时',
                    value: 60 * 60 * 1000
                },
                {
                    text: '1天',
                    value: 24 * 60 * 60 * 1000
                },
                {
                    text: '7天',
                    value: 7 * 24 * 60 * 60 * 1000
                }
            ]
        }
    }

    attached() {
        const now = new Date().getTime();
        const oneHour = 60 * 60 * 1000;
        const end = new Date().getTime() + 60 * 60 * 1000;
        const begin = end - 40 * 24 * 60 * 60 * 1000;
        const date = {
            begin: new Date(now - oneHour),
            end: new Date(now)
        };
        const range = {
            begin: new Date(begin),
            end: new Date(end)
        };
        this.data.set('range', range);
        this.data.set('date', date);
        this.fire('time-change', {data: date});
        this.initShortcutRange();
    }

    onTimeChange(target: {value: any}) {
        let begin = new Date(target.value.begin).getTime();
        let end = new Date(target.value.end).getTime();
        if (begin === end) {
            begin = begin - 1000;
        }
        this.data.set('time', null);
        this.fire('time-change', {data: {begin, end}});
    }

    handleRadioGroupChange(target: { value: any }) {
        const now = new Date().getTime();
        const date = {
            begin: new Date(now - target.value),
            end: new Date(now)
        };
        this.data.set('date', date);
        this.fire('time-change', {data: date});
    }

    async initShortcutRange() {
        let finalShortcut = timeRange;
        const shortcut = this.data.get('shortcut');

        if (shortcut && Array.isArray(shortcut) && shortcut.length) {
            finalShortcut = shortcut;
        }

        const shortcutDateRange = [];
        _.forEach(finalShortcut, time => {
            shortcutDateRange.push({
                text: time.text,
                onClick: picker => {
                    const date = {
                        begin: new Date(new Date().getTime() - time.value),
                        end: new Date()
                    };
                    picker.setValueByShortCut(date);
                }
            })
        });

        // 太多的话排列不开，所以修改 style
        if (shortcutDateRange.length > 7) {
            this.data.set('isMoreShortcut', true);
        }

        this.data.set('shortcutDateRange', shortcutDateRange);
    }
}