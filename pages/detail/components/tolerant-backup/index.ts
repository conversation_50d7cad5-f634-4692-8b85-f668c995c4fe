/*
 * 备份恢复页面
 */

import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {Tabs, Alert, Loading} from '@baidu/sui';
import BackUpList from './backup-list';
import SnapshotList from './snapshot-list';
import {updateUrlQuery, deleteUrlQuery} from '@common/utils';
import {bucketHref} from './config';
import './index.less';

export default class BackUpPage extends Component {
    static pageName = 'tolerant-backup';
    static template = html`
     <div class="tolerant-backup">
        <s-alert
            skin="info"
            class="mb16"
            closable
            message="备份恢复"
            on-close="onAlertClose"
        >
            <div slot="description">
                <p>数据备份或恢复过程中会因集群重启（如进行水平扩容，水平缩容，垂直变配，参数配置等操作）而导致任务执行失败，请等待系统重启后重新尝试。</p>
                <p>支持对当前集群设置一次性备份；一次性备份不影响周期备份策略，但同一个库表正在备份或恢复期间，再执行其他备份或恢复操作会失败。</p>
                <p>BOS存储桶支持变更，变更后，新备份的快照会写入新的BOS存储桶中，原存储桶中的备份文件仍然可恢复。</p>
                <p>备份文件会占用BOS存储空间，将由BOS进行计费出账，请注意到<a href="${bucketHref}" target="_blank">BOS存储桶</a>页面清理不需要的备份文件。</p>
            </div>
        </s-alert>
        <s-loading class="page-loading" s-if="{{isLoading}}" loading="{{isLoading}}"></s-loading>
        <template s-else>
            <s-tabs class="tolerant-backup-tabs" active="{{pageType}}" on-change="onTabChange">
                <s-tabpane s-for="tab in tabs" label="{{tab.text}}" key="{{tab.value}}">
                </s-tabpane>
            </s-tabs>
            <backup-list
                s-ref="backupList"
                s-if="{{pageType === 'backupList'}}"
                deployId="{{deployId}}"
                detail="{{detail}}"
            />
            <snapshot-list
                s-ref="snapshotList" 
                s-if="{{pageType === 'snapshotList'}}"
                deployId="{{deployId}}"
                taskName="{{taskName}}"
                detail="{{detail}}"
            />
        </template>
     </div>
     `;

     static components = {
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        'backup-list': BackUpList,
        'snapshot-list': SnapshotList,
        's-alert': Alert,
        's-loading': Loading
     };

    initData() {
        return {
            pageType: 'backupList',
            tabs: [
                {text: '任务列表', value: 'backupList'},
                {text: '快照列表', value: 'snapshotList'}
            ]
        };
    }

    onTabChange(e: {value: {key: string}}) {
        this.data.set('pageType', e.value.key);
        updateUrlQuery({
            pageType: e.value.key
        });
        deleteUrlQuery(['taskName']);
        this.data.set('taskName', '');
    }

    refresh() {
        const pageType = this.data.get('pageType');
        switch  (pageType) {
            case 'backupList':
                this.ref('backupList')?.getCurrentBucket();
                this.ref('backupList')?.refresh();
                break;
            case 'snapshotList':
                this.ref('snapshotList')?.refresh();
        }
    }
}
