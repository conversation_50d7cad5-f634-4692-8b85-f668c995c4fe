/**
 * 数据恢复
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Alert, Drawer, Button, Notification, Form, Radio, Loading, Checkbox} from '@baidu/sui';
import {AppLegend} from '@baidu/sui-biz';
import TransferCmpt, { treeNodeType, TableData } from './components/transfer';
import { formateSize } from '@common/utils';
import './index.less';

const klass = 'data-restore';

export default class extends Component {
    static template = html` <template>
        <s-drawer
            size="{{800}}"
            open="{=open=}"
            title="{{name}}"
            cancelText="取消"
            okText="确定"
            showConfirmFooter="{{true}}"
            on-confirm="onConfirm"
            on-close="onClose"
            maskClose="{{false}}"
            class="${klass}"
        >
            <s-alert skin="info" class="mt16 mb16" closable on-close="onAlertClose">
                <p>1. 备份数据会按备份表的原名称恢复到当前集群的当前数据库</p>
                <p>
                    2.
                    数据恢复过程中如果存在与已经备份表名称相同的表，恢复任务会失败，如存在同名表，建议修改或删除同名表
                </p>
                <p>3. 数据恢复后，将沿用备份表的配置，包括表副本数，动态分区开启状态等</p>
            </s-alert>
            <s-append noHighlight label="数据范围" />
            <s-form s-ref="form" rules="{{rules}}" data="{= formData =}">
                <s-formitem prop="backupScope" label="恢复方式：">
                    <s-radio-group
                        datasource="{{backupScopes}}"
                        value="{=formData.backupScope=}"
                        on-change="onScopeChange"
                        enhanced="{{true}}"
                        radioType="button"
                        name="backupScope"
                    ></s-radio-group>
                </s-formitem>
                <s-formitem prop="targetObjs" label="选择库表：" s-if="{{formData.backupScope !== 'FULL'}}">
                    <s-loading
                        s-if="{{loadingTree}}"
                        loading="{{loadingTree}}"
                    >
                    </s-loading>
                    <transfer
                        s-else
                        treeData="{=treeData=}"
                        formData="{=formData=}"
                        checkedKeys="{=checkedKeys=}"
                        searchValue="{{searchValue}}"
                        on-deleteAll="onDeleteAll"
                        on-check="onCheck"
                        on-deleteItem="onDeleteItem"
                        on-inputSearchValue="onInputSearchValue"
                    />
                </s-formitem>
                <s-formitem prop="inheritProperties" class="form-item-center" label="继承配置：">
                    <s-checkbox
                        label="恢复后沿用备份表的全部配置，包括副本数、动态分区开启状态等"
                        checked="{=formData.inheritProperties=}"
                    />
                </s-formitem>
            </s-form>
        </s-drawer>
    </template>`;

    static components = {
        's-drawer': Drawer,
        's-alert': Alert,
        's-button': Button,
        's-form': Form,
        's-formitem': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-loading': Loading,
        's-append': AppLegend,
        transfer: TransferCmpt,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup
    };

    initData() {
        return {
            name: '数据恢复',
            open: true,
            okText: '提交',
            confirming: false,
            loadingConfirmText: '提交中...',
            treeData: [],
            checkedKeys: [],
            loadingTree: true,
            backupScopes: [
                {
                    label: '按表恢复',
                    value: 'TABLE'
                },
                {
                    label: '按库恢复',
                    value: 'DATABASE'
                },
                {
                    label: '全量恢复',
                    value: 'FULL'
                }
            ],
            formData: {
                backupScope: 'TABLE',
                inheritProperties: true,
                targetObjs: [],
                customDbName: ''
            },
            rules: {
                targetObjs: [{required: true, message: '请选择需要恢复的表或库'}]
            }
        };
    }
    attached() {
        this.getDataBaseList();
    }

    async onConfirm() {
        try {
            await this.ref('form').validateFields();
            this.data.set('confirming', true);
            const {backupScope, inheritProperties, customDbName, targetObjs = []} = this.data.get('formData');
            const { id, deployId } = this.data.get('');
            const restoreObjs = backupScope === 'TABLE'
                ? [
                    {
                        dbName: customDbName,
                        tables: targetObjs.map(i => {return i.label})
                    }
                ]
                : backupScope === 'DATABASE'
                    ? targetObjs.map(i => ({ dbName: i.label, tables: [] }))
                    : [];
            const params = {
                deployId,
                restoreObjs,
                backupLogId: id,
                backupScope,
                inheritProperties
            };
            await this.$http.paloPost('paloSnapShotRestore', params);
        } catch {
            Notification.error('数据恢复失败');
        } finally {
            this.data.set('confirming', false);
            this.fire('success', {});
            this.onClose();
        }
    }

    async getDataBaseList() {
        const {id, deployId} = this.data.get('');
        this.data.set('loadingTree', true);
        const {dataScopeDetails} = await this.$http.paloPost('paloSnapShotDetail', {
            backupLogId: id,
            deployId
        });
        const databases = dataScopeDetails.map((i: object) => ({
            label: i.dbName,
            key: i.dbName,
            customDbName: i.dbName,
            customDataSize: formateSize(i.totalSize),
            checkable: false,
            children: i.tables?.map(table => ({
                label: table.tableName,
                key: i.dbName + table.tableName,
                customDbName: i.dbName,
                isLeaf: true,
                customDataSize: table.dataSize,
                customTableName: table.tableName,
            })),
            isLeaf: false
        }));
        this.data.set('treeData', [...databases]);
        this.data.set('databases', databases);
        this.data.set('loadingTree', false);
    }

    async onScopeChange(target: { value: string }) {
        await this.getDataBaseList();
        const databases = this.data.get('databases');
        let treeData = this.data.get('treeData');
        switch (target.value) {
            case 'TABLE':
                treeData = databases.map((i: treeNodeType) => ({ ...i, isLeaf: false, checkable: false }));
                break;
            case 'DATABASE':
                treeData = databases.map((i: treeNodeType) => ({ ...i, isLeaf: true, disabled: false, children: null, checkable: true }));
                break;
        }
        this.data.set('searchValue', '');
        this.data.set('formData.targetObjs', []);
        this.data.set('treeData', _.cloneDeep(treeData));
        this.data.set('checkedKeys', []);
    }

    setDisabledForTreeNodes(treeData: treeNodeType[], isDisabled: boolean, func?: Function) {
        treeData.forEach((node: treeNodeType) => {
            // 给当前节点添加 disabled 属性
            let target = isDisabled;
            if (func) {
                target = func(node.key);
            }
            node.disabled = target;

            // 如果有子节点，则递归调用全部设置和当前节点一样的禁用值
            if (node.children && node.children.length > 0) {
                this.setDisabledForTreeNodes(node.children, target);
            }
        });
    }

    isNotChildren(dbName: string, key: string): boolean {
        return dbName !== key;
    }

    onCheck(e: {checkedKeys: string[]; info: object}) {
        if (this.data.get('formData.backupScope') === 'TABLE') {
            const { treeData } = this.data.get('');
            const {customDbName = ''} = e?.info?.node?.data?.raw?.treeCustomValue;
            this.data.set('formData.customDbName', customDbName);
            // 选中某个节点后，其他数据库禁用
            if (e.checkedKeys.length === 0) {
                // 设置所有节点可用
                this.setDisabledForTreeNodes(treeData, false);
            } else {
                // 设置其他库选择禁用
                this.setDisabledForTreeNodes(treeData, true, (key: string) => this.isNotChildren(customDbName, key));
            }
            this.data.set('treeData', _.cloneDeep(treeData));
            this.data.set('formData.targetObjs', e.checkedKeys?.map(item => {
                return {
                    label: item?.slice(customDbName.length),
                    key: item
                }
            }));
        }
        else {
            this.data.set('formData.targetObjs', e.checkedKeys?.map(item => {
                return {
                    label: item,
                    key: item
                }
            }));
        }
    }

    onDeleteItem(data: {index: number}) {
        const targetObjs = this.data.get('formData.targetObjs');
        this.data.set(`formData.targetObjs`, [...targetObjs.slice(0, data.index), ...targetObjs.slice(data.index + 1)]);
        this.data.set('checkedKeys', this.data.get('formData.targetObjs').map(item => item.key));
        if (this.data.get('checkedKeys').length === 0) {
            const treeData = this.data.get('treeData');
            this.setDisabledForTreeNodes(treeData, false);
            this.data.set('treeData', _.cloneDeep(treeData));
        }
    }

    onDeleteAll() {
        this.data.set('formData.targetObjs', []);
        this.data.set('checkedKeys', []);
        const treeData = this.data.get('treeData');
        this.setDisabledForTreeNodes(treeData, false);
        this.data.set('treeData', _.cloneDeep(treeData));
    }

    onInputSearchValue(data: {value: string}) {
        this.data.set('searchValue', data.value.trim());
    }

    // 关闭
    onClose() {
        this.dispose();
    }
}
