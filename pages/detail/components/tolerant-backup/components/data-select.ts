import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Radio, Form, Input, Tree2, Loading} from '@baidu/sui';
import {OutlinedDelete, OutlinedClose} from '@baidu/sui-icon';
import TransferCmpt, { treeNodeType, TableData } from './transfer';
import {formateSize} from '@common/utils';
import './index.less';

export default class DataSelect extends Component {
    static template = html`
        <div class="palo-form-panel">
            <s-append noHighlight label="数据范围" />
            <s-form
                s-ref="form"
                rules="{{rules}}"
                data="{= formData =}">        
                <s-formitem
                    prop="backupScope"
                    label="备份方式："
                >
                    <s-radio-group
                        datasource="{{backupScopes}}"
                        value="{=formData.backupScope=}"
                        on-change="onScopeChange"
                        enhanced="{{true}}"
                        radioType="button"
                        name="backupScope"
                    ></s-radio-group>
                </s-formitem>
                <s-formitem
                    prop="targetObjs"
                    label="选择库表："
                    style="margin-bottom: 0"
                    s-if="{{formData.backupScope !== 'FULL'}}"
                >
                    <s-loading
                        s-if="{{loadingTree}}"
                        loading="{{loadingTree}}"
                    >
                    </s-loading>
                    <s-transfer
                        s-else
                        treeData="{=treeData=}"
                        onLoadData="{{onLoadData}}"
                        formData="{=formData=}"
                        checkedKeys="{=checkedKeys=}"
                        searchValue="{{searchValue}}"
                        on-deleteAll="onDeleteAll"
                        on-check="onCheck"
                        on-deleteItem="onDeleteItem"
                        on-inputSearchValue="onInputSearchValue"
                    />
                    <p s-if="showError" slot="error">请选择库表</p>
                </s-formitem>
            </s-form>
        </div>
    </template>`;

    static components = {
        's-append': AppLegend,
        's-form': Form,
        's-formitem': Form.Item,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-input': Input,
        's-tree': Tree2,
        's-outlined-delete': OutlinedDelete,
        's-outlined-close': OutlinedClose,
        's-loading': Loading,
        's-transfer': TransferCmpt
    };

    initData() {
        return {
            backupScopes: [
                {
                    label: '按表备份',
                    value: 'TABLE'
                },
                {
                    label: '按库备份',
                    value: 'DATABASE'
                },
                {
                    label: '全量备份',
                    value: 'FULL'
                }
            ],
            treeData: [],
            checkedKeys: [],
            loadingTree: true,
            onLoadData: this.onLoadData.bind(this),
        };
    }

    attached() {
        const scope = this.data.get('formData.backupScope');
        const targetObjs = this.data.get('formData.targetObjs');
        if (scope === 'DATABASE' && targetObjs?.length) {
            this.data.set('checkedKeys', targetObjs.map(item => item.dbName));
        }
        this.getDataBaseList(scope);
    }

    async getDataBaseList(scope: string) {
        if (scope === 'FULL') {
            return;
        }
        this.data.set('loadingTree', true);
        this.fire('loadingTree', true);
        const {dbNameList} = await this.$http.paloPost('paloBackupDatabaseList', {deployId: this.data.get('deployId')});
        const databases = dbNameList.map((i: treeNodeType) => ({
            label: i.dbName,
            key: i.dbName,
            isLeaf: scope === 'DATABASE' ? true : false,
            customDataSize: formateSize(parseInt(i.dataSize)),
            customDbName: i.dbName,
            checkable: scope === 'DATABASE' ? true : false,
        }));
        this.data.set('treeData', [...databases]);
        this.data.set('databases', databases);
        this.data.set('loadingTree', false);
        this.fire('loadingTree', false);
    }

    // 加载库表信息
    async onLoadData(treeNodeData: treeNodeType) {
        const {tables} = await this.$http.paloPost('paloBackupTableList', {
            deployId: this.data.get('deployId'),
            dbName: treeNodeData.label
        });
        return tables.map((i: TableData) => ({
            label: i.tableName,
            key:  treeNodeData.label + i.tableName,
            customDataSize: i.dataSize,
            isLeaf: true,
            customDbName: treeNodeData.label,
            disabled: treeNodeData.disabled
        }));
    }

    async onScopeChange(target: { value: string }) {
        if (target.value === 'FULL') {
            this.data.set('treeData', []);
            this.data.set('checkedKeys', []);
            this.data.set('formData.targetObjs', []);
            return;
        }
        await this.getDataBaseList(target.value);
        const databases = this.data.get('databases');
        switch (target.value) {
            case 'TABLE':
                this.data.set(
                    'treeData',
                    databases.map((i: treeNodeType) => ({...i, isLeaf: false, checkable: false}))
                );
                break;
            case 'DATABASE':
                this.data.set(
                    'treeData',
                    databases.map((i: treeNodeType) => ({...i, isLeaf: true, disabled: false, checkable: true}))
                );
                break;
        }
        this.data.set('searchValue', '');
        this.data.set('formData.targetObjs', []);
        this.data.set('checkedKeys', []);
    }

    setDisabledForTreeNodes(treeData: treeNodeType[], isDisabled: boolean, func?: Function) {
        treeData.forEach((node: treeNodeType) => {
            // 给当前节点添加 disabled 属性
            let target = isDisabled;
            if (func) {
                target = func(node.key);
            }
            node.disabled = target;

            // 如果有子节点，则递归调用全部设置和当前节点一样的禁用值
            if (node.children && node.children.length > 0) {
                this.setDisabledForTreeNodes(node.children, target);
            }
        });
    }

    isNotChildren(dbName: string, key: string): boolean {
        return dbName !== key;
    }

    onCheck(e: { checkedKeys: string[]; info: object }) {
        const scope = this.data.get('formData.backupScope');
        if (scope === 'TABLE') {
            const { treeData } = this.data.get('');
            const { customDbName = '' } = e?.info?.node?.data?.raw?.treeCustomValue;
            this.data.set('formData.customDbName', customDbName);
            // 选中某个节点后，其他数据库禁用
            if (e.checkedKeys.length === 0) {
                // 设置所有节点可用
                this.setDisabledForTreeNodes(treeData, false);
            } else {
                // 设置其他库选择禁用
                this.setDisabledForTreeNodes(treeData, true, (key: string) => this.isNotChildren(customDbName, key));
            }
            this.data.set('treeData', _.cloneDeep(treeData));
            this.data.set('formData.targetObjs', e.checkedKeys?.map(item => {
                return {
                    label: item?.slice(customDbName.length),
                    key: item
                }
            }));
        }
        else {
            this.data.set('formData.targetObjs', e.checkedKeys?.map(item => {
                return {
                    label: item,
                    key: item
                }
            }));
        }
    }

    onDeleteItem(data: {index: number}) {
        const targetObjs = this.data.get('formData.targetObjs');
        this.data.set(`formData.targetObjs`, [...targetObjs.slice(0, data.index), ...targetObjs.slice(data.index + 1)]);
        this.data.set('checkedKeys', this.data.get('formData.targetObjs').map(item => item.key));
        if (this.data.get('formData.targetObjs').length === 0) {
            const treeData = this.data.get('treeData');
            this.setDisabledForTreeNodes(treeData, false);
            this.data.set('treeData', _.cloneDeep(treeData));
        }
    }

    onDeleteAll() {
        this.data.set('formData.targetObjs', []);
        this.data.set('checkedKeys', []);
        const treeData = this.data.get('treeData');
        this.setDisabledForTreeNodes(treeData, false);
        this.data.set('treeData', _.cloneDeep(treeData));
    }

    onInputSearchValue(data: {value: string}) {
        this.data.set('searchValue', data.value.trim());
    }

    // 校验
    async verify() {
        this.data.set('showError', false);
        const backupScope = this.data.get('formData.backupScope');
        if (backupScope === 'FULL') {
            return Promise.resolve();
        }
        else {
            const targetObjs = this.data.get('formData.targetObjs');
            if (!targetObjs || targetObjs.length === 0) {
                this.data.set('showError', true);
                return Promise.reject('请选择备份对象');
            }
        }
    }
}
