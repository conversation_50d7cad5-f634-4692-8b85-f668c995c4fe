import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend} from '@baidu/sui-biz';
import {Input, Radio, Form, DatePicker, TimePicker, Select, Checkbox} from '@baidu/sui';
import {
    BackUpTaskTypeEnum,
    BackUpTaskPeriodicEnum,
    BackUpTaskTimeWeekType,
    BackUpTaskTimeMonthType,
    BackUpTaskTimeDayType
} from '@common/config';
import {taskNameValidator, retentionDaysValidator} from '@common/utils/rules';
import QuestionTip from '@pages/create/components/question-tip';
import './index.less';

export default class BasicInfo extends Component {
    static template = html`
        <div class="palo-form-panel">
            <s-append noHighlight label="基本信息" />
            <s-form
                s-ref="form"
                rules="{{rules}}"
                data="{= formData =}">        
                <s-formitem
                    prop="boxName"
                    label="BOS存储桶："
                    class="form-item-center"
                >
                    {{boxName}}
                </s-formitem>
                <s-formitem
                    prop="taskName"
                    s-if="mode  === 'new'"
                    class="mb16"
                    label="任务名称："
                >
                    <s-input value="{=formData.taskName=}" />
                    <span slot="help">字母(a-z及A-Z)、数字(0-9)、（-）、(_)，6-20字符</span>
                </s-formitem>
                <s-formitem
                    prop="taskName"
                    s-if="mode  === 'edit'"
                    class="form-item-center"
                    label="任务名称："
                >
                    {{formData.taskName}}
                </s-formitem>
                <s-formitem
                    prop="taskType"
                    s-if="mode === 'new'"
                    label="任务类型："
                >
                    <s-radio-group
                        datasource="{{taskTypes}}"
                        value="{=formData.taskType=}"
                        enhanced="{{true}}"
                        radioType="button"
                        name="taskType"
                    ></s-radio-group>
                </s-formitem>
                <s-formitem
                    prop="taskType"
                    s-else
                    label="任务类型："
                    class="form-item-center"
                >
                    {{formData.taskType | formateTaskType}}
                </s-formitem>

                <template s-if="formData.taskType === 'ONE_TIME'">
                    <s-formitem
                        prop="execInfo"
                        class="form-item-center"
                        label="执行方式："
                    >
                        <s-radio-group
                            datasource="{{timeTypes}}"
                            value="{=formData.execInfo.execType=}"
                            enhanced="{{true}}"
                            radioType="button"
                            name="execInfo"
                        ></s-radio-group>
                    </s-formitem>
                    <s-formitem
                        prop="exeInfo"
                        s-if="formData.execInfo.execType === 'SCHEDULE'"
                        class="form-item-center"
                        label="选择时间："
                    >
                        <s-datepicker
                            value="{=formData.execInfo.execTimestamp=}"
                            mode="second"
                            range="{{range}}"
                        />
                    </s-formitem>
                </template>

                <template s-if="formData.taskType === 'PERIODIC'">
                    <s-formitem
                        prop="schedulePeriod"
                        label="调度周期："
                    >
                        <s-radio-group
                            datasource="{{PeriodicTypes}}"
                            value="{=formData.periodicInfo.schedulePeriod=}"
                            enhanced="{{true}}"
                            radioType="button"
                            on-change="onSchedulePeriodChange"
                        ></s-radio-group>
                    </s-formitem>

                    <s-formitem
                        s-if="formData.periodicInfo.schedulePeriod === 'WEEKLY'"
                        prop="periodicInfo.execWeek"
                        class="form-item-center weekly"
                    >
                        <span slot="label">
                            <span>执行日期：</span>
                            <question-tip placement="right" type="question">
                                如果配置日期不存在（如：2月30号）则备份任务不执行。
                            </question-tip>
                        </span>
                        <s-checkbox-group
                            value="{=formData.periodicInfo.execWeek=}"
                            enhanced="{{true}}"
                        >
                            <s-checkbox
                                s-for="item in WeekTypes"
                                label="{{item.text}}"
                                value="{{item.value}}"
                            />
                        </s-checkbox-group>
                    </s-formitem>

                    <s-formitem
                        s-if="formData.periodicInfo.schedulePeriod === 'MONTHLY'"
                        prop="periodicInfo.execDay"
                        class="form-item-center monthly"
                    >
                        <span slot="label">
                            <span>执行日期：</span>
                            <question-tip placement="right" type="question">
                                如果配置日期不存在（如：2月30号）则备份任务不执行。
                            </question-tip>
                        </span>
                        <s-select
                            value="{=formData.periodicInfo.execDay=}"
                            enhanced="{{true}}"
                            datasource="{{DayTypes}}"
                            placeholder="请选择日期"
                            multiple
                        >
                            <s-option
                                s-for="day in DayTypes"
                                value="{{day.value}}"
                                label="{{day.text}}"
                            ></s-option>
                        </s-select>
                    </s-formitem>

                    <s-formitem
                        s-if="formData.periodicInfo.schedulePeriod === 'YEARLY'"
                        class="form-item-center yearly"
                        required
                    >
                        <span slot="label">
                            <span>执行日期：</span>
                            <question-tip placement="right" type="question">
                                如果配置日期不存在（如：2月30号）则备份任务不执行。
                            </question-tip>
                        </span>
                        <s-formitem
                            prop="periodicInfo.execMonth"
                        >
                            <s-select
                                value="{=formData.periodicInfo.execMonth=}"
                                prop="periodicInfo.execMonth"
                                enhanced="{{true}}"
                                datasource="{{MonthTypes}}"
                                placeholder="请选择月份"
                                multiple
                            >
                                <s-option
                                    s-for="month in MonthTypes"
                                    value="{{month.value}}"
                                    label="{{month.text}}"
                                ></s-option>
                            </s-select>
                        </s-formitem>

                        <s-formitem
                            prop="periodicInfo.execDay"
                        >
                            <s-select
                                value="{=formData.periodicInfo.execDay=}"
                                prop="periodicInfo.execDay"
                                enhanced="{{true}}"
                                datasource="{{DayTypes}}"
                                placeholder="请选择日期"
                                multiple
                            >
                                <s-option
                                    s-for="day in DayTypes"
                                    value="{{day.value}}"
                                    label="{{day.text}}"
                                ></s-option>
                            </s-select>
                        </s-formitem>

                    </s-formitem>

                    <s-formitem
                        prop="periodicInfo.execTime"
                        class="form-item-center"
                        label="执行时间："
                    >
                        <s-timepicker
                            value="{=formData.periodicInfo.execTime=}"s
                            clearable="{{false}}"
                        />
                    </s-formitem>

                    <s-formitem
                        class="form-item-center exec-description"
                        label="执行说明："
                    >
                        <div class="description" title="{{execDescription}}">{{ execDescription }}</div>
                    </s-formitem>
                </template>

                <s-formitem
                    prop="retentionDays"
                >
                    <span slot="label">
                        <span>
                            快照保留时间：
                        </span>
                        <question-tip placement="right" type="question">
                            到期后快照及备份文件被删除，如果不配置则永久保存
                        </question-tip>
                    </span>
                    <s-input
                        value="{=formData.retentionDays=}"
                        width="80"
                    /> 天
                </s-formitem>

            </s-form>
        </div>
    </template>`;

    static components = {
        's-append': AppLegend,
        's-form': Form,
        's-formitem': Form.Item,
        's-input': Input,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-datepicker': DatePicker,
        's-timepicker': TimePicker,
        's-select': Select,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-checkbox': Checkbox,
        'question-tip': QuestionTip
    };

    static filters = {
        formateTaskType(type: string) {
            return BackUpTaskTypeEnum.getTextFromAlias(type);
        }
    };

    static computed = {
        execDescription() {
            const {schedulePeriod, execWeek, execMonth, execDay, execTime} = this.data.get('formData.periodicInfo') || {};
            const periodText = schedulePeriod === 'DAILY'
                ? '每天 ' : schedulePeriod === 'WEEKLY'
                    ? '每周 ' : schedulePeriod === 'MONTHLY'
                        ? '每月 ' : '每年 ';
            const weekText = _.map(execWeek, item => BackUpTaskTimeWeekType.getTextFromValue(item))?.join('/') || '-';
            const dayText = _.map(execDay, item => BackUpTaskTimeDayType.getTextFromValue(item))?.join('/') || '-';
            const monthText = _.map(execMonth, item => {
                const month = BackUpTaskTimeMonthType.getTextFromValue(item);
                return _.map(execDay, item => {
                    const day = BackUpTaskTimeDayType.getTextFromValue(item);
                    return `${month}${day}`;
                })?.join('/');
            })?.join('/') || '-';
            const timeText = execTime
                ? `${('0' + execTime.getHours())?.slice(-2)}:${('0' + execTime.getMinutes())?.slice(-2)}:${('0' + execTime.getSeconds())?.slice(-2)}`
                : '-';
            return `${periodText} ${schedulePeriod === 'WEEKLY'
                ? weekText
                : schedulePeriod === 'MONTHLY'
                    ? dayText
                    : schedulePeriod === 'YEARLY'
                        ? monthText
                        : ''
                } ${timeText} 开始执行一次备份任务`;
        }
    }

    initData() {
        return {
            taskTypes: [
                {
                    label: '单次备份',
                    value: 'ONE_TIME'
                },
                {
                    label: '周期备份',
                    value: 'PERIODIC'
                }
            ],
            PeriodicTypes: BackUpTaskPeriodicEnum.toArray(),
            WeekTypes: BackUpTaskTimeWeekType.toArray(),
            DayTypes: BackUpTaskTimeDayType.toArray(),
            MonthTypes: BackUpTaskTimeMonthType.toArray(),  
            timeTypes: [
                {
                    label: '定时执行',
                    value: 'SCHEDULE'
                },
                {
                    label: '立即执行',
                    value: 'IMMEDIATE'
                }
            ],
            range: {
                begin: new Date(),
                end: new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000)
            },
            rules: {
                taskName: [
                    { required: true, message: '请输入任务名称', trigger: 'blur' },
                    { validator: taskNameValidator }
                ],
                periodicInfo: {
                    execWeek: [
                        { required: true, message: '请选择执行日期', trigger: 'blur' }
                    ],
                    execMonth: [
                        { required: true, message: '请选择执行月份', trigger: 'blur' }
                    ],
                    execDay: [
                        { required: true, message: '请选择执行日期', trigger: 'blur' }
                    ]
                },
                retentionDays: [
                    { validator: retentionDaysValidator }
                ]
            }
        };
    }

    attached() {
        this.initTimeAndDate();
    }

    // 校验
    async verify() {
        const ref = this.ref('form') as unknown as Form;
        await ref.validateFields();
    }

    initTimeAndDate() {
        const mode = this.data.get('mode');
        if (mode === 'edit') {
            return;
        }
        const date = new Date();
        this.data.set('formData.execInfo.execTimestamp', date);
        this.data.set('formData.periodicInfo.execTime', new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0));
        this.data.set('formData.periodicInfo.execWeek', []);
        this.data.set('formData.periodicInfo.execMonth', []);
        this.data.set('formData.periodicInfo.execDay', []);
        this.data.set('formData.periodicInfo.schedulePeriod', 'DAILY');
    }

    onSchedulePeriodChange() {
        this.data.set('formData.periodicInfo.execWeek', []);
        this.data.set('formData.periodicInfo.execMonth', []);
        this.data.set('formData.periodicInfo.execDay', []);
    }
}
