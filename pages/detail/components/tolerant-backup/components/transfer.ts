import { Component } from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Input, Tree2} from '@baidu/sui';
import { OutlinedDelete, OutlinedClose } from '@baidu/sui-icon';
import { formateSize } from '@common/utils';
import Tip from '@components/ellipsis-tip';
import './index.less';

export type treeNodeType = {
    dbName?: string;
    label: string;
    key: string;
    isLeaf: boolean;
    children?: treeNodeType[];
    disabled?: boolean;
    dataSize?: number;
}

export type TableData = {
    tableName: string;
    dataSize: number;
    partitions: any[];
    properties: any
}

export default class TransferCmpt extends Component {
    static template = html`
        <div class="table-area-wrap">
            <div class="tree-area">
                <s-input
                    on-input="onInputSearchValue"
                    placeholder="输入关键字搜索"
                    class="mb8 transfer-search"
                ></s-input>
                <s-tree
                    treeData="{=treeData=}"
                    loadData="{{onLoadData}}"
                    checkable
                    on-check="onCheck"
                    checkedKeys="{=checkedKeys=}"
                    searchValue="{{searchValue}}"
                ></s-tree>
            </div>
            <div class="right-area">
                <div class="top-content">
                    <div class="count">已选对象：{{checkedKeys.length}}</div>
                    <span>数据量</span>
                    <div class="delete-all" on-click="onDeleteAll">
                        清除全部
                    </div>
                </div>
                <div s-for="item, index in selectedObjs" class="right-content-item">
                        <ellipsis-tip
                            content="{{item.label}}"
                            text="{{item.label}}"
                            placement="top"
                            class="table-name"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                        >
                        </ellipsis-tip>
                        <span>{{item.key | filterDataSize}}</span>
                        <s-outlined-close
                            class="item-close"
                            disabled="{{item.disabled}}"
                            on-click="onDeleteItem($event, item, index)"
                        />
                    </div>
                </div>
            </div>
        </div>
    `;

    static components = {
        's-input': Input,
        's-tree': Tree2,
        's-outlined-delete': OutlinedDelete,
        's-outlined-close': OutlinedClose,
        'ellipsis-tip': Tip
    };

    static filters = {
        filterDataSize(item: string) {
            const treeData = this.data.get('treeData');
            const backupScope = this.data.get('formData.backupScope');
            let targetObj;
            if (backupScope === 'TABLE') {
                treeData.forEach((node: treeNodeType) => {
                    if (node.children?.length) {
                        node.children?.forEach(child => {
                            if (child.key === item) {
                                targetObj = child;
                            }
                        })
                    }
                });
            }
            else if (backupScope === 'DATABASE') {
                treeData.forEach(node => {
                    if (node.key === item) {
                        targetObj = node;
                    }
                });
            }
            return targetObj?.customDataSize ?? '- KB';
        }
    };

    static computed = {
        selectedObjs() {
            const backupScope = this.data.get('formData.backupScope');
            const targetObjs = this.data.get('formData.targetObjs');
            const checkedKeys = this.data.get('checkedKeys');
            const treeData = this.data.get('treeData');
            if (backupScope === 'TABLE') {
                return checkedKeys?.length ? targetObjs : [];
            } else if (backupScope === 'DATABASE') {
                return treeData.filter(item => checkedKeys.includes(item.key));
            }
        }
    }

    onCheck(e: Event) {
        this.fire('check', e);
    }

    onInputSearchValue(e: Event) {
        this.fire('inputSearchValue', e);
    }

    onDeleteAll(e: Event) {
        this.fire('deleteAll', e);
    }

    onDeleteItem(e: Event, item: TableData, index: number) {
        this.fire('deleteItem', {index});
    }
}
