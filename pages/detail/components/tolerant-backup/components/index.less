.table-area-wrap {
    display: flex;
}
.tree-area {
    border: 1px solid #eee;
    border-radius: 6px;
    padding: 16px;
    height: 350px;
    overflow-y: auto;

    .s-tree2-checkbox-wrapper, .s-tree2-node-content-wrapper {
        line-height: 28px;
    }
    
    .s-tree2-checkbox-wrapper {
        .s-checkbox {
            margin-bottom: 4px;
        }
    }
}
.form-item-center {
    .s-form-item-control-content {
        .s-checkbox{
            .s-radio-text {
                height: 16px;
                line-height: 16px;
            }
        }
    }
    .s-select-options-wrapper {
        min-height: 230px;
    }

    .s-form-item-label {
        line-height: 32px;
    }

    &.weekly {
        .s-checkbox {
            display: inline-flex;
            align-items: center;
            margin-right: 24px;
        }
    }

    &.monthly, &.yearly {
        .s-checkbox {
            width: 14px;
            height: 14px;
        }
    }

    &.yearly {
        .s-checkbox {
            width: 14px;
            height: 14px;
        }

        .s-form-item-control-content {
            display: flex;

            .s-form-item {
                margin-bottom: 0;

                .s-form-item-error {
                    padding-bottom: 4px;
                }
                
                &:first-child {
                    margin-right: 8px;
                }
            }
        }
    }
}

.exec-description {
    .s-form-item-control-wrapper {
        width: calc(~'100% - 120px');
    }
    .description {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.right-area {
    flex-grow: 1;
    margin-left: 16px;
    border: 1px solid #eee;
    border-radius: 6px;
    height: 350px;
    width: 300px;
    overflow-y: auto;

    .top-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #F7F7F9;
        padding: 8px 16px;

        .count {
            width: 100px;
        }
    }

    .top-content-table-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #F7F7F9;
        padding: 4px 16px;
    }

    .delete-all {
        color: #108CEE;
        cursor: pointer;
    }

    .item-content-wrap {
        display: flex;
        justify-content: space-between;
        padding: 4px 16px;
    }

    .right-content-item {
        line-height: 24px;
        height: 24px;
        display: flex;
        width: 100%;
        justify-content: space-between;
        padding: 0 16px;

        .table-name {
            display: inline-block;
            width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

.transfer-search {
    border-color: #144bcc!important;
}

.s-form {
    .s-form-item-label {
        width: 120px;
    }
}