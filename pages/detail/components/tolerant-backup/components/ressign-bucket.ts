/**
 * 指定存储桶弹窗
 * @file index.ts 
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {
    Dialog,
    Select,
    Form,
    Alert,
    Button,
    Notification,
} from '@baidu/sui';
import { bucketHref } from '../config';
import RefreshBtn from '@components/refresh-btn';

export default class RessignBucket extends Component {
    static template = html`
    <template>
        <s-dialog 
            open="{= open =}"
            on-close="close"
            title="{{title}}"
            height="450"
        >
            <s-alert skin="info" class="mb16">
                <p>BOS存储桶路径变更后，新备份的快照会写入新的BOS存储桶中，原存储桶中的备份文件可以正常恢复。</p>
                <p>备份文件会占用BOS存储空间，将由BOS进行计费出账，请注意到BOS存储桶页面清理不需要的备份文件。</p>
            </s-alert>
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                inlineMessage="true"
                label-align="left"

            >
                <s-form-item
                    prop="bucketName"
                    label="BOS存储桶："
                >
                    <s-select
                        s-ref="select"
                        placeholder="请选择存储桶"
                        value="{=formData.bucketName=}"
                        datasource="{{bucketList}}"
                        searchable
                        getPopupContainer="{{getPopupContainer}}"
                        width="300"
                    >
                    </s-select>
                    <span slot="help" class="mt8">请选择已有存储桶，或
                        <a href="${bucketHref}" target="_blank">去创建BOS存储桶</a>
                    </span>
                    <refresh-btn refreshing="{{refreshing}}" on-refresh="refresh" />
                </s-form-item>
            </s-form>
            <footer slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button on-click="submit" skin="primary" loading="{{loading.confirm}}">确定</s-button>
            </footer>
        </s-dialog> 
    </template>  
    `;
    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-alert': Alert,
        's-select': Select,
        'refresh-btn': RefreshBtn,
    };

    static computed = {
        title(): string {
            const bucketName = this.data.get('bucketName');
            return bucketName ? '修改存储桶' : '指定存储桶';
        }
    }

    initData() {
        return {
            open: true,
            formData: {
                bucketName: ''
            },
            refreshing: false,
            bucketList: [],
            rules: {
                bucketName: [
                    {
                        required: true,
                        message: '请选择存储桶'
                    }
                ],
            },
            loading: {
                confirm: false,
            }
        };
    }

    async attached() {
        const bucketName = this.data.get('bucketName');
        this.data.set('formData.bucketName', bucketName);
        await this.getBuckets();
    }

    async getBuckets() {
        const deployId = this.data.get('deployId');
        const {bucketList} = await this.$http.paloPost('paloBucketList', {deployId});
        this.data.set('bucketList', bucketList.map(i => ({
                label: i,
                value: i
        })));
    }

    async refresh() {
        this.data.set('refreshing', true);
        await this.getBuckets();
        this.data.set('refreshing', false);
    }

    close() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    async submit() {
        await this.ref('form').validateFields();
        try {
            const bucketName = this.data.get('formData.bucketName');
            const deployId = this.data.get('deployId');
            this.data.set('loading.confirm', true);
            await this.$http.paloPost('paloBucketDesignate', {
                bucketName,
                deployId
            });
            Notification.success('存储桶指定成功！');
            this.close();
        }
        catch (err) {
            console.error(err);
        }
        finally {
            this.data.set('loading.confirm', false);
        }
    }
}
