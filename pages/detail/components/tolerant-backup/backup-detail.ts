/**
 * 备份任务详情
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Table, Button, Pagination} from '@baidu/sui';
import {AppDetailCell, AppLegend, Empty} from '@baidu/sui-biz';
import {PAGER} from '@common/config/constant';
import {AllEnum, BackUpTaskStateEnum, BackUpTaskTypeEnum} from '@common/config';
import {formatTime, formateSize, formateBackupStatus} from '@common/utils';
import {SnapshotItem} from './snapshot-list';
import {filterStatus} from './config';
import './index.less';

const klass = 'backup-detail';
const allEnum = AllEnum.toArray();

export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            size="{{1000}}"
            open="{=open=}"
            title="{{name}}"
            class="${klass}"
        >
            <s-app-legend noHighlight label="任务信息" />
            <s-detail-cell
                datasource="{{cellSource}}"
                divide="3"
                labelWidth="92px"
            >
            </s-detail-cell>
            <s-app-legend noHighlight label="快照列表" />
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-sort="onSort"
                on-filter="onFilter"
            >
                <div slot="c-backupState">{{row.backupState | filterStatus | raw}}</div>
                <div slot="empty">
                    <s-empty
                        vertical
                        emptyText="暂无快照信息"
                    >
                        <p slot="action"></p>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{table.datasource.length}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.total}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-detail-cell': AppDetailCell,
        's-app-legend': AppLegend,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        's-empty': Empty
    };

    static filters = {
        filterStatus: filterStatus,
    };

    initData() {
        return {
            name: '备份详情',
            open: true,
            cellSource: [],
            table: {
                loading: true,
                columns: [
                    {
                        name: 'snapshotName',
                        label: '快照名称',
                    },
                    {
                        name: 'backupState',
                        label: '状态',
                        width: 100,
                        filter: {
                            options: [
                                ...allEnum,
                                ...BackUpTaskStateEnum.toArray()
                            ],
                            value: allEnum[0].value
                        }
                    },
                    {
                        name: 'bucketName',
                        label: '存储桶',
                        width: 200,
                    },
                    {
                        name: 'backupSize',
                        label: '备份大小',
                        width: 100,
                        render: (item: SnapshotItem) => formateSize(item.backupSize),
                    },
                    {
                        name: 'kernelVersion',
                        label: '对应内核版本',
                        width: 100,
                    },
                    {
                        name: 'createTimestamp',
                        label: '生成时间',
                        width: 150,
                        render: (item: SnapshotItem) => formatTime(item.createTimestamp),
                    }
                ],
                datasource: []
            },
            pager: PAGER,
        };
    }

    attached() {
        this.getSnapList();
        const {
            taskName,
            taskType,
            lastBackupState,
            lastExecTimestamp,
            createTimestamp,
        } = this.data.get('');
        const displayStatus = BackUpTaskStateEnum.getTextFromAlias(lastBackupState);
        this.data.set('cellSource', [
            {
                label: '任务名称：',
                value: taskName
            },
            {
                label: '任务类型：',
                value: BackUpTaskTypeEnum.getTextFromAlias(taskType)
            },
            {
                label: '最近运行状态：',
                value: formateBackupStatus(displayStatus, lastBackupState)
            },
            {
                label: '最近运行时间：',
                value: formatTime(lastExecTimestamp)
            },
            {
                label: '创建时间：',
                value: formatTime(createTimestamp)
            },
        ]);
    }

    async getSnapList() {
        this.data.set('table.loading', true);
        const {deployId, taskName} = this.data.get('');
        const {snapshotDetails = []} = await this.$http.paloPost('paloSnapshotList', {
            taskName,
            pageSize: 10,
            pageNo: 1,
            deployId
        });
        this.data.set('table.datasource', snapshotDetails);
        this.data.set('pager', {
            total: snapshotDetails?.length,
        });
        this.data.set('table.loading', false);
    }

    async onFilter(event: {field: any; filter: any}) {
        const {field, filter} = event;
        this.data.set(`${field.name}`, filter.value);
        await this.getSnapList();
    }

    // 关闭
    onClose() {
        this.dispose();
    }
}
