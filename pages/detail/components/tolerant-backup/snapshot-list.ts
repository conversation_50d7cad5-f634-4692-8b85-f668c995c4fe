/**
 *  快照列表
 *
 * @file snapshot-list.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html, redirect} from '@baiducloud/runtime';
import moment from 'moment';
import {defineComponent} from 'san';
import {Dialog, Pagination, Table, Notification, Button, DatePicker} from '@baidu/sui';
import {AppListPage, SearchBox, Empty, ClipBoard} from '@baidu/sui-biz';
import {formatTime, pickEmpty, formateSize, getTimeStamp, formateBackupStatus} from '@common/utils';
import {AllEnum, BackUpTaskTypeEnum, BackUpTaskStateEnum} from '@common/config';
import {PAGER, BackUpTaskState, BOXER1_MAX_ID} from '@common/config/constant';
import SnapShotDetail from './snapshot-detail';
import DataRestore from './data-restore';
import {dateRangePickerShortcuts} from './config';
import EllipsisTip from '@components/ellipsis-tip';
import Tip from '@components/tip-icon';
import {filterStatus} from './config';
import './index.less';

const klass = 'palo-snapshot-list';
const allEnum = AllEnum.toArray();
export type SnapshotItem = {
    id: number;
    taskName: String;
    taskType: String;
    snapshotName: String;
    backupState: String;
    errorMsg: String;
    kernelVersion: String;
    createTimestamp: number; //生成时间
    bucketName: String;
    bucketPath: String;
    snapshotNames: Array<String>;
    backupSize: number;
};

export default class SnapshotList extends AppListPage {
    searchKey = 'taskName';

    static template = html` <div class="${klass} palo-list-page palo-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="bulk"></div>
            <div slot="filter">
                <s-date-range-picker value="{= date =}" shortcut="{{shortCutDate}}" on-change="onDatePickerChange" />
                <s-searchbox
                    class="searchbox ml8"
                    placeholder="{{searchbox.placeholder}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    text-datasource="{{searchbox.textDataSource}}"
                    value="{= searchbox.keyword =}"
                    keyword-type="{= searchbox.keywordType =}"
                    datasource="{{searchbox.keywordTypes}}"
                    on-search="onSearch"
                    width="170"
                />
            </div>
            <s-table
                class="btn-format-table"
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                on-sort="onSort"
                on-filter="onFilter"
                datasource="{{table.datasource}}"
            >
                <ellipsis-tip
                    slot="c-snapshotName"
                    content="{{row.snapshotName}}"
                    placement="top"
                    showTip="{{true}}"
                    alwaysTip="{{true}}"
                >
                    <a
                        href="javascript:void(0);"
                        class="ellipsis-tip__text a-btn"
                        slot="text"
                        on-click="onView($event, row)"
                    >
                        {{row.snapshotName}}
                    </a>
                </ellipsis-tip>
                <ellipsis-tip
                    slot="c-bucketName"
                    alwaysTip="{{true}}"
                    copy="{{true}}"
                    class="bucketName"
                    text="{{row.bucketName}}"
                    useSlot="{{true}}"
                    placement="top"
                >
                    <div slot="content-slot" class="bucket-tip-content">
                        <div class="bucket-tip-content-item">
                            <span class="bucket-tip-content-item-title">BOS存储桶：</span>
                            <span class="bucket-tip-content-item-text">
                                {{row.bucketName}}
                                <s-clip-board text="{{row.bucketName}}"/>
                            </span>
                        </div>
                        <div class="bucket-tip-content-item">
                            <span class="bucket-tip-content-item-title">BOS路径：</span>
                            <span class="bucket-tip-content-item-text">
                                {{row.bosPath}}
                                <s-clip-board text="{{row.bosPath}}"/>
                            </span>
                        </div>
                        <div class="bucket-tip-content-item">
                            <span class="bucket-tip-content-item-title">文件路径：</span>
                            <span class="bucket-tip-content-item-text">
                                {{row.filePath}}
                                <s-clip-board text="{{row.filePath}}"/>
                            </span>
                        </div>
                    </div>
                </ellipsis-tip>
                <div slot="c-backupState">
                    {{row.backupState | filterStatus | raw}}
                    <tip-cmpt s-if="row.backupState === 'FAILED' || row.backupState === 'RESTORE_FAILED'">
                        {{row.errorMsg}}
                    </tip-cmpt>
                </div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button
                            s-if="{{row.backupState === 'RUNNING' || row.backupState === 'RESTORING'}}"
                            skin="stringfy"
                            class="table-btn-slim"
                            on-click="onCancel(row)"
                        >
                            取消
                        </s-button>
                        <s-button
                            s-else
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{row.backupState === 'FAILED' || restoreDisabled}}"
                            on-click="onRestore($event, row)"
                        >
                            数据恢复
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{row.backupState === 'RESTORING' || row.backupState === 'RUNNING'}}"
                            on-click="onDelete(row)"
                        >
                            删除
                        </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty vertical>
                        <span slot="action" class="empty-area">
                            <template s-if="{{isFilter}}">
                                搜索结果为空，更换筛选条件试试
                            </template>
                            <template s-else>
                                创建备份任务后将生成快照数据，您可以先
                                <s-button
                                    skin="stringfy"
                                    on-click="goCreateTask"
                                    class="ml8"
                                >创建备份任务</s-button>
                            </template>
                        </span>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        's-empty': Empty,
        'ellipsis-tip': EllipsisTip,
        's-date-range-picker': DatePicker.DateRangePicker,
        'tip-cmpt': Tip,
        's-clip-board': ClipBoard,
    };

    static filters = {
        filterStatus: filterStatus
    };

    static computed = {
        restoreDisabled(): boolean {
            const status = this.data.get('detail.status');
            const deployId = this.data.get('detail.deployId');
            return status === 'Stopped' || deployId  - 0 <= BOXER1_MAX_ID;
        }
    }

    initData() {
        return {
            searchbox: {
                keyword: '',
                keywordType: ['snapshotName'],
                keywordTypes: [
                    {
                        value: 'snapshotName',
                        text: '快照名称'
                    },
                    {
                        value: 'taskName',
                        text: '任务名称'
                    },
                    {
                        value: 'bucketName',
                        text: 'BOS存储桶'
                    }
                ]
            },
            table: {
                loading: true,
                columns: [
                    {
                        name: 'snapshotName',
                        label: '快照名称',
                        width: 230,
                        fixed: 'left'
                    },
                    {
                        name: 'taskName',
                        label: '任务名称',
                        width: 170
                    },
                    {
                        name: 'taskType',
                        label: '任务类型',
                        width: 120,
                        filter: {
                            options: [...allEnum, ...BackUpTaskTypeEnum.toArray()],
                            value: allEnum[0].value
                        },
                        render: (item: SnapshotItem) => BackUpTaskTypeEnum.getTextFromAlias(item.taskType)
                    },
                    {
                        name: 'backupState',
                        label: '状态',
                        width: 120,
                        filter: {
                            options: [...allEnum, ...BackUpTaskStateEnum.toArray()],
                            value: allEnum[0].value
                        }
                    },
                    {
                        name: 'bucketName',
                        label: 'BOS存储桶',
                        width: 120
                    },
                    {
                        name: 'backupSize',
                        label: '备份文件大小',
                        width: 100,
                        render: (item: SnapshotItem) => formateSize(item.backupSize)
                    },
                    {
                        name: 'kernelVersion',
                        label: '对应内核版本',
                        width: 100
                    },
                    {
                        name: 'createTimestamp',
                        label: '生成时间',
                        width: 150,
                        render: (item: SnapshotItem) => formatTime(item.createTimestamp)
                    },
                    {
                        name: 'operation',
                        label: '操作',
                        width: 150,
                        fixed: 'right'
                    }
                ],
                datasource: []
            },
            pager: PAGER,
            date: {
                begin: moment().subtract('day', 7).startOf('day').toDate(),
                end: moment().endOf('day').toDate()
            },
            shortCutDate: dateRangePickerShortcuts,
            isFilter: false
        };
    }

    attached() {
        if (this.data.get('taskName')) {
            this.data.set('searchbox.keywordType', ['taskName']);
            this.data.set('searchbox.keyword', this.data.get('taskName'));
        }
        this.refresh();
    }

    onSearch() {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', 10);
        this.refresh();
    }

    goCreateTask() {
        const deployId = this.data.get('deployId');
        redirect(`#/palo/detail?deployId=${deployId}&page=3&pageType=backupList`);
    }

    async onDatePickerChange(event: {value: {begin: Date; end: Date}}) {
        const {value} = event;
        this.data.set('date', value);
        await this.refresh();
    }

    async getSnapList() {
        this.data.set('table.loading', true);
        this.data.set('isFilter', false);
        const {deployId, date, searchbox, taskType, backupState, pager} = this.data.get('');
        const {keywordType, keyword} = searchbox;
        const params = pickEmpty({
            [keywordType]: keyword,
            taskType,
            backupState,
            pageSize: pager.pageSize,
            pageNo: pager.page,
            deployId,
            snapshotCreatedStartAt: getTimeStamp(date.begin),
            snapshotCreatedEndAt: getTimeStamp(date.end)
        });
        if (searchbox.keyword || taskType || backupState) {
            this.data.set('isFilter', true);
        }
        this.data.set('table.datasource', []);
        const {snapshotDetails = [], total} = await this.$http.paloPost('paloSnapshotList', params);
        this.data.set('table.datasource', snapshotDetails?.map(item => ({
            ...item,
            bosPath: item.bucketPath.split('/')[0],
            filePath: item.bucketPath.split('/')[1]
        })));
        this.data.set('pager.count', total);
        this.data.set('table.loading', false);
    }

    async onFilter(event: {field: any; filter: any}) {
        const {field, filter} = event;
        this.data.set(`${field.name}`, filter.value);
        await this.getSnapList();
    }

    async onPageChange(target: {value: {page: number}}) {
        this.data.set('pager.page', target.value.page);
        await this.refresh();
    }

    async onPageSizeChange(target: {value: {pageSize: number}}) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', target.value.pageSize);
        await this.refresh();
    }

    async refresh() {
        this.data.set('table.datasource', []);
        await this.getSnapList();
    }

    onRestore(event: Event, detail: SnapshotItem) {
        event.stopPropagation();
        const {deployId} = this.data.get('');
        const dialog = new DataRestore({
            data: {
                deployId,
                ...detail
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            // 刷新列表数据
            Notification.success('数据恢复提交成功');
            this.refresh();
        });
    }

    // 删除
    onDelete(row: SnapshotItem) {
        const {snapshotName, id} = row;
        const deployId = this.data.get('deployId');
        Dialog.confirm({
            title: '删除快照',
            content: `将删除快照记录及对应快照文件，确认删除快照${snapshotName}吗？`,
            okText: '确定',
            onOk: async () => {
                await this.$http.paloPost('paloSnapShotDelete', {backupLogId: id, deployId});
                Notification.success('快照已删除');
                this.refresh();
            }
        });
    }

    // 取消
    onCancel(row: SnapshotItem) {
        const {snapshotName, id} = row;
        const deployId = this.data.get('deployId');
        const api = _.includes(['RESTORING'], row.backupState) ? 'paloSnapShotRestoreCancel' : 'paloSnapShotCancel'
        Dialog.confirm({
            title: _.includes(['RESTORING'], row.backupState) ? '取消数据恢复' : '取消备份',
            content: defineComponent({
                template: `<div style="width: 380px;">确认取消恢复${snapshotName}吗？</div>`
            }),
            okText: '确定',
            onOk: async () => {
                await this.$http.paloPost(api, {backupLogId: id, deployId});
                Notification.success(_.includes(['RESTORING'], row.backupState) ? '数据恢复已取消' : '备份已取消');
                this.refresh();
            }
        });
    }

    // 查看快照详情
    onView(event: Event, row: SnapshotItem) {
        event.stopPropagation();
        const {deployId} = this.data.get('');
        const dialog = new SnapShotDetail({
            data: {
                deployId,
                backupLogId: row.id,
                detail: {...row}
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }
}
