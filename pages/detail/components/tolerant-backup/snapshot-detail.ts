/**
 * 快照详情
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Drawer, Table, Button, Pagination} from '@baidu/sui';
import {AppDetailCell, AppLegend} from '@baidu/sui-biz';
import {PAGER} from '@common/config/constant';
import {BackUpTaskStateEnum, BackUpTaskTypeEnum} from '@common/config';
import { formatTime, formateSize, formatEmpty, formateBackupStatus } from '@common/utils';
import Tip from '@components/ellipsis-tip';
import './index.less';

const klass = 'backup-detail';

export default class extends Component {
    static template = html`
    <template>
        <drawer-common
            size="{{1000}}"
            open="{=open=}"
            title="{{name}}"
            class="${klass}"
        >
            <s-app-legend noHighlight label="基本信息" />
            <s-detail-cell
                datasource="{{cellSource}}"
                divide="3"
                labelWidth="75px"
            >
            </s-detail-cell>
            <s-app-legend noHighlight label="数据范围" />
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-sort="onSort"
                on-filter="onFilter"
            >
                <ellipsis-tip
                    slot="c-dbName"
                    content="{{row.dbName}}"
                    text="{{row.dbName}}"
                    placement="top"
                    showTip="{{true}}"
                    alwaysTip="{{true}}"
                >
                </ellipsis-tip>
                <ellipsis-tip
                    slot="c-tableName"
                    content="{{row.tableName}}"
                    text="{{row.tableName}}"
                    placement="top"
                    showTip="{{true}}"
                    alwaysTip="{{true}}"
                >
                </ellipsis-tip>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.total}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </drawer-common>
    </template>`;

    static components = {
        'drawer-common': Drawer,
        's-detail-cell': AppDetailCell,
        's-app-legend': AppLegend,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'ellipsis-tip': Tip
    };

    initData() {
        return {
            name: '快照详情',
            open: true,
            cellSource: [],
            table: {
                loading: true,
                columns: [
                    {
                        name: 'dbName',
                        label: '数据库',
                    },
                    {
                        name: 'tableName',
                        label: '数据表',
                    },
                    {
                        name: 'dataSize',
                        label: '数据量',
                        render: (row: object) => {
                            return formatEmpty(row.dataSize);
                        }
                    }
                ],
                datasource: []
            },
            pager: PAGER,
        };
    }

    attached() {
        this.setSnapDetail();
        this.getSnapDetail();
    }

    setSnapDetail() {
        const {
            snapshotName,
            taskName,
            taskType,
            backupState,
            bucketName,
            backupSize,
            kernelVersion,
            createTimestamp,
        } = this.data.get('detail');
        const displayStatus  = BackUpTaskStateEnum.getTextFromAlias(backupState)
        this.data.set('cellSource', [
            {
                label: '文件名称：',
                value: snapshotName
            },
            {
                label: '任务名称：',
                value: taskName
            },
            {
                label: '任务类型：',
                value: BackUpTaskTypeEnum.getTextFromAlias(taskType)
            },
            {
                label: '状态：',
                value: formateBackupStatus(displayStatus, backupState)
            },
            {
                label: 'BOS存储桶：',
                value: bucketName
            },
            {
                label: '文件大小：',
                value: formateSize(backupSize)
            },
            {
                label: '内核版本：',
                value: kernelVersion
            },
            {
                label: '生成时间：',
                value: formatTime(createTimestamp)
            },
        ]);
    }

    async getSnapDetail() {
        this.data.set('table.loading', true);
        const {deployId, backupLogId} = this.data.get('');
        const {dataScopeDetails = []} = await this.$http.paloPost('paloSnapShotDetail', {
            backupLogId,
            deployId
        });
        let result = [];
        dataScopeDetails?.forEach(item => {
            if (item.tables?.length) {
                result = result.concat(item.tables?.map(i => ({
                    dbName: item.dbName,
                    tableName: i.tableName,
                    dataSize: i.dataSize
                })));
            }
            else {
                result.push({
                    dbName: item.dbName,
                    tableName: '-',
                    dataSize: formateSize(item.totalSize)
                });
            }
        });
        this.data.set('table.datasource', result);
        this.data.set('table.loading', false);
    }

    // 关闭
    onClose() {
        this.dispose();
    }
}
