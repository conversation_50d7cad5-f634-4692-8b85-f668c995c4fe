/**
 * 创建备份任务
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {<PERSON><PERSON>, Drawer, Button, Notification} from '@baidu/sui';
import BasicInfo from './components/basic-info';
import DataSelect from './components/data-select';
import {getTimeStamp} from '@common/utils';
import './index.less';

const klass = 'create-backup';

export default class extends Component {
    static template = html` <template>
        <s-drawer
            size="{{800}}"
            open="{=open=}"
            title="{{name}}"
            confirming="{{confirming}}"
            loadingAfterConfirm="{{false}}"
            cancelText="取消"
            okText="提交"
            showConfirmFooter="{{true}}"
            on-confirm="onConfirm"
            on-close="onClose"
            maskClose="{{false}}"
            confirmButtonDisable="{{isTreeLoading}}"
            class="${klass}"
        >
            <s-alert skin="info" class="mt16 mb16" closable on-close="onAlertClose">
                <p>1. 数据以单副本保存在BOS存储桶内</p>
                <p>2. 若备份任务因执行超过24小时失败，建议调整备份范围</p>
                <p>3. 当前仅支持备份内表以及View类型的视图，不支持备份外表及multi-catalog中的表</p>
                <p>4. 同一库表同时只能执行一个备份/恢复任务，若当前已有备份/恢复任务正在执行，后续任务将直接失败</p>
                <p s-if="mode === 'edit' && formData.taskType === 'PERIODIC'">5. 编辑任务后，将在下次备份执行时生效</p>
            </s-alert>
            <basic-info s-ref="basicInfo" formData="{=formData=}" boxName="{{boxName}}" mode="{{mode}}"></basic-info>
            <data-select s-ref="dataSelect" formData="{=formData=}" deployId="{{deployId}}" on-loadingTree="onLoadingTree" />
        </s-drawer>
    </template>`;

    static components = {
        's-drawer': Drawer,
        's-alert': Alert,
        's-button': Button,
        'basic-info': BasicInfo,
        'data-select': DataSelect
    };

    initData() {
        return {
            name: '创建备份任务',
            formData: {
                taskType: 'ONE_TIME',
                taskName: '',
                timeType: 'SCHEDULE',
                backupScope: 'TABLE',
                execInfo: {
                    execType: 'SCHEDULE',
                    execTimestamp: new Date()
                },
                periodicInfo: {
                    schedulePeriod: 'DAILY',
                    execTime: '',
                    execWeek: [],
                    execMonth: [],
                    execDay: []
                },
                customDbName: '',
                targetObjs: [],
                retentionDays: ''
            },
            open: true,
            okText: '提交',
            confirming: false,
            loadingConfirmText: '提交中...',
            isTreeLoading: false
        };
    }

    static computed: SanComputedProps = {
        name(): string {
            const mode = this.data.get('mode');
            return mode === 'new' ? '创建备份任务' : '编辑备份任务';
        }
    };

    attached() {
        const {mode, detail} = this.data.get('');
        if (mode === 'edit') {
            this.data.merge('formData', {
                taskName: detail.taskName,
                taskType: detail.taskType
            });
        }
        else {
            const taskName = 'BackUp_' + this.getFormattedTimestamp();
            this.data.merge('formData', {
                taskName,
            });
        }
    }

    getFormattedTimestamp() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        return `${year}${month}${day}${hours}${minutes}`;
    }

    verifyForms() {
        const BasicInfo = this.ref('basicInfo');
        const DataSelect = this.ref('dataSelect');
        const arr = [BasicInfo?.verify(), DataSelect?.verify()];
        return Promise.all(arr);
    }

    formatPeriodParams(periodicInfo: {
        schedulePeriod: string,
        execTime: Date,
        execWeek: string[],
        execMonth: number[],
        execDay: number[]
    }) {
        const {schedulePeriod, execTime, execWeek, execMonth, execDay} = periodicInfo;
        const second = execTime.getSeconds();
        const minute = execTime.getMinutes();
        const hour = execTime.getHours();
        return [
            {
                "cronTimeUnit": "second",
                "timePoints": [second]
            },
            {
                "cronTimeUnit": "minute",
                "timePoints": [minute]
            },
            {
                "cronTimeUnit": "hour",
                "timePoints": [hour]
            },
            ...(
                schedulePeriod === 'WEEKLY'
                ? [{
                    "cronTimeUnit": "dayOfWeek",
                    "timePoints": execWeek
                }]
                : schedulePeriod === 'MONTHLY'
                    ? [{
                        "cronTimeUnit": "dayOfMonth",
                        "timePoints": execDay
                    }]
                    : schedulePeriod === 'YEARLY'
                        ? [{
                            "cronTimeUnit": "month",
                            "timePoints": execMonth
                        }, {
                            "cronTimeUnit": "dayOfMonth",
                            "timePoints": execDay
                        }]
                        : []
            )
        ]
    }

    async onConfirm() {
        try {
            this.verifyForms().then(async () => {
                this.data.set('confirming', true);
                const {deployId, formData, mode, taskId} = this.data.get('');
                const { taskName, taskType, backupScope, execInfo, targetObjs, customDbName, periodicInfo, retentionDays } = formData;
                const objs = backupScope === 'TABLE'
                    ? [
                        {
                            dbName: customDbName,
                            tables: targetObjs.map(i => {return i.label})
                        }
                    ]
                    : backupScope === 'DATABASE'
                        ? targetObjs.map(i => ({ dbName: i.label, tables: [] }))
                        : [];
                let params = {
                    deployId,
                    backupScope,
                    targetObjs: objs,
                    ...(
                        taskType === 'ONE_TIME' ? {
                            execInfo: JSON.stringify({
                                execType: execInfo.execType,
                                execTimestamp: getTimeStamp(execInfo.execTimestamp)
                            })
                        } : {
                            cronElements: this.formatPeriodParams(periodicInfo)
                        }
                    ),
                    ...(
                        retentionDays ? {
                            retentionDays: Number(retentionDays)
                        } : {}
                    )
                };
                params =
                    mode === 'new'
                        ? {
                              ...params,
                              taskName,
                              taskType
                          }
                        : {
                              taskId,
                              ...params
                        };
                try {
                    await this.$http.paloPost(mode === 'new' ? 'paloBackupCreate' : 'paloTaskUpdate', params);
                    this.data.set('confirming', false);
                    this.fire('success', {});
                    this.onClose();
                }
                catch {
                    this.data.set('confirming', false);
                }
            })
        } catch {
            Notification.error('创建备份任务失败');
            this.data.set('confirming', false);
        }
    }

    // 关闭
    onClose() {
        this.dispose();
    }

    onLoadingTree(e: boolean) {
        this.data.set('isTreeLoading', e);
    }
}
