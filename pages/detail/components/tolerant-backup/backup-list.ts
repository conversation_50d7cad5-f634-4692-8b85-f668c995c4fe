/**
 *  备份任务列表
 *
 * @file backup-list.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {defineComponent} from 'san';
import {Dialog, Pagination, Table, Notification, Button, DatePicker, Loading} from '@baidu/sui';
import {AppListPage, SearchBox, Empty} from '@baidu/sui-biz';
import {formatTime, pickEmpty, getTimeStamp} from '@common/utils';
import BackUpDetail from './backup-detail';
import CreateBtn from '@components/create-btn';
import RessignBucket from './components/ressign-bucket';
import moment from 'moment';
import createBackup from './create-backup';
import {AllEnum, BackUpTaskTypeEnum, BackUpTaskStateEnum, CILENT_SILENT} from '@common/config';
import {PAGER, BOXER1_MAX_ID} from '@common/config/constant';
import {dateRangePickerShortcuts} from './config';
import Tip from '@components/tip-icon';
import {filterStatus} from './config';
import './index.less';

type cronItem = {
    cronTimeUnit: string;
    timePoints: number[]
}

type backupTaskItem = {
    id: string;
    taskName: string;
    createTimestamp: number;
    taskType: string;
    lastExecTimestamp: number; // 最近一次执行时间
    lastBackupState: string; // 最近一次执行状态
    extraInfo: string;
    scheduled: boolean;
    cronElements: cronItem[];
    backupScope: string; // 备份范围
    execTimestamp: number; // 计划执行时间
    retentionDays?: number;
    targetObjs?: {
        clusterName: string;
        dbName: string;
        tables: string[];
    }[];
};

const allEnum = AllEnum.toArray();
const klass = 'palo-backup-list';
export default class BackUpList extends AppListPage {
    searchKey = 'taskName';
    static template = html` <div class="${klass} palo-list-page palo-detail-list-page">
        <app-list-page class="${klass}_content">
            <div slot="bulk">
                <div class="left-area">
                    <create-btn
                        text="创建任务"
                        disabled="{{createBtnDisabled}}"
                        disableText="{{disableText}}"
                        on-click="onCreate($event, e)"
                    />
                    <p class="ml8 bucket-area">
                        BOS存储桶：
                        <s-loading size="small" loading="{{bucketLoading}}" class="bucket">
                            {{boxName}}
                            <s-button skin="stringfy" on-click="onRessginBox" disabled="{{ressignDisabled}}"> {{boxButtonText}} </s-button>
                        </s-loading>
                    </p>
                </div>
            </div>
            <div slot="filter">
                创建时间：
                <s-date-range-picker value="{= date =}" shortcut="{{shortCutDate}}" on-change="onDatePickerChange" />
                <s-searchbox
                    class="searchbox ml8"
                    placeholder="请输入任务名称"
                    value="{= searchName =}"
                    on-search="onSearch"
                    width="{{170}}"
                >
                    <div slot="prefix"></div>
                </s-searchbox>
            </div>
            <s-empty vertical="{{true}}" s-if="{{versionTooLow}}" emptyText="" image="{{image}}">
                <div slot="action" class="empty-area">当前集群版本过低</div>
            </s-empty>
            <s-table
                class="btn-format-table"
                s-else
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-sort="onSort"
                on-filter="onFilter"
            >
                <div slot="c-taskName">
                    <s-button skin="stringfy" class="table-btn-slim"  on-click="onView($event, row)">{{row.taskName}}</s-button>
                </div>
                <div slot="c-taskType">{{row.taskType | filterTaskType}}</div>
                <div slot="c-lastBackupState">
                    {{row.lastBackupState | filterStatus | raw}}
                    <tip-cmpt s-if="row.lastBackupState === 'FAILED'"> {{row.extraInfo}} </tip-cmpt>
                </div>
                <div slot="c-operation">
                    <span class="operation">
                        <s-button skin="stringfy" class="table-btn-slim" on-click="onView($event, row)">
                            快照
                        </s-button>
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim"
                            disabled="{{row.taskType === 'ONE_TIME' && row.lastBackupState !== 'NOT_START'}}"
                            on-click="onEdit($event, row)"
                        >
                            编辑
                        </s-button>
                        <s-button skin="stringfy" class="table-btn-slim" on-click="onDelete(row)"> 删除 </s-button>
                    </span>
                </div>
                <div slot="empty">
                    <s-empty vertical="{{true}}" title="暂无数据" image="{{image}}">
                        <template slot="action" s-if="{{isFilter}}">
                            搜索结果为空，更换筛选条件试试
                        </template>
                        <div slot="action" s-elif="{{boxName === '未指定'}}" class="empty-area">
                            请先
                            <s-button
                                skin="stringfy"
                                on-click="onRessginBox"
                                disabled="{{ressignDisabled}}"
                                class="ml4 mr4"
                            >
                                指定存储桶
                            </s-button>
                            再创建任务
                        </div>
                        <div slot="action" s-else class="empty-area">
                            您还没有创建任何任务
                            <s-button skin="stringfy" on-click="onCreate" class="ml4" disabled="{{createBtnDisabled}}">
                                创建任务
                            </s-button>
                        </div>
                    </s-empty>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.total}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    static components = {
        'app-list-page': AppListPage,
        's-searchbox': SearchBox,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        's-empty': Empty,
        's-date-range-picker': DatePicker.DateRangePicker,
        'create-btn': CreateBtn,
        's-loading': Loading,
        'tip-cmpt': Tip
    };

    initData() {
        return {
            searchName: '',
            table: {
                loading: true,
                columns: [
                    {
                        name: 'taskName',
                        label: '任务名称',
                        width: 250
                    },
                    {
                        name: 'taskType',
                        label: '任务类型',
                        width: 150,
                        filter: {
                            options: [...allEnum, ...BackUpTaskTypeEnum.toArray()],
                            value: allEnum[0].value
                        }
                    },
                    {
                        name: 'lastBackupState',
                        label: '最近一次任务状态',
                        width: 150,
                        filter: {
                            options: [...allEnum, ...BackUpTaskStateEnum.toArray().slice(0, 6)],
                            value: allEnum[0].value
                        }
                    },
                    {
                        name: 'lastExecTimestamp',
                        label: '最新运行时间',
                        width: 150,
                        sortable: true,
                        render: (item: backupTaskItem) => formatTime(item.lastExecTimestamp)
                    },
                    {
                        name: 'createTimestamp',
                        label: '创建时间',
                        width: 150,
                        sortable: true,
                        render: (item: backupTaskItem) => formatTime(item.createTimestamp)
                    },
                    {
                        name: 'operation',
                        label: '操作',
                        width: 180
                    }
                ],
                datasource: []
            },
            pager: PAGER,
            date: {
                begin: moment().subtract('day', 7).startOf('day').toDate(),
                end: moment().endOf('day').toDate()
            },
            boxName: '未指定',
            order: 'desc',
            orderBy: 'createTimestamp',
            shortCutDate: dateRangePickerShortcuts,
            isFilter: false
        };
    }

    static filters = {
        filterTaskType(type: string) {
            return BackUpTaskTypeEnum.getTextFromAlias(type);
        },
        filterStatus: filterStatus
    };

    static computed: SanComputedProps = {
        boxButtonText() {
            return this.data.get('boxName') !== '未指定' ? '修改存储桶' : '指定存储桶';
        },
        createBtnDisabled() {
            const status = this.data.get('detail.status');
            const deployId = this.data.get('detail.deployId');
            const versionTooLow = this.data.get('versionTooLow');
            const boxName = this.data.get('boxName');
            return boxName === '未指定' || status === 'Stopped' || deployId  - 0 <= BOXER1_MAX_ID || versionTooLow;
        },
        disableText() {
            const versionTooLow = this.data.get('versionTooLow');
            const boxName = this.data.get('boxName');
            const status = this.data.get('detail.status');
            if (versionTooLow) {
                return '当前版本过低，请升级后重试';
            }
            else if (boxName === '未指定') {
                return '尚未指定存储桶，请先指定存储桶';
            }
            else if (status === 'Stopped') {
                return '已停止集群无法创建备份任务';
            }
            else {
                return '暂不支持';
            }
        },
        ressignDisabled() {
            const status = this.data.get('detail.status');
            const deployId = this.data.get('detail.deployId');
            const versionTooLow = this.data.get('versionTooLow');
            return status === 'Stopped' || deployId  - 0 <= BOXER1_MAX_ID || versionTooLow;
        },
        versionTooLow() {
            const modules = this.data.get('detail.modules');
            const version = modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
            return version <= '0.15';
        },
        image() {
            const isFilter = this.data.get('isFilter');
            const versionTooLow = this.data.get('versionTooLow');
            if (isFilter) {
                return require('@static/img/noResult.svg');
            }
            else if (versionTooLow) {
                return require('@static/img/versionLow.svg');
            }
            else {
                return require('@static/img/empty2.svg');
            }
        }
    };

    async onFilter(event: {field: any; filter: any}) {
        const {field, filter} = event;
        this.data.set(`${field.name}`, filter.value);
        await this.refresh();
    }

    async onSearch() {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', 10);
        await this.refresh();
    }

    async onDatePickerChange(event: {value: {begin: Date; end: Date}}) {
        const {value} = event;
        this.data.set('date', value);
        await this.refresh();
    }

    async onSort(event: {value: {order: string; orderBy: string}}) {
        const {orderBy, order} = event.value;
        this.data.set('order', order);
        this.data.set('orderBy', orderBy);
        await this.refresh();
    }

    async attached() {
        const {deployId} = this.data.get('');
        await this.getCurrentBucket();
        await this.getTableList();
        try {
            const res = await this.$http.post('/api/palo/backup/auth/check', { deployId }, CILENT_SILENT);
            if (res.code === 'FOLLOWING_AUTH') {
                Dialog.confirm({
                    content: defineComponent({
                        template: `<div>
                            <p>检测到子用户（_palo_backup_user）不存在或权限异常，将进行重新进行授权</p>
                            <p>请确认是否重新为子用户（_palo_backup_user）授权？</p>
                        </div>`}),
                    onOk: async () => {
                        await this.$http.paloPost('backupAuthprize', { deployId });
                        Notification.success('子用户_palo_backup_user授权成功');
                        this.refresh();
                    },
                });
            }
            else if (res.code === 'FIRST_AUTH') {
                Dialog.confirm({
                    content: defineComponent({
                        template: `<div>
                            <p>PALO产品将在当前账户下创建子用户（_palo_backup_user）</p>
                            <p>该子用户仅用于 百度数据仓库 PALO 创建仓库、写入备份文件、恢复备份文件等与BOS交互场景中</p>
                            <p>请进行确认是否继续创建创建子用户（_palo_backup_user）？</p>
                        </div>`}),
                    onOk: async () => {
                        await this.$http.paloPost('backupAuthprize', { deployId });
                        Notification.success('创建子用户_palo_backup_user成功');
                        this.refresh();
                    },
                });
            }
        }
        catch (e) {
            Notification.error(e);
        }
    }

    formatCronToPeriodicInfo(cronElements: cronItem[]) {
        const date = new Date();
        const hour = _.find(cronElements, (ele) => ele.cronTimeUnit === 'hour')?.timePoints[0] || 0;
        const minute = _.find(cronElements, (ele) => ele.cronTimeUnit === 'minute')?.timePoints[0] || 0;
        const second = _.find(cronElements, (ele) => ele.cronTimeUnit === 'second')?.timePoints[0] || 0;
        const week = _.find(cronElements, (ele) => ele.cronTimeUnit === 'dayOfWeek')?.timePoints || [];
        const day = _.find(cronElements, (ele) => ele.cronTimeUnit === 'dayOfMonth')?.timePoints || [];
        const month = _.find(cronElements, (ele) => ele.cronTimeUnit === 'month')?.timePoints || [];
        return {
            schedulePeriod: 
                _.filter(cronElements, (ele) => ele.cronTimeUnit === 'month').length > 0
                    ? 'YEARLY'
                    : _.filter(cronElements, (ele) => ele.cronTimeUnit === 'dayOfMonth').length > 0
                        ? 'MONTHLY'
                        : _.filter(cronElements, (ele) => ele.cronTimeUnit === 'dayOfWeek').length > 0
                            ? 'WEEKLY'
                            : 'DAILY',
            execTime: new Date(
                date.getFullYear(),
                date.getMonth(),
                date.getDate(),
                hour,
                minute,
                second
            ),
            execWeek: week,
            execDay: day,
            execMonth: month
        }
    }

    // 新建任务
    onCreate(event: Event) {
        event.stopPropagation();
        const {deployId, boxName} = this.data.get('');
        const dialog = new createBackup({
            data: {
                deployId,
                boxName,
                mode: 'new'
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            // 刷新列表数据
            Notification.success('创建备份任务成功');
            this.refresh();
        });
    }

    onEdit(event: Event, row: backupTaskItem) {
        event.stopPropagation();
        const { deployId, boxName } = this.data.get('');
        const {
            taskName,
            id,
            taskType,
            backupScope,
            execTimestamp,
            scheduled,
            cronElements,
            retentionDays,
            targetObjs
        } = row;
        const dialog = new createBackup({
            data: {
                deployId,
                boxName,
                mode: 'edit',
                detail: {
                    taskName,
                    taskType
                },
                taskId: id,
                formData: {
                    retentionDays: (retentionDays && Number(retentionDays) > 0) ? String(retentionDays) : '',
                    backupScope,
                    targetObjs,
                    ...(
                        taskType === 'ONE_TIME'
                        ? {
                            execInfo: {
                                execType: scheduled ? 'SCHEDULE' : 'IMMEDIATE',
                                execTimestamp: new Date(execTimestamp)
                            }
                        }
                        : {
                            periodicInfo: this.formatCronToPeriodicInfo(cronElements)
                        }
                    )
                },
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            // 刷新列表数据
            Notification.success('编辑备份任务成功');
            this.refresh();
        });
    }

    // 删除
    onDelete(row: backupTaskItem) {
        const {taskName, id} = row;
        const deployId = this.data.get('deployId');
        Dialog.confirm({
            content: `删除任务不会删除对应的备份文件，确认删除任务${taskName}吗？`,
            okText: '确定',
            onOk: async () => {
                await this.$http.paloPost('paloBackupTaskDelete', {taskId: id, deployId});
                Notification.success('任务已删除');
                this.refresh();
            }
        });
    }

    // 查看快照详情
    onView(event: Event, row: backupTaskItem) {
        event.stopPropagation();
        const {deployId} = this.data.get('');
        const dialog = new BackUpDetail({
            data: {
                deployId,
                ...row
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {});
    }

    async getCurrentBucket() {
        this.data.set('bucketLoading', true);
        try {
            const result = await this.$http.paloPost('paloCurrentBucket', {
                deployId: this.data.get('deployId')
            });
            this.data.set('boxName', result !== '' ? result : '未指定');
        }
        finally {
            this.data.set('bucketLoading', false);
        }
    }

    async getTableList() {
        const { pager, orderBy, order, taskType, deployId, searchName, date, lastBackupState } = this.data.get('');
        this.data.set('isFilter', false);
        const params = pickEmpty({
            order,
            orderBy,
            pageNo: pager.page,
            pageSize: pager.pageSize,
            taskType,
            deployId,
            lastBackupState,
            taskName: searchName,
            taskCreatedStartAt: getTimeStamp(date.begin),
            taskCreatedEndAt: getTimeStamp(date.end)
        });
        if (taskType || lastBackupState || searchName) {
            this.data.set('isFilter', true);
        }
        this.data.set('table.loading', true);
        this.data.set('table.datasource', []);
        const res = await this.$http.paloPost('paloBackupTaskList', params);
        this.data.set('table.datasource', res.taskList);
        this.data.set('pager.total', res?.total);
        this.data.set('table.loading', false);
    }

    // 指定存储桶
    async onRessginBox(event: Event) {
        const { deployId, boxName } = this.data.get('');
        const dialog = new RessignBucket({
            data: {
                deployId,
                bucketName: boxName === '未指定' ? '' : boxName
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            Notification.success('BOS存储桶路径已更新');
            this.getCurrentBucket();
            this.refresh();
        });
    }

    async refresh() {
        await this.getTableList();
    }

    async onPageChange(target: {value: {page: number}}) {
        this.data.set('pager.page', target.value.page);
        await this.refresh();
    }

    async onPageSizeChange(target: {value: {pageSize: number}}) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', target.value.pageSize);
        await this.refresh();
    }
}
