.tolerant-backup {
    padding: 24px;
    .s-tabs .s-tabnav .s-tabnav-scroll .s-tabnav-nav {
        border-bottom: 1px solid rgba(232, 233, 235, 1);
        .s-tabnav-nav-selected:after {
            width: auto;
        }
    }
    .s-list-page {
        background: #fff;
        .table-full-wrap {
            margin: 0;
            padding: 0;

            .operation-wrap {
                .buttons-quick-wrap {
                    height: 32px;
                }
            }
        }
    }
    .s-empty.s-empty-vertical .s-empty-desc {
        flex-direction: column;
    }
    .page-loading {
        width: 100%;
        margin-top: 100px;
    }
}

.palo-backup-list {
    .left-area {
        display: flex;
        align-items: center;

        .bucket-area {
            display: flex;
            align-items: center;
        }

        .bucket {
            .s-loading-slot-wrap {
                display: flex;
                align-items: center;
            }
        }
    }
}

.ellipsis-single {
    display: flex;
    justify-content: flex-start;
}

.ellipsis-tip__text {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bucketName {
    .ellipsis-tip__text {
        border-bottom: 1px dashed rgba(184, 186, 191, 1);
    }
}

.create-backup,
.data-restore {
    .s-form-item-label {
        width: 100px;
        text-align: right;
    }

    .s-checkbox-input {
        vertical-align: middle;
    }
}

.empty-area {
    display: flex;
    align-items: center;
}

.bucket-tip-content {
    width: 400px;
    padding: 16px;
    &-item {
        display: flex;
        margin-bottom: 16px;
        &-title {
            font-size: 12px;
            color: #5C5F66;
            line-height: 20px;
            font-weight: 400;
            width: 80px;
            flex: 0 0 auto;
        }
        &-text {
            width: 300px;
            display: block;

        }
        &:last-child {
            margin-bottom: 0;
        }
    }
}
