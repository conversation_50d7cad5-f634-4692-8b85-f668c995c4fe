import moment from 'moment';
import {BackUpTaskStateEnum} from '@common/config';
import {formateBackupStatus } from '@common/utils';

export const dateRangePickerShortcuts = [
    {
        text: '近7天',
        onClick(picker) {
            picker.setValueByShortCut({
                begin: moment().subtract('day', 7).startOf('day').toDate(),
                end: moment().endOf('day').toDate()
            });
        }
    },
    {
        text: '近14天',
        onClick(picker) {
            picker.setValueByShortCut({
                begin: moment().subtract('day', 14).startOf('day').toDate(),
                end: moment().endOf('day').toDate()
            });
        }
    },
    {
        text: '近30天',
        onClick(picker) {
            picker.setValueByShortCut({
                begin: moment().subtract('day', 30).startOf('day').toDate(),
                end: moment().endOf('day').toDate()
            });
        }
    },
];

export const filterStatus = (state: string) => {
    const displayStatus = BackUpTaskStateEnum.getTextFromAlias(state);
    return formateBackupStatus(displayStatus, state);
}

export const bucketHref = '/bos/#/bos/bucket';