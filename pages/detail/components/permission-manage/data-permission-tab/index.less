// 数据权限外层
.meta-manage-content {
    background: #fff;
    border-radius: 4px;
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;
    width: calc(~'100% - 32px');

    &-left {
        margin-right: 24px;
    }

    &-right {
        flex: 1;
    }
}

// 左侧 Tree 面板
.meta-catalog-collapse {
    min-width: 200px;
    max-width: 560px;
    width: 300px;
    height: 100%;
    position: relative;

    &-loading {
        position: absolute;
        background-color: white;
        width: 100%;
    }

    &-container {
        height: 100%;
        width: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
    }

    &-title {
        font-size: 14px;
        color: #151b26;
        line-height: 24px;
        font-weight: 500;
        display: flex;
        align-items: center;
        padding-right: 8px;
        margin-bottom: 8px;
        overflow: hidden;
        justify-content: space-between;
    }

    &-filter {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-top: 1px solid #e8e9eb;
        margin: 0 8px;
        justify-content: flex-end;
        overflow: hidden;

        &-search {
            flex: 1;
            height: 24px;
            display: flex;
            align-items: center;

            .s-input {
                border-radius: 2px;
            }
            .s-search-icon {
                padding-right: 5px;
            }
            .s-input,
            .s-input-area {
                width: 100%;
            }
        }

        &-icon {
            height: 24px;
            width: 24px;
            border: 1px solid rgba(232, 233, 235, 1);
            border-radius: 2px;
            margin-left: 8px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                border-color: #2468f2;
            }
        }
    }
}

// 行列授权抽屉
.r-and-c-perm-drawer {
    &-h4 {
        color: #151b26;
        font-size: 16px;
        font-weight: 500;
        line-height: 32px;
    }
    &-row {
        margin-bottom: 10px;
        display: flex;
        > aside {
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
        }
    }
    &-radio {
        margin-right: 10px;
    }
    &-cloumn {
        margin: 15px 0 10px;
        display: flex;
        > span {
            color: #898585;
            line-height: 32px;
            margin-left: 12px;
            font-size: 14px;
        }
    }
    .s-table-content {
        min-height: 200px;
    }
}

// 针对行权限条件编辑器
.row-predicate-editor {
    &-edit-form {
        .s-form-item {
            margin-bottom: 0;
        }
    }

    &-footer-btn {
        margin-top: 10px;
        > button {
            margin-right: 8px;
        }
    }
}
