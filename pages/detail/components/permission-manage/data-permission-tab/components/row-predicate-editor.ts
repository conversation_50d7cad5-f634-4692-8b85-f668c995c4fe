import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Tag, Tooltip, Button, Input, Form} from '@baidu/sui';
import {Edit} from '@baidu/sui-icon';
import { SanComputedProps } from 'types/global';

import '../index.less';

const klass = 'row-predicate-editor';

export default class RowPredicateEditor extends Component {
    static template = html`
        <div class="${klass}">
            <span>{{datasource || '-'}}</span>

            <s-tooltip
                s-if="editable"
                s-ref="tooltip"
                trigger="click"
                placement="bottom"
                on-visibleChange="onVisibleChange"
            >
                <edit-icon color="#2468F2" on-click="onEditPredicate"/>

                <div slot="content" class="${klass}-edit-form">
                    <s-form
                        s-ref="edit-predicate-form"
                        data="{=formData=}"
                        rules="{{rules}}"
                    >
                        <s-form-item>
                            <s-textarea
                                value="{=formData.wherePredicate=}"
                                placeholder="请输入行权限条件"
                                width="{{300}}"
                                height="{{80}}"
                            />
                        </s-form-item>
                    </s-form>
                    <div class="${klass}-footer-btn">
                        <s-button skin="primary" on-click="onConfirmEditPredicate">确定</s-button>
                        <s-button on-click="handleClose">取消</s-button>
                    </div>
                </div>
            </s-tooltip>
        </div>
    `;

    static components = {
        's-tag': Tag,
        's-tooltip': Tooltip,
        's-button': Button,
        's-textarea': Input.TextArea,
        's-form': Form,
        's-form-item': Form.Item,
        'edit-icon': Edit,
    };

    static computed: SanComputedProps = {
        current() {
            return this.data.get('datasource') || '';
        }
    };

    initData() {
        return {
            editable: false,
            formData: {
                wherePredicate: ''
            },
            rules: {
                wherePredicate: [
                    {
                        required: false,
                        message: '请输入行权限条件'
                    }
                ]
            }
        }
    }

    attached() {
        // 设置初始值
        this.data.set('formData.wherePredicate', this.data.get('current'));
    }

    onVisibleChange() {
        // 重置初始值
        this.data.set('formData.wherePredicate', this.data.get('current'));
    }

    onEditPredicate() {
        console.log('编辑行权限条件');
    }

    onConfirmEditPredicate() {
        const formData = this.data.get('formData');
        this.fire('confirmEdit', formData);
        (this.ref('tooltip') as any).data.set('visible', false);
    }

    handleClose() {
        (this.ref('tooltip') as any).data.set('visible', false);
    }
}
