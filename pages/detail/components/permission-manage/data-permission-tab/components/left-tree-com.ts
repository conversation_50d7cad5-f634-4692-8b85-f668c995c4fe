import {Component, DataTypes} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Tree2, Loading, Search, Select} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';
import {SanComputedProps} from 'types/global';
import AuthDataDialog from './authorization-data-dialog';

import '../index.less';

const klass = 'meta-catalog-collapse';

/**
 * 转换 API to Tree2 可用数据
 * @param res 后端返回 API 数据
 * @param keyprefix 节点 key 前缀，通常为 catalog.database. 格式
 * @param isLeaf 是否为叶子节点
 */
const transformResData = (res: any, keyprefix: string = '', isLeaf: boolean = false) => {
    return res.map((item: any) => ({
        key: `${keyprefix}${item.name}`,
        label: item.name,
        isLeaf,
        customprivilegeSources: item.privilegeSources
    }));
};

export default class LeftTreeCom extends Component {
    static template = html`
        <div class="${klass} main-second-sidebar drag-container ">
            <div class="${klass}-container">
                <div class="${klass}-title">
                    <s-button skin="primary" on-click="onAuthorizationData">授权数据</s-button>
                    <outlined-refresh on-click="onTreeRefresh" class="${klass}-filter-icon" />
                </div>
                <div class="${klass}-filter">
                    <s-search
                        value="{= keyword =}"
                        placeholder="{{searchPlaceholder}}"
                        on-search="onTreeSearch"
                        class="${klass}-filter-search"
                        size="small"
                    >
                        <s-select
                            slot="options"
                            width="{{145}}"
                            value="{=keywordType=}"
                            datasource="{{keywordTypeSource}}"
                        />
                    </s-search>
                </div>
                <div class="${klass}-content">
                    <s-loading loading="{{treeLoading}}" class="${klass}-loading" />
                    <s-tree
                        blockNode
                        height="{{300}}"
                        loadData="{{onLoadData}}"
                        treeData="{{treeData}}"
                        on-select="onTreeSelect"
                    ></s-tree>
                </div>
            </div>
        </div>
    `;
    static components = {
        's-tree': Tree2,
        's-button': Button,
        's-loading': Loading,
        's-search': Search,
        's-select': Select,
        'outlined-refresh': OutlinedRefresh
    };

    static DataTypes = {
        // 路由参数 route.query
        query: DataTypes.object,
        // 左侧面板Tree 选中数据
        treeSelectData: DataTypes.arrayOf(
            DataTypes.shape({
                treeLevel: DataTypes.number,
                key: DataTypes.string,
                label: DataTypes.string,
                privilegeSources: DataTypes.array
            })
        ),
        // 针对 用户详情和角色详情接口参数不同
        baseAPIParams: DataTypes.object
    };

    static computed: SanComputedProps = {
        searchPlaceholder() {
            const keywordType = this.data.get('keywordType');
            switch (keywordType) {
                case 'catalog':
                    return '搜索 catalog';
                case 'database':
                    return '搜索 catalog.database';
                case 'table':
                    return '搜索 catalog.database.table';
            }
        }
    };

    initData() {
        return {
            keywordTypeSource: [
                {text: 'Catalog', value: 'catalog'},
                {text: 'Database', value: 'database'},
                {text: '表/视图', value: 'table'}
            ],
            keyword: '',
            keywordType: 'catalog',
            treeData: [],
            treeSelectData: [],
            onLoadData: this.onLoadData.bind(this),
            treeLoading: false
        };
    }

    async attached() {
        this.onTreeRefresh();
    }

    // 授权数据
    onAuthorizationData() {
        const deployId = this.data.get('query.deployId');
        const dialog = new AuthDataDialog({
            data: {
                deployId
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            Notification.success('授权数据成功');
            this.onTreeRefresh();
        });
    }

    // 异步加载数据
    async onLoadData(treeNodeData: any) {
        const level = treeNodeData.treeLevel;

        if (level === 0) {
            return this.getDatabaseData(treeNodeData);
        } else if (level === 1) {
            const [table, view, materializedView] = await Promise.all([
                this.getTableData(treeNodeData),
                this.getViewData(treeNodeData),
                this.getMaterializedViewData(treeNodeData)
            ]);
            return [
                {
                    label: `数据表 (${table.length})`,
                    key: `${treeNodeData.key}.table`,
                    children: table
                },
                {
                    label: `视图 (${view.length})`,
                    key: `${treeNodeData.key}.view`,
                    children: view
                },
                {
                    label: `物化视图 (${materializedView.length})`,
                    key: `${treeNodeData.key}.materializedView`,
                    children: materializedView
                }
            ];
        }
    }

    // 刷新 Tree
    onTreeRefresh() {
        this.data.set('treeData', []);
        this.data.set('treeSelectData', []);
        // 存在搜索调用搜索，否则调用获取 Catalog 数据
        this.nextTick(() => {
            if (this.data.get('keyword')) {
                this.onTreeSearch();
            } else {
                this.getCatalogData();
            }
        });
    }

    // 获取 Catalog 数据
    async getCatalogData() {
        this.data.set('treeLoading', true);
        // const res = await this.$http.paloPost('engineAuthCatalogList', {
        //     deployId: this.data.get('query.deployId'),
        //     ...this.data.get('baseAPIParams')
        // });
        let res = [
            {
                name: 'hive'
            },
            {
                name: 'internal',
                privilegeSources: [
                    {
                        privilege: 'SELECT',
                        roles: [],
                        direct: true
                    },
                    {
                        privilege: 'LOAD',
                        roles: ['shd_role'],
                        direct: false
                    }
                ]
            },
            {
                name: 'internal22'
            }
        ];
        const treeData = transformResData(res, '');
        this.data.set('treeData', treeData || []);
        this.data.set('treeLoading', false);
    }

    // 获取 Database 数据
    async getDatabaseData(treeNodeData: any) {
        this.data.set('treeLoading', true);
        // const res = await this.$http.paloPost('engineAuthDatabaseList', {
        //     deployId: this.data.get('query.deployId'),
        //     catalogName:  treeNodeData.key,
        //     ...this.data.get('baseAPIParams'),
        // });
        let res = [
            {
                name: 'db1'
            },
            {
                name: 'db2'
            },
            {
                name: 'db3'
            }
        ];
        const treeData = transformResData(res, `${treeNodeData.key}.`);
        this.data.set('treeLoading', false);
        return treeData;
    }

    // 获取 Table 数据
    async getTableData(treeNodeData: any) {
        this.data.set('treeLoading', true);
        // const [catalogName, dbName] = treeNodeData.key.split('.');
        // const res = await this.$http.paloPost('engineAuthTableList', {
        //     deployId: this.data.get('query.deployId'),
        //     catalogName,
        //     dbName,
        //     ...this.data.get('baseAPIParams'),
        // });
        let res = [
            {
                name: 'table1'
            },
            {
                name: 'table2',
                privilegeSources: [
                    {
                        privilege: 'SELECT',
                        roles: [],
                        direct: true
                    },
                    {
                        privilege: 'LOAD',
                        roles: ['shd_role'],
                        direct: false
                    }
                ]
            },
            {
                name: 'table3'
            }
        ];
        const treeData = transformResData(res, `${treeNodeData.key}.table.`, true);
        this.data.set('treeLoading', false);
        return treeData;
    }

    // 获取 视图 数据
    getViewData(treeNodeData: any) {
        this.data.set('treeLoading', true);
        // const [catalogName, dbName] = treeNodeData.key.split('.');
        // const res = await this.$http.paloPost('engineAuthViewList', {
        //     deployId: this.data.get('query.deployId'),
        //     catalogName,
        //     dbName,
        //     ...this.data.get('baseAPIParams'),
        // });
        let res = [
            {
                name: 'view1'
            },
            {
                name: 'view2'
            },
            {
                name: 'view3'
            }
        ];
        const treeData = transformResData(res, `${treeNodeData.key}.view.`, true);
        this.data.set('treeLoading', false);
        return treeData;
    }

    // 获取 物化视图 数据
    getMaterializedViewData(treeNodeData: any) {
        this.data.set('treeLoading', true);
        // const [catalogName, dbName] = treeNodeData.key.split('.');
        // const res = await this.$http.paloPost('engineAuthMaterializedViewList', {
        //     deployId: this.data.get('query.deployId'),
        //     catalogName,
        //     dbName,
        //     ...this.data.get('baseAPIParams'),
        // });
        let res = [
            {
                name: 'materializedView1'
            },
            {
                name: 'materializedView2'
            },
            {
                name: 'materializedView3'
            }
        ];
        const treeData = transformResData(res, `${treeNodeData.key}.materializedView.`, true);
        this.data.set('treeLoading', false);
        return treeData;
    }

    // 树节点选择
    onTreeSelect({info}: {info: any}) {
        let treeSelectData = [];
        let nodeData = info.node.data.get().treeNodeDataV;
        let treeLevel = nodeData.isEnd.length;
        if (treeLevel === 3) {
            // PM 确定右侧只能有一行数据，后续可优化，当点击分类时，右侧全量分页展示分类中的全部节点
            return;
        }
        treeSelectData.unshift({
            treeLevel,
            key: nodeData.key,
            label: nodeData.label,
            privilegeSources: nodeData.customprivilegeSources
        });
        this.data.set('treeSelectData', treeSelectData);
    }

    // 树搜索
    onTreeSearch() {
        this.data.set('treeLoading', true);
        const {keywordType, keyword} = this.data.get('');
        // TODO: 搜索接口调用，待后端出接口文档

        console.log('keywordType :>> ', keywordType);
        console.log('keyword :>> ', keyword);
        if (!keyword) {
            this.onTreeRefresh();
            return;
        }
        // this.data.set('treeLoading', false);
    }
}
