/**
 * 管理行列权限 - Drawer
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Search, Drawer, Button, Table, Radio} from '@baidu/sui';

import '../index.less';
import {SanComputedProps} from 'types/global';

const klass = 'r-and-c-perm-drawer';
export default class RowAndColumnDrawer extends Component {
    static template = html` <template>
        <s-drawer size="{{800}}" open="{=open=}" title="{{row.label + ' 行列权限'}}" class="${klass}">
            <div class="${klass}-row">
                <h4 class="${klass}-h4">列权限</h4>
                <aside>
                    <s-radio-group
                        class="${klass}-radio"
                        value="{=radioGroupValue=}"
                        radioType="button"
                        name="radioGroupValue"
                    >
                        <s-radio label="全部" value="all" />
                        <s-radio label="继承权限" value="inherit" />
                        <s-radio label="直接授权" value="direct" />
                    </s-radio-group>
                    <s-search value="{= keyword =}" placeholder="请输入列名搜索" size="small" />
                </aside>
            </div>
            <s-table columns="{{cColumns}}" datasource="{{displayColumnTableData}}"> </s-table>
            <div class="${klass}-cloumn">
                <h4 class="${klass}-h4">行权限</h4>
                <span>仅支持编辑来源于本人的行权限</span>
            </div>
            <s-table columns="{{rColumns}}" datasource="{{displayRowTableData}}"> </s-table>
        </s-drawer>
    </template>`;

    static components = {
        's-drawer': Drawer,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-button': Button,
        's-search': Search,
        's-table': Table
    };

    static computed: SanComputedProps = {
        // 列权限表格数据
        displayColumnTableData() {
            const keyword = this.data.get('keyword');
            const radioGroupValue = this.data.get('radioGroupValue');
            const columnTableData = this.data.get('columnTableData');
            return columnTableData.filter((item: any) => {
                if (radioGroupValue === 'all') {
                    return item.name.includes(keyword);
                } else if (radioGroupValue === 'inherit') {
                    return item.privilegeSources.some((item: any) => !item.direct) && item.name.includes(keyword);
                } else if (radioGroupValue === 'direct') {
                    return item.privilegeSources.some((item: any) => item.direct) && item.name.includes(keyword);
                }
                return false;
            });
        },
        // 行权限表格数据
        displayRowTableData() {
            const rowTableData = this.data.get('rowTableData');
            return rowTableData;
        }
    };

    initData() {
        return {
            open: true,
            radioGroupValue: 'all',
            keyword: '',
            cColumns: [
                {
                    name: 'name',
                    label: '名称',
                    width: 260
                },
                {
                    name: 'privilegeSources',
                    label: '列权限'
                }
            ],
            columnTableData: [],
            rColumns: [
                {
                    name: 'name',
                    label: '来源',
                    width: 260
                },
                {
                    name: 'wherePredicate',
                    label: '行权限'
                }
            ],
            rowTableData: []
        };
    }
    attached() {
        this.getRowAndCloumnData();
    }

    // 获取行列权限数据
    async getRowAndCloumnData() {
        // const {query, row, baseAPIParams} = this.data.get('');
        // const [catalogName, dbName, tableType, tableName] = row.key.split('.');
        // const name = tableType === 'table' ? {tableName} : tableType === 'view' ? {viewName: tableName} : {materializedViewName: tableName};
        // const rowAndcolumnData = await this.$http.paloPost('engineAuthRowColumnList', {
        //     deployId: query.deployId,
        //     catalogName,
        //     dbName,
        //     ...name,
        //     ...baseAPIParams
        // });

        const res = {
            // 列权限
            columnMetas: [
                {
                    name: 'column1',
                    privilegeSources: [
                        {
                            privilege: 'SELECT',
                            roles: [],
                            direct: true
                        }
                    ]
                },
                {
                    name: 'column2',
                    privilegeSources: [
                        {
                            privilege: 'SELECT',
                            roles: [],
                            direct: true
                        }
                    ]
                }
            ],
            // 行权限
            ToRowPolicies: [
                // 对应的值
                {
                    filterType: 'RESTRICTIVE', // RESTRICTIVE / PERMISSIVE 格式
                    wherePredicate: '(`age`> 30)',
                    tpye: 'role', // 来自用户或者角色
                    name: 'shd'
                },
                {
                    filterType: 'RESTRICTIVE',
                    wherePredicate: '(`age`> 30)',
                    tpye: 'role', // 来自用户或者角色
                    name: 'shd'
                }
            ]
        };
        this.data.set('columnTableData', res.columnMetas);
        this.data.set('rowTableData', res.ToRowPolicies);
        this.data.set('treeLoading', false);
    }
}
