import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Button, Radio, Checkbox, Search} from '@baidu/sui';

const klass = 'auth-data-dialog';

export default class AuthDataDialog extends Component{
    static template = html`
    <template>
        <s-dialog
            title="授权数据"
            open="{=open=}"
            mask="{{true}}"
            class="${klass}"
            height="350"
            width="800"
            widthStrictly
            on-close="onClose">
            <div>TODO:待开发</div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button on-click="onConfirm" skin="primary">确定</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-checkbox': Checkbox,
        's-checkbox-group': Checkbox.CheckboxGroup,
        's-radio-group': Radio.RadioGroup,
        's-search': Search,
    };

    initData() {
        return {
            open: true,
        }
    }

    // 确认
    async onConfirm() {
        // TODO: 获取表单数据发送请求
        // 向父组件告知刷新 tree 数据
        this.fire('success', {});
        this.onClose();
    }
    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }
}