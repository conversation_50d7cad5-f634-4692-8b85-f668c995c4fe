
import {Component} from 'san';
import moment from 'moment';
import {html} from '@baiducloud/runtime';
import {
    Button,
    Search,
    Table,
    Pagination,
    Dialog,
    Form,
    Select
} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';


const klass = 'granting-roles-com';

export default class GrantingRolesCom extends Component {
    static template = html`
        <div class="${klass}">
            <div class="${klass}-header">
                <div class="${klass}-header-left">
                    <s-button skin="primary" on-click="onGrantRole" width="{{50}}">授予角色</s-button>
                </div>
                <div class="${klass}-header-right">
                    <s-search placeholder="请输入角色名称搜索" on-search="onSearch" />
                    <s-button on-click="onRefresh" class="refresh-btn">
                        <outlined-refresh />
                    </s-button>
                </div>
            </div>

            <div class="${klass}-content">
                <s-table 
                    columns="{{columns}}"
                    datasource="{{displayRoles}}"
                    on-filter="onFilter"
                >
                    <span slot="c-operation" on-click="onRevokeGrantRole(row)">撤销授权</span>
                </s-table>
                <s-pagination
                    s-if="displayRoles.length > 0"
                    layout="{{'pageSize, pager, go'}}"
                    total="{{pager.totalCount}}"
                    page="{{pager.pageNo}}"
                    page-size="{{pager.pageSize}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange">
                </s-pagination>
            </div>
            <s-dialog
                width="520"
                class="show-grant-dialog"
                open="{{showGrantDialog}}"
                title="授予角色"
                on-close="onCloseGrantDialog"
                on-confirm="onConfirmGrantDialog"
            >
                <s-form
                    ref="grant-roles-form"
                    data="{=formData=}"
                    rules="{{rules}}"
                    label-align="left"
                >
                    <s-form-item label="授权角色：">
                        <s-select
                            multiple
                            taggable
                            clearable
                            value="{{grantRoles}}"
                            datasource="{{selectRolesOptions}}"
                            width="{{352}}"
                        />
                        <p slot="extra">
                            如需创建新的角色，请前往 
                            <a href="javascript:void(0);" on-click="onCreateRole">创建角色</a>
                            ，创建完成后请点击 
                            <a href="javascript:void(0);" on-click="onRefreshRoles">刷新</a>
                        </p>
                    </s-form-item>
                </s-form>
            </s-dialog> 
        </div>
    `;

    static components = {
        's-button': Button,
        's-search': Search,
        's-table': Table,
        's-pagination': Pagination,
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        'outlined-refresh': OutlinedRefresh
    };

    static computed = {
        displayRoles() {
            return []
        },
        selectRolesOptions() {
            return []
        }
    };

    initData() {
        return {
            showGrantDialog: false,
            columns: [
                {
                    name: 'name',
                    label: '角色名称',
                },
                {
                    name: 'source',
                    label: '来源',
                    filterable: true
                },
                {
                    name: 'createTime',
                    label: '授权时间',
                    sortable: true,
                    render(row: any) {
                        return moment(row.createTime).format('YYYY-MM-DD HH:mm:ss');
                    }
                },
                {
                    name: 'operation',
                    label: '操作'
                },
            ]
        }
    }

    onGrantRole() {
        this.data.set('showGrantDialog', true);
    }

    onSearch() {

    }

    onRefresh() {

    }

    onCloseGrantDialog() {
        this.data.set('showGrantDialog', false);
    }

    onConfirmGrantDialog() {
        this.data.set('showGrantDialog', false);
    }

    onRevokeGrantRole(row: any) {
        Dialog.confirm({
            title: '撤销授权',
            content: `撤销"${row.roleName}"后无法恢复，请确认是否删除？`,
            onOk: () => {
                
            },
        });
    }
}