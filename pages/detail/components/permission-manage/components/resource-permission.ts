
import {Component} from 'san';
import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {
    Button,
    Radio,
    Search,
    Select,
    Table,
    Pagination,
    Tag,
    Dialog,
    Form,
} from '@baidu/sui';
import {OutlinedRefresh, Edit} from '@baidu/sui-icon';
import PrivilegeTags from './privilege-tags';
import {grantType} from '../types';

const klass = 'resource-permission-com';

export default class ResourcePermissionCom extends Component {
    static template = html`
        <div class="${klass}">
            <div class="${klass}-header">
                <div class="${klass}-header-left">
                    <s-button skin="primary" on-click="onGrantResource" width="{{50}}">授权资源</s-button>
                </div>
                <div class="${klass}-header-right">
                    <s-radio-group
                        radioType="button"
                        datasource="{{privsPointSourceOptions}}"
                        value="{=filterSource=}"
                    />

                    <s-search value="{=keyword=}"
                        placeholder="请输入名称搜索"
                        on-search="onSearch"
                    >
                        <s-select
                            slot="options"
                            width="{{130}}"
                            value="{=keywordType=}"
                            datasource="{{keywordTypeList}}"
                        />
                    </s-search>

                    <s-button on-click="onRefresh">
                        <outlined-refresh />
                    </s-button>
                </div>
            </div>

            <div class="${klass}-content">
                <s-table
                    columns="{{columns}}"
                    datasource="{{displayResources}}"
                    has-Expand-Row="{{true}}"
                    expandedIndex="{{expandedIndex}}"
                >
                        <s-table
                            slot="sub-table"
                            columns="{{columns}}"
                            datasource="{{row.objects}}"
                        >
                            <div slot="c-privileges">
                                <privilege-tags
                                    datasource="{{row.privileges}}"
                                    editable="{{true}}"
                                    type="${grantType.RESOURCE}"
                                    isUser="{{isUser}}"
                                    on-edit="onEditPrivileges(row)"
                                    on-confirmEdit="onConfirmEditPrivileges"
                                />
                            </div>
                        </s-table>

                        <span slot="c-privileges">-</span>

                </s-table>
                <s-pagination
                    s-if="displayResources.length > 0"
                    layout="{{'pageSize, pager, go'}}"
                    total="{{pager.totalCount}}"
                    page="{{pager.pageNo}}"
                    page-size="{{pager.pageSize}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange">
                </s-pagination>
            </div>
            <s-dialog
                width="700"
                class="grant-resource-dialog"
                open="{{showGrantResourceDialog}}"
                title="授权资源"
                on-close="onCloseGrantResourceDialog"
                on-confirm="onConfirmGrantResourceDialog"
            >
                <s-form
                    ref="grant-resource-form"
                    data="{{formData}}"
                    rules="{{rules}}"
                    label-align="left"
                >
                    <s-form-item label="授权对象：">
                        <s-radio-group
                            radioType="button"
                            datasource="{{keywordTypeList}}"
                            value="{{grantTarget}}"
                            on-change="onGrantTargetChange"
                        />
                    </s-form-item>

                    <s-form-item label="选择对象：">
                        <s-select
                            multiple
                            taggable
                            clearable
                            value="{{grantResources}}"
                            datasource="{{[]}}"
                            width="{{532}}"
                        />
                    </s-form-item>

                    <s-form-item label="权限点：">
                        <s-select
                            multiple
                            taggable
                            clearable
                            value="{{grantPrivs}}"
                            datasource="{{[]}}"
                            width="{{532}}"
                        />
                    </s-form-item>
                </s-form>
            </s-dialog> 
        </div>
    `;

    static components = {
        's-button': Button,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-search': Search,
        's-select': Select,
        's-table': Table,
        's-pagination': Pagination,
        's-tag': Tag,
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        'outlined-refresh': OutlinedRefresh,
        'edit-icon': Edit,
        'privilege-tags': PrivilegeTags,
    };

    static computed = {
        displayResources() {
            const resources = this.data.get('resources');
            const filterSource = this.data.get('filterSource');
            return _.map(resources, (group: any) => {
                return {
                    ...group,
                    objects: _.map(group.objects, (obj: any) => {
                        return {
                            ...obj,
                            privileges: _.filter(obj.privileges, (privilege: any) => {
                                if (filterSource === 'roles') {
                                    return privilege.roles.length > 0;
                                } else if (filterSource === 'direct') {
                                    return privilege.direct;
                                } else {
                                    return privilege;
                                }
                            })
                        }
                    })
                }
            })
        },
        // 全部默认展开
        expandedIndex() {
            const length = this.data.get('displayResources')?.length || 0;
            return Array.from({length}, (_, i) => i);
        }
    };

    initData() {
        return {
            resources: [
                {
                    name: 'ComputerGroup',
                    privileges: [],
                    subSlot: 'sub-table',
                    objects: [
                        {
                            name: 'Group1',
                            resourceGroup: 'ComputerGroup',
                            privileges: [
                                {
                                    privilege: 'GRANT',
                                    roles: [],
                                    direct: true
                                },
                                {
                                    privilege: 'USAGE',
                                    roles: ['Role1'],
                                    direct: false
                                }
                            ]
                        },
                        {
                            name: 'Group2',
                            resourceGroup: 'ComputerGroup',
                            privileges: []
                        },
                        {
                            name: 'Group3',
                            resourceGroup: 'ComputerGroup',
                            privileges: []
                        },
                    ]
                },
                {
                    name: 'WorkoadGroup',
                    privileges: [],
                    subSlot: 'sub-table',
                    objects: [
                        {
                            name: 'Group3',
                            resourceGroup: 'WorkoadGroup',
                            privileges: []
                        },
                        {
                            name: 'Group4',
                            resourceGroup: 'WorkoadGroup',
                            privileges: []
                        },
                        {
                            name: 'Group5', 
                            resourceGroup: 'WorkoadGroup',
                            privileges: []
                        },
                    ]
                }
            ],
            privsPointSourceOptions: [
                {
                    label: '全部',
                    value: 'all'
                },
                {
                    label: '继承角色',
                    value: 'roles'
                },
                {
                    label: '直接授权',
                    value: 'direct'
                }
            ],
            columns: [
                {
                    name: 'name',
                    label: '资源对象',
                    width: '50%',
                },
                {
                    name: 'privileges',
                    label: '权限点',
                    width: '50%'
                }
            ],
            filterSource: 'all',
            keywordTypeList: [
                {
                    label: 'ComputerGroup',
                    value: 'ComputerGroup'
                },
                {
                    label: 'WorkloadGroup',
                    value: 'WorkloadGroup'
                }
            ],
            keywordType: 'ComputerGroup',
            grantTarget: 'ComputerGroup'
        }
    }

    attached() {
        this.getResourceList();
    }

    async getResourceList() {
        const { deployId, userName } = this.data.get('query');
        const { grantMeta } = this.data.get('detail');
        const params = {
            deployId,
            userName,
            host: grantMeta?.host
        };
        const res = await this.$http.paloPost('listResourceAuth', params);
    }

    onGrantResource() {
        this.data.set('showGrantResourceDialog', true);
    }

    onFilterSourceChange() {
        
    }

    onSearch() {
        
    }

    onRefresh() {
        
    }

    onCloseGrantResourceDialog() {
        this.data.set('showGrantResourceDialog', false);
    }

    onConfirmGrantResourceDialog() {
        this.data.set('showGrantResourceDialog', false);
    }

    onEditPrivileges(row: any) {
        console.log('row: ', row);
    }

    onConfirmEditPrivileges(data: any) {
        console.log('get data: ', data);
    }
}