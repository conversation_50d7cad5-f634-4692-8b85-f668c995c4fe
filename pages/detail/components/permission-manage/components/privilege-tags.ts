import {Component} from 'san';
import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Tag, Tooltip, Button, Select} from '@baidu/sui';
import {Edit} from '@baidu/sui-icon';
import {grantType} from '../types';

const klass = 'privilege-tags';

export default class PrivilegeTags extends Component {
    static template = html`
        <div class="${klass}">
            <template s-if="datasource.length > 0">
                <template s-for="item in datasource">
                    <s-tag border>{{item.privilege}}</s-tag>
                </template>
            </template>
            <span class="ellipsis" s-else>-</span>

            <s-tooltip
                s-if="editable"
                s-ref="tooltip"
                trigger="click"
                placement="bottom"
                on-visibleChange="onVisibleChange"
            >
                <edit-icon color="#2468F2" on-click="onEditPrivileges(datasource)"/>

                <div slot="content" class="edit-privileges">
                    <s-form
                        s-ref="edit-privileges-form"
                        data="{=formData=}"
                        rules="{{rules}}"
                    >
                        <s-form-item label="">
                            <s-select
                                multiple
                                taggable
                                clearable
                                checkAll
                                value="{=formData.privileges=}"
                                width="{{532}}"
                            >
                                <s-option
                                    s-for="item in optionalPrivileges"
                                    value="{{item.value}}"
                                    label="{{item.label}}"
                                    disabled="{{item.disabled}}"
                                >
                                    <span class="privilege-option-label" style="float: left">{{ item.label }}</span>
                                    <span s-if="item.roles" class="privilege-option-roles" style="float: right; color: #B8BABF; font-size: 13px">{{ '权限来自于' + item.roles }}</span>
                                </s-option>
                            </s-select>
                        </s-form-item>
                    </s-form>

                    <div class="footer">
                        <s-button skin="primary" on-click="onConfirmEditPrivileges">确定</s-button>
                        <s-button on-click="handleClose">取消</s-button>
                    </div>
                </div>
            </s-tooltip>
        </div>
    `;

    static components = {
        's-tag': Tag,
        's-tooltip': Tooltip,
        's-button': Button,
        's-select': Select,
        's-option': Select.Option,
        'edit-icon': Edit,
    };

    static computed = {
        optionalPrivileges(): string[] {
            // @ts-ignore 区分全局
            const isGlobal = this.data.get('isGlobal');
            // @ts-ignore 区分数据权限（data）与资源权限（resource）
            const type = this.data.get('type');
            // @ts-ignore 区分用户（user）与角色（role）
            const isUser = this.data.get('isUser');
            // @ts-ignore 当前权限点
            const datasource = this.data.get('datasource');
            // @ts-ignore 所有权限点
            const privileges = this.data.get('privileges');

            // 过滤数据权限和资源权限
            const options = _.filter(privileges, (privilege: {value: string, type: string}) => {
                return privilege.type === type;
            });

            let data: any = _.cloneDeep(options);
            console.log('data1==', data);
            // 过滤用户和角色
            // 逻辑混乱，可以优化
            if (isUser) {
                // 非Global的数据权限没有ADMIN
                data = !isGlobal
                    ? _.filter(options, (privilege: {value: string}) => {
                        return privilege.value !== 'ADMIN';
                    })
                    : data;
            } else {
                // 角色的资源权限没有GRANT
                data = type === grantType.RESOURCE
                    ? _.filter(options, (privilege: {value: string}) => {
                        return privilege.value !== 'GRANT';
                    })
                    : data;
            }

            // @ts-ignore
            return _.map(data, (item: {value: string, label: string, disabled?: boolean, roles?: string[]}) => {
                const res = item;

                // 继承角色的权限点不能修改
                const fromRoles = _.find(datasource, (source: {privilege: string, roles: string[]}) => {
                    return source.privilege === item.value && source.roles.length > 0
                });
                if (fromRoles) {
                    res.disabled = true;
                    res.roles = fromRoles?.roles || [];
                }
                return res;
            });

            return [];
        },
        current() {
            return _.map(this.data.get('datasource'), (item: {privilege: string}) => {
                return item.privilege
            });
        }
    }

    initData() {
        return {
            privileges: [
                {value: 'ADMIN', label: 'ADMIN', type: 'data'},
                {value: 'GRANT', label: 'GRANT', type: 'data'},
                {value: 'SELECT', label: 'SELECT', type: 'data'},
                {value: 'LOAD', label: 'LOAD', type: 'data'},
                {value: 'ALTER', label: 'ALTER', type: 'data'},
                {value: 'CREATE', label: 'CREATE', type: 'data'},
                {value: 'DROP', label: 'DROP', type: 'data'},
                {value: 'USAGE', label: 'USAGE', type: 'resource'},
                {value: 'GRANT', label: 'GRANT', type: 'resource'},
            ],
            editable: false,
            // 是否是全局
            isGlobal: true,
            // 区分数据权限（data）与资源权限（resource）
            type: 'data'
        }
    }

    attached() {
        // 设置初始值
        this.data.set('formData.privileges', this.data.get('current'));
    }

    onVisibleChange() {
        // 重置初始值
        this.data.set('formData.privileges', this.data.get('current'));
    }

    onConfirmEditPrivileges() {
        this.fire('confirmEdit', this.data.get('formData'));
        this.ref('tooltip').data.set('visible', false);
    }

    handleClose() {
        this.ref('tooltip').data.set('visible', false);
    }
}