/*
 * @Author: <EMAIL>
 * @Date: 2025-09-18 17:50:24
 * @LastEditTime: 2025-09-18 18:19:16
 */
import {Component} from "san"
import {html} from '@baiducloud/runtime';
import {Up, Down} from '@baidu/xicon-san';
export default class DrapShow extends Component {
  static template = html`
    <template>
      <div on-click="changeDirect">
        <s-up s-if="curDirect !=='down'" class="iconfont icon-up" size="{{size}}"></s-up>
        <s-down s-else class="iconfont icon-down" size="{{size}}"></s-down>
      </div>
    </template>
    `

  static components = {
    's-up': Up,
    's-down': Down,
  }

  initData() {
    return {
      curDirect: 'down'
    }
  }

  changeDirect() {
    this.data.set('curDirect', this.data.get('curDirect') === 'down' ? 'up' : 'down');
    this.nextTick(() => {
      this.fire('change', this.data.get('curDirect'));
    })
  }
}