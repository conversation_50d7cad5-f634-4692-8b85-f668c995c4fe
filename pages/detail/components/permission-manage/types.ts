export enum userStatus {
    ACTIVE = 'ACTIVE',
    LOCK = 'LOCK'
  }
  
export const statusInfo = {
    [userStatus.ACTIVE]: '正常',
    [userStatus.LOCK]: '锁定'
}

export enum userOrigin {
    SYSTEM = 'SYSTEM',
    USER = 'USER'
}

export const originInfo = {
    [userOrigin.SYSTEM]: '系统',
    [userOrigin.USER]: '用户'
}

export enum targetType {
    USER = 'user',
    ROLE = 'role'
}

export enum grantType {
    DATA = 'data',
    RESOURCE = 'resource'
}
