/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-09-16 11:29:57
 * @LastEditTime: 2025-09-22 14:26:43
 */
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Tabs, Notification} from '@baidu/sui';
import './index.less';
import {serializeQueryEncode} from '@common/utils';

// 用户列表
import AuthUser from './auth-user';
// 角色列表
import AuthRole from './auth-role';
// 用户详情
import UserDetailIndex from './detail/user-detail-index';
// 角色详情
import RoleDetailIndex from './detail/role-detail-index';

const klass = 'palo-permission-manage';

const PageType = { // 页面类型
    USER_LIST: 'userlist',
    ROLE_LIST: 'rolelist',
    USER_DETAIL: 'userdetail',
    ROLE_DETAIL: 'roledetail'
};


export default class PermissionManage extends Component {
    static template = html`
        <div class="${klass}">
            <!-- 用户 & 角色列表 -->
            <div s-if="{{isShowList}}">
                <s-tabs active="{{query.type || PageType.USER_LIST}}" on-change="onTabChange">
                    <s-tab-pane label="用户" key="{{PageType.USER_LIST}}}">
                        <template s-if="!query.type || query.type === PageType.USER_LIST">
                            <s-auth-user
                                isLoading="{{isLoading}}"
                                deployId="{{query.deployId}}"
                                query="{{query}}"
                                version="{{versionInfo}}"
                            ></s-auth-user>
                        </template>
                    </s-tab-pane>

                    <s-tab-pane label="角色" key="{{PageType.ROLE_LIST}}">
                        <template s-if="query.type === PageType.ROLE_LIST">
                            <s-auth-role
                                isLoading="{{isLoading}}"
                                deployId="{{query.deployId}}"
                                query="{{query}}"
                                version="{{versionInfo}}"
                            ></s-auth-role>
                        </template>
                    </s-tab-pane>
                </s-tabs>
            </div>
            <!-- 用户 & 角色详情 -->
            <div s-else>
                    <template s-if="query.type === PageType.USER_DETAIL">
                        <user-detail-index query="{{query}}" version="{{versionInfo}}"/>
                    </template>
                    <template s-if="query.type === PageType.ROLE_DETAIL">
                        <role-detail-index query="{{query}}" version="{{versionInfo}}"/>
                    </template>
            </div>
        </div>
    `;

    static components = {
        's-tabs': Tabs,
        's-tab-pane': Tabs.TabPane,
        's-auth-user': AuthUser,
        's-auth-role': AuthRole,
        'user-detail-index': UserDetailIndex,
        'role-detail-index': RoleDetailIndex,
    };

    static computed = {
        isShowList() {
            const query = this.data.get('query');
            return ![PageType.USER_DETAIL, PageType.ROLE_DETAIL].includes(query.type);
        },
        versionInfo() {
            const modules = this.data.get('detail.modules');
            const version: string = modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
            return version;
        }
    };

    initData() {
        return {
            PageType
        }
    }

    startDoThing() {
        const deployId = this.data.get('query.deployId');
        const version = this.data.get('versionInfo');
        if (!deployId || !version) {
            Notification.error('deployId 或 version 参数缺失');
            return;
        }
        const query = this.data.get('query');
        // 初始化如果是详情页，直接跳转到列表页
        // if ([PageType.USER_DETAIL, PageType.ROLE_DETAIL].includes(query.type)) {
        //     const query = serializeQueryEncode({
        //         deployId,
        //         page: 5,
        //         type: PageType.USER_LIST
        //     });
        //     redirect(`#/palo/detail?${query}`);
        // }
    }

    async attached() {
        if (this.data.get('isLoading')) {
            this.watch('isLoading', async () => {
                this.startDoThing();
            });
        } else {
            this.startDoThing();
        }
    }
    updated() {
        // 监听路由参数变化，根据 query 内容，调整组件渲染
        console.log('query, updated==', this.data.get('query'));
    }

    onTabChange(target: any) {
        const query = serializeQueryEncode({
            ...this.data.get('query'),
            page: 5,
            type: target.value?.key || PageType.USER_LIST,
        });
        redirect(`#/palo/detail?${query}`);
    }
}
