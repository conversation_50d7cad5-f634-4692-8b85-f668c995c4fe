/**
 * 权限管理 - 角色详情
 * <AUTHOR>
 */


/**
 * 权限管理 - 用户详情
 * <AUTHOR>
 */
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {AppDetailPage, AppTabPage} from '@baidu/sui-biz';
import { serializeQueryEncode } from '@common/utils';

const klass = 'permission-detail';

const template = html`
    <template>
        <app-detail-page class="${klass}" pageTitle="{{'角色管理'}}" backTo="{{backTo}}">
            <div>1111</div>
        </app-detail-page>
    </template>
`;

export default class PaloDetail extends AppDetailPage {
    static template = template;
    static components = {
        'app-detail-page': AppDetailPage,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPane
    };

    static computed = {
        backTo() {
            const query = this.data.get('query');
            const {deployId, page = 5, version} = query;
            const queryParams = serializeQueryEncode({
                deployId,
                page,
                version,
                type: 'rolelist'
            });
            return {
                text: '返回',
                url: `#/palo/detail?${queryParams}`
            };
        }
    };

    initData() {
        return {};
    }

     attached() {
        console.log('role-detail-index, attached==', this.data.get('query'));
    }
}
