.permission-detail {
    .s-detail-page-title {
        padding-left: 0;
    }
    .s-detail-page-content {
        margin: 0;
    }
    .detail-name {
        .instance-editor {
            display: none;
        }

        &:hover {

            .instance-editor {
                display: inline-block;
            }
        }
    }

    &-instanceinfo {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    &-instancename {
        display: block;
        margin-left: 16px;
        font-size: 16px;
        color: var(--titleColor);
        font-weight: 500;
    }

    &-baseinfo {
        margin-top: 16px;
    }

    .app-tab-page {
        padding: 0;
        overflow-x: hidden;
    }

    &-button {
        display: flex;
    }

    &-button .s-button {
        padding: 0 17.5px;
        display: flex;
    }

    &-status {
        margin-left: 12px;
        width: 70px;
    }

    &-left {
        display: flex;
        align-items: center;
    }

    &-right {
        display: flex;
        align-items: center;
    }

    .ellipsis {
        max-width: 95%;
        display: inline-block;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
    }

    .table-btn-slim {
        padding: 0  10px 0 0 !important
    }

    .passreset {
        margin-top: -2px;
    }

    .granting-roles-com,
    .resource-permission-com {
        &-header { 
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;

            &-right {
                display: flex;
                align-items: center;
                .s-radio-button-group {
                    margin-right: 8px;

                    .s-radio-text {
                        display: inline-block;
                        width: 60px;
                    }

                    .s-radio-checked {
                        .s-radio-text {
                            background-color: #E6F0FF;
                        }
                    }
                    
                }

                .s-search {
                    margin-right: 8px;

                    .s-input-prefix-container {
                        padding: 0 12px;
                    }
                }

                .refresh-btn {
                    padding: 0 7px;
                }

            }
        }

        &-content {
            .s-table {
                .s-table-subrow-wrapper {
                    padding: 0 0 0 40px;
                    background-color: #FFF;

                    .s-table-header  {
                        display: none;
                    }

                    .s-table-cell-text {
                        > div {
                            display: flex;
                            align-items: center;

                            .ellipsis {
                                margin: 0 4px;
                            }

                            edit-icon {
                                margin-left: 4px;
                            }
                        }
                    }
                }
            }

            .s-tag {
                margin: 0 4px;
                background-color: #FFF;
            }

            .s-pagination {
                padding: 16px 0;    
                .s-pagination-wrapper {
                    justify-content: flex-end;
                }
            }

        }
    }

    .privilege-tags {
        display: inline-flex;
        align-items: center;
    }
}

.edit-privileges {
    .s-option {
        align-items: center;

        .privilege-option-label {
            float: left;
        }

        .privilege-option-roles {
            float: right;
            color: #B8BABF;
            font-size: 13px;
        }
    }
    .s-checkbox {
        display: inline-flex;
        align-items: center;
    }

    .s-select-check-all .s-checkbox {
        height: 30px;
    }

    .footer {
        padding: 12px 12px 0;

        .s-button:first-child {
            margin-right: 8px;
        }
    }
}
