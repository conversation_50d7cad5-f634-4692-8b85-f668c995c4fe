/**
 * 权限管理 - 用户详情
 * 注意：
 *  ‼️ UserDetailIndex 和 RoleDetailIndex 代码逻辑基本一致
 *  ‼️ 代码相似是为了提高可扩展性，但维护性需要分开维护，如有改动需要同步修改
 * <AUTHOR>
 */
import {html, redirect} from '@baiducloud/runtime';
import {Button, Tabs, Notification, Dialog} from '@baidu/sui';
import {AppDetailPage, AppTabPage, AppDetailCell} from '@baidu/sui-biz';
import {serializeQueryEncode} from '@common/utils';
import {OutlinedLeft} from '@baidu/sui-icon';

// 授予角色详情页
import GrantingRolesCom from '../components/granting-roles';
// 数据权限详情页
import DataPermissionCom from '../data-permission-tab/index-data-permission';
// 资源权限详情页
import ResourcePermissionCom from '../components/resource-permission';

import UserDialog from '../dialogs/user-dialog';
import RestPwdDialog from '../dialogs/rest-pwd-dialog';
import './user-and-role.less';

const klass = 'permission-detail';

const template = html`
    <template>
        <app-detail-page class="${klass}">
            <div slot="pageTitle" class="${klass}-instanceinfo">
                <div class="${klass}-left">
                    <span class="back-btn" on-click="onBack">
                        <outlined-left width="{{16}}" />
                    </span>
                    <h4 class="${klass}-instancename">{{query.userName + ' 用户管理'}}</h4>
                </div>
                <div class="${klass}-right">
                    <div class="${klass}-button">
                        <s-button on-click="operateUser('resetPwd')" class="mr8">重置密码</s-button>
                        <s-button on-click="operateUser('edit')" class="mr8">编辑</s-button>
                        <s-button on-click="operateUser('delete')" class="mr8">删除</s-button>
                    </div>
                </div>
            </div>
            <div class="${klass}-baseinfo">
                <app-detail-cell datasource="{{baseInfoDatasource}}" divide="3" labelWidth="120px" />
            </div>
            <s-tabs active="{{operateTabValue}}" on-change="onOperateTabChange">
                <s-tab-pane label="授予角色" key="{{0}}">
                    <template s-if="operateTabValue === 0 ">
                        <granting-roles-com query="{{query}}" isUser detail="{{detailData}}" />
                    </template>
                </s-tab-pane>
                <s-tab-pane label="数据权限" key="{{1}}">
                    <template s-if="operateTabValue === 1">
                        <data-permission-com query="{{query}}" isUser />
                    </template>
                </s-tab-pane>
                <s-tab-pane label="资源权限" key="{{2}}">
                    <template s-if="operateTabValue === 2">
                        <resource-permission-com query="{{query}}" isUser detail="{{detailData}}" />
                    </template>
                </s-tab-pane>
            </s-tabs>
        </app-detail-page>
        <s-user-dailog 
            s-if="showDialog" 
            deployId="{{query.deployId}}" 
            version="{{version}}" 
            rowData="{{editAndRestUserData}}" 
            on-close="closeDialog">
        </s-user-dailog>
        <s-reset-pwd-dailog
            s-if="showResetPwdDialog"
            deployId="{{query.deployId}}"
            rowData="{{editAndRestUserData}}"
            on-close="closeResetPwdDialog"
        >
        </s-reset-pwd-dailog>
    </template>
`;

export default class UserDetailIndex extends AppDetailPage {
    static template = template;
    static components = {
        'app-detail-page': AppDetailPage,
        'app-detail-cell': AppDetailCell,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPane,
        's-button': Button,
        'outlined-left': OutlinedLeft,
        's-tabs': Tabs,
        's-tab-pane': Tabs.TabPane,
        'granting-roles-com': GrantingRolesCom,
        'data-permission-com': DataPermissionCom,
        'resource-permission-com': ResourcePermissionCom,
        's-user-dailog': UserDialog,
        's-reset-pwd-dailog': RestPwdDialog
    };

    static computed = {
        backTo() {
            const query = this.data.get('query');
            const {deployId, page = 5, version} = query;
            const queryParams = serializeQueryEncode({
                deployId,
                page,
                version,
                type: 'userlist'
            });
            let obj = {
                text: '返回',
                url: `/palo/#/palo/detail?${queryParams}`
            };
            return obj;
        },
        baseInfoDatasource(): {label: string; value: string}[] {
            const detailData = this.data.get('detailData');
            const {mysqlUserTableInfo, status} = detailData;
            return [
                {
                    label: '用户名：',
                    value: mysqlUserTableInfo?.user || '-'
                },
                {
                    label: '主机：',
                    value: mysqlUserTableInfo?.host || '-'
                },
                {
                    label: '状态：',
                    value: status
                },
                {
                    label: '来源：',
                    value: ''
                },
                {
                    label: '描述：',
                    value: ''
                },
                {
                    label: '密码有效期：',
                    value: ''
                },
                {
                    label: '禁止历史密码：',
                    value: ''
                },
                {
                    label: '密码错误锁定次数：',
                    value: mysqlUserTableInfo?.failedLoginCounter || '-'
                }
            ]
        },
        editAndRestUserData() {
            const detailData: any = this.data.get('detailData');
            const {grantMeta, mysqlUserTableInfo} = detailData;
            return {
                user: grantMeta?.user,
                host: grantMeta?.host,
                comment: grantMeta?.comment,
                mysqlUserTableInfo
            };
        },
    };

    initData() {
        return {
            operateTabValue: 1, // 详情页 tab, 默认 0 为「授予角色」
            loading: false,
            detailData: {},
            showDialog: false,
            showResetPwdDialog: false,
        };
    }

    async attached() {
        console.log('user-detail-index, attached==', this.data.get('query'));
        await this.getClusterDetail();
    }
    // 获取集群详情
    async getClusterDetail() {
        const {deployId, userName, host} = this.data.get('query');
        if (!deployId) {
            Notification.error('缺少集群ID，请返回列表页重试');
            return;
        }
        const params = {
            deployId,
            userName,
            host
        };
        this.data.set('loading', true);
        const detailData = await this.$http.paloPost('engineAuthUserDetail', params);
        this.data.set('detailData', detailData || {});
        this.data.set('loading', false);
    }

    // 详情页tab切换
    onOperateTabChange(target: {value: {key: string}}) {
        this.data.set('operateTabValue', target.value.key);
    }

    // 返回
    onBack() {
        const backTo = this.data.get('backTo');
        redirect(backTo.url);
    }

    // 操作「重置密码」or 「编辑」or 「删除」
    operateUser(type: 'resetPwd' | 'edit' | 'delete') {
        console.log('type==', type);
        if (type === 'resetPwd') {
            this.data.set('showResetPwdDialog', true);
        } else if (type === 'edit') {
            this.data.set('showDialog', true);
        } else {
            this.onDeleteUser(this.data.get('editAndRestUserData'));
        }
    }

    /**
  * 删除用户时触发的函数
  */
    async onDeleteUser(rowData: any) {
        const {user, host} = rowData;
        const {deployId} = this.data.get("query");
        if (!deployId || !user || !host) {
            Notification.error('请求必填参数缺失');
            return;
        }
        await Dialog.confirm({
            content: `"${rowData.user}"删除后无法恢复，请确认是否删除？`,
            title: '删除',
            onOk: async () => {
                try {
                    await this.$http.paloPost('engineAuthUserDelete', {
                        deployId,
                        userName: user,
                        host: host
                    });
                    Notification.success('删除成功');
                    await this.getAuthUserList();
                } catch (e) {
                    Notification.error('删除失败，请重新尝试');
                }
            }
        });
    }

    async closeDialog() {
        this.data.set('showDialog', false);
    }

    closeResetPwdDialog() {
        this.data.set('showResetPwdDialog', false);
    }
}
