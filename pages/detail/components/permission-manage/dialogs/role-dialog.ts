import {Component} from "san"
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Input, Notification} from '@baidu/sui'
import './index.less';
export default class UserDialog extends Component {
  static template = html`
  <template>
    <s-dialog
      class="dialog-container"
      width="600"
      title="{{mode[dialogMode]}}角色"
      open="{= open =}"
      on-confirm="confirmContent"
      on-close="onClose(false)"
      confirming="{{confirmLoading}}"
      widthStrictly
    >
      <s-form  s-ref="role-form" data="{=formData=}" rules="{{rules}}" label-align="left">
        <s-form-item 
            prop="roleName" 
            label="角色名称:"
            help="以字母开头，支持字母、数字、下划线, 长度1~64个字符"
          >
            <s-input 
              disabled="{{dialogMode === 'edit' ? true : false }}" 
              value="{=formData.roleName=}"  
              width="400"
              placeholder="请输入角色名" 
            >
            </s-input>
        </s-form-item>
        <s-form-item
            s-if="showComment"
            label="描述:"
            prop="comment">
            <s-textarea  width="405" height="80" placeholder="请输入描述信息" value="{=formData.comment=}" ></s-textarea>
        </s-form-item> 
      </s-form>
    </s-dialog>
  </template>
  `

  static components = {
    's-dialog': Dialog,
    's-form': Form,
    's-input': Input,
    's-form-item': Form.Item,
    's-textarea': Input.TextArea,
  }

  initData() {
    return {
      open: true,
      mode: {
        create: '创建',
        edit: '编辑'
      },
      formData: {
        roleName: '',
        comment: '',
      },
      rules: {
        roleName: [
          {required: true, message: '请输入用户名'},
          {
            validator(_: any, value: any, callback: any) {
              let pattern = /^[a-zA-Z][\w\-\_\/\.]{0,64}$/;
              if (!pattern.test(value)) {
                return callback('长度限制为1-65个字符，以字母开头，只允许包含字母、数字及 - _ . /');
              }
              callback();
            }
          }
        ]
      },
      confirmLoading: false,
    }
  }

  static computed = {
    showComment() {
      const version = this.data.get('version');
      const versionTemp: any = version.split('.');
      if (versionTemp[0] < 2) {
        return false;
      } else if (versionTemp[0] == 2) {
        return versionTemp[1] >= 1 ? true : false;
      }
      return true;
    },
    dialogMode() {
      const rowData: any = this.data.get('rowData');
      return Object.keys(rowData).length ? 'edit' : 'create';
    }
  }

  inited() {
    const rowData = this.data.get('rowData');
    if (Object.keys(rowData).length <= 0) {
      return;
    }
    // 关于安全策略的内容，后端无法返回，所以这里都是默认值。
    const dataTemp = {
      roleName: rowData.name,
      comment: rowData.comment
    }
    this.data.set('formData', {...dataTemp});
  }

  /**
  * 当关闭组件时触发的事件
  *
  * @param tag 是否带有标签，默认为 false
  */
  onClose(tag = false) {
    this.fire('close', {tag})
  }

  /**
  * 确认内容
  */
  async confirmContent() {
    try {
      this.data.set('confirmLoading', true);
      await this.ref('role-form').validateFields();

      const {deployId, formData} = this.data.get('');
      if (this.data.get('dialogMode') === 'create') {
        await this.createRoleContent(deployId, formData);
        Notification.success('创建成功');
      } else {
        await this.editRoleContent(deployId, formData);
        Notification.success('编辑成功');
      }
      this.onClose(true);
    }
    catch (err) {
      console.error('confirmContent', err);
    }
    finally {
      this.data.set('confirmLoading', false);
    }
  }

  /**
  * 异步创建角色内容
  *
  * @param deployId 部署ID
  * @param formData 表单数据
  * @returns Promise，表示异步操作的结果
  */
  async createRoleContent(deployId: any, formData: any) {
    const {roleName, comment} = formData;
    const params = {
      deployId,
      roleName,
      ...comment ? {comment} : {}
    };
    await this.$http.paloPost('engineAuthRoleCreate', params);
  }

  /**
  * 编辑角色内容
  *
  * @param deployId 部署ID
  * @param formData 包含角色名和备注的表单数据
  * @returns 无返回值
  */
  async editRoleContent(deployId: any, formData: any) {
    const {roleName, comment} = formData;
    const params = {
      deployId,
      roleName,
      ...comment ? {comment} : {},
    };
    await this.$http.paloPost('engineAuthRoleEdit', params);
  }
}