import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Table, Button, Search, Pagination, Notification, Dialog} from '@baidu/sui';
import {Plus1} from '@baidu/xicon-san';
import EllipsisTip from '@components/ellipsis-tip';
import {debounce} from '@common/decorators';
import UserDialog from './dialogs/user-dialog';
import RestPwdDialog from './dialogs/rest-pwd-dialog';
import {serializeQueryEncode} from '@common/utils';
import {userOrigin, userStatus, originInfo, statusInfo} from './types';
import './index.less';

const userColumns = [
  {
    name: 'user',
    label: '用户名',
  },
  {
    name: 'host',
    label: '主机',
  },
  {
    name: 'status',
    label: '状态',
    width: 150,
    filter: {
      options: [
        {text: '全部', value: ''},
        {text: statusInfo[userStatus.ACTIVE], value: userStatus.ACTIVE},
        {text: statusInfo[userStatus.LOCK], value: userStatus.LOCK}
      ],
      value: ''
    }
  },
  {
    name: 'origin',
    label: '来源',
    width: 100,
    filter: {
      options: [
        {text: '全部', value: ''},
        {text: originInfo[userOrigin.SYSTEM], value: userOrigin.SYSTEM},
        {text: originInfo[userOrigin.USER], value: userOrigin.USER}
      ],
      value: ''
    },
    render: (item: {origin: string}) => {
      return item.origin === userOrigin.SYSTEM ? originInfo[userOrigin.SYSTEM] : originInfo[userOrigin.USER];
    }
  },
  {
    name: 'role',
    label: '授予角色',
  },
  {
    name: 'createTime',
    label: '创建时间',
    width: 150,
    render: (item: {createTime: string}) => {
      return item.createTime || '-';
    },
  },
  {
    name: 'comment',
    label: '描述',
  },
  {
    name: 'actions',
    label: '操作',
  }
];

const systemUser = ['admin', 'auditloader'];
export default class UserContent extends Component {
  static template = html`
  <template>
      <div class="content-header">
          <s-button skin="primary" on-click="onCreateUser" disabled="{{isLoading}}">
              <s-plus-1 class="button-icon mr4" is-button="{{false}}"/>新建用户
          </s-button>
          <s-search placeholder="请输入用户名搜索" on-search="onSearch" on-input="onInputSearch" clearable></s-search>
      </div>
      <s-table loading="{{table.loading}}" 
        columns="{{userColumns}}" 
        datasource="{{userList}}" 
        on-filter="onFilterData">
          <div slot="c-user">
            <s-button skin="stringfy" on-click="goToUserDetail(row)">{{row.user}}</s-button>
          </div>
          <div slot="c-status">
            <span s-if="!row.status">-</span>
            <template s-else>
              <span class="status {{row.status === userStatus.ACTIVE ? 'normal' : 'error'}}">
                {{row.status | statusDesc}}
              </span>
              <s-button skin="stringfy" s-if="row.status === userStatus.LOCK" on-click="goToUnlock(row)">解锁</s-button>
            </template>
          </div>
          <ellipsis-tip
              slot="c-role"
              content="{{row.role | roleDesc}}"
              text="{{row.role | roleDesc}}"
              placement="top"
              showTip="{{true}}"
              alwaysTip="{{false}}"
              copy="{{false}}"
          >
          </ellipsis-tip>
          <ellipsis-tip
              slot="c-comment"
              content="{{row.comment || '-'}}"
              text="{{row.comment || '-'}}"
              placement="top"
              showTip="{{true}}"
              alwaysTip="{{false}}"
              copy="{{false}}"
          >
          </ellipsis-tip>
          <div slot="c-actions">
              <s-button
                  disabled="{{row.user | isRestPwdDisable}}"
                  skin="stringfy"
                  on-click="onResetPwd(row, rowIndex)"
              >
                  重置密码
              </s-button>
              <s-button
                  skin="stringfy"
                  on-click="onEditUser(row, rowIndex)"
              >
                  编辑
              </s-button>
              <s-button
                  disabled="{{row.user | isDeleteDisable}}"
                  skin="stringfy"
                  on-click="onDeleteUser(row, rowIndex)"
              >
                  删除
              </s-button>
          </div>
      </s-table>
      <div class="pagination">
          <s-pagination
              slot="pager"
              layout="{{'pageSize, pager, go'}}"
              total="{{pager.totalCount}}"
              page="{{pager.pageNo}}"
              page-size="{{pager.pageSize}}"
              on-pagerChange="onPageChange"
              on-pagerSizeChange="onPageSizeChange">
          </s-pagination>
      </div>
      <s-user-dailog 
        s-if="showDialog" 
        deployId="{{deployId}}" 
        version="{{version}}" 
        rowData="{{curRowData}}" 
        on-close="closeDialog">
      </s-user-dailog>
      <s-reset-pwd-dailog
        s-if="showResetPwdDialog"
        deployId="{{deployId}}"
        rowData="{{resetRowData}}"
        on-close="closeResetPwdDialog"
      >
      </s-reset-pwd-dailog>
    </template>
    `;

  static components = {
    's-table': Table,
    's-button': Button,
    's-search': Search,
    's-plus-1': Plus1,
    's-pagination': Pagination,
    'ellipsis-tip': EllipsisTip,
    's-user-dailog': UserDialog,
    's-reset-pwd-dailog': RestPwdDialog
  };

  static filters = {
    statusDesc(status: any): string {
      return status === userStatus.ACTIVE ? statusInfo[userStatus.ACTIVE] : statusInfo[userStatus.LOCK];
    },
    roleDesc(data: string[]): string {
      return data && data.length > 0 ? data.join(', ') : '-';
    },
    isDeleteDisable(user: string): boolean {
      return systemUser.includes(user);
    },
    isRestPwdDisable(user: string): boolean {
      return user === systemUser[1];
    }
  }

  /**
  * 初始化数据
  *
  * @returns 返回初始化的数据对象，包含分页信息、用户列和用户列表
  */
  initData() {
    return {
      pager: {
        pageSize: 10,
        pageNo: 1,
        totalCount: 0
      },
      userColumns: userColumns,
      userList: [],
      userStatus: userStatus,
      showDialog: false,
      curRowData: {},
      showResetPwdDialog: false,
      resetRowData: {},
    }
  }

  /**
    * 获取授权用户列表
    *
    * @returns 返回授权用户列表和分页信息
    */
  @debounce(500)
  async getAuthUserList() {
    const {deployId, status, origin, searchValue} = this.data.get("");
    if (!deployId) {
      Notification.error('deployId 参数缺失');
      return;
    }
    const params = {
      deployId: deployId,
      pageNo: this.data.get('pager.pageNo'),
      pageSize: this.data.get('pager.pageSize'),
      ...status ? {status} : {},
      ...origin ? {origin} : {},
      ...searchValue ? {
        searchKey: 'user',
        searchValue: searchValue
      } : {}
    };

    this.data.set('table.loading', true);
    try {
      const res = await this.$http.paloPost('engineAuthUserList', params);
      this.data.set('userList', res?.users || []);
      this.data.set('pager.totalCount', res?.total || 0);
      this.data.set('table.loading', false);
    }
    catch (e) {
      this.data.set('table.loading', false);
    }
  }

  /**
  * 当组件附加到 DOM 时调用此方法。
  *
  * @returns 返回一个 Promise，当获取授权用户列表的操作完成时解析。
  */
  async attached() {
    if (this.data.get('isLoading')) {
      this.data.set('table.loading', true);
      this.watch('isLoading', async () => {
        await this.getAuthUserList();
      });
    } else {
      await this.getAuthUserList();
    }
  }

  /**
  * 页面改变时触发的方法
  *
  * @param target 包含页码和页面大小的对象
  */
  onPageChange(target: {value: {page: number; pageSize: number}}) {
    this.data.set('pager.pageNo', target.value.page);
    this.getAuthUserList();
  }

  /**
  * 当分页大小改变时调用的方法
  *
  * @param target 包含分页信息的对象
  */
  onPageSizeChange(target: {value: {page: number; pageSize: number}}) {
    this.data.set('pager', {pageNo: 1, pageSize: target.value.pageSize});
    this.getAuthUserList();
  }

  /**
  * 创建用户时触发的回调方法
  */
  onCreateUser() {
    this.data.set('curRowData', {})
    this.data.set('showDialog', true);
  }
  /**
  * 重置密码
  */
  onResetPwd(rowData: any) {
    this.data.set('resetRowData', rowData);
    this.data.set('showResetPwdDialog', true);
  }
  /**
  * 编辑用户信息
  */
  onEditUser(rowData: any) {
    this.data.set('curRowData', rowData)
    this.data.set('showDialog', true);
  }
  /**
  * 删除用户时触发的函数
  */
  async onDeleteUser(rowData: any) {
    const {user, host} = rowData;
    const deployId = this.data.get("deployId");
    if (!deployId || !user || !host) {
      Notification.error('请求必填参数缺失');
      return;
    }
    await Dialog.confirm({
      content: `"${rowData.user}"删除后无法恢复，请确认是否删除？`,
      title: '删除',
      onOk: async () => {
        try {
          await this.$http.paloPost('engineAuthUserDelete', {
            deployId,
            userName: user,
            host: host
          });
          Notification.success('删除成功');
          await this.getAuthUserList();
        } catch (e) {
          Notification.error('删除失败，请重新尝试');
        }
      }
    });
  }
  /**
  * 跳转到用户详情页面
  */
  goToUserDetail(rowData: any) {
    const {user, host} = rowData;
    // 这里更新路由对象中的 query
    const query = serializeQueryEncode({
      ...this.data.get('query'),
      type: 'userdetail',
      userName: user,
      host
    });
    redirect(`#/palo/detail?${query}`);
  }
  /**
  * 异步函数，用于解锁用户
  *
  * @param rowData 包含用户信息和主机信息的对象
  * @returns 无返回值
  */
  async goToUnlock(rowData: any) {
    const {user, host} = rowData;
    const deployId = this.data.get("deployId");
    if (!deployId || !user || !host) {
      Notification.error('请求必填参数缺失');
      return;
    }
    const params = {
      deployId: deployId,
      userName: user,
      host: host,
      unlock: true,
    };
    try {
      await this.$http.paloPost('engineAuthUserEdit', params);
      Notification.success('解锁成功');
      await this.getAuthUserList();
    }
    catch (e) {
      Notification.error('解锁失败，请重新尝试');
    }
  }
  /**
  * 过滤数据的异步方法
  *
  * @param event 包含字段和过滤条件的事件对象
  * @param event.field 字段对象
  * @param event.filter 过滤条件对象
  */
  async onFilterData(event: {field: any; filter: any}) {
    const {field, filter} = event;
    this.data.set(`${field.name}`, filter.value);
    await this.getAuthUserList();
  }

  /**
  * 处理输入框搜索事件
  *
  * @param event 事件对象，包含搜索值
  */
  onInputSearch(event: {value: string}) {
    this.data.set('searchValue', event.value);
  }
  /**
  * 搜索用户
  *
  * @returns 无返回值
  */
  async onSearch() {
    this.data.set('pager', {pageNo: 1, pageSize: 10});
    await this.getAuthUserList();
  }

  async closeDialog(e: {tag: boolean}) {
    this.data.set('showDialog', false);
    e.tag && await this.getAuthUserList();
  }

  closeResetPwdDialog() {
    this.data.set('showResetPwdDialog', false);
  }
}
