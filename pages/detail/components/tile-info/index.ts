import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppLegend, ClipBoard} from '@baidu/sui-biz';
import {Loading, Alert} from '@baidu/sui';

import './index.less';

export default class TileInfo extends Component {
    static template = html`
    <div class="palo-infotile {{isLast ? 'is-last' : ''}}">
        <slot name="h-{{type}}-title">
            <s-append label="{{title}}" noHighlight>
                <span slot="extra">
                    <slot name="h-{{type}}-extra"></slot>
                </span>
            </s-append>
            <s-alert skin="info" s-if="{{title === '基本信息' && showAlert}}" class="mt16">
                当前集群是非高可用集群，不承诺SLA。如果在生产环境使用建议尽快<a href="https://cloud.baidu.com/doc/PALO/s/nkvjbf8j4">扩容升级启用高可用（HA）</a>
            </s-alert>
            <s-alert skin="info" s-if="title === '连接信息'" class="mt16">
                如果已绑定EIP，请将连接信息中的IP替换为EIP，即可通过公网访问
            </s-alert>
        </slot>
        <div class="tile-wrap">
            <template s-for="item,index in list">
                <div class="tile-wrap-item {{item.wide ? 'tile-wrap-item-wide' : ''}}" s-if="{{item.isShow}}">
                    <slot name="c-{{item.key}}-item" var-label="{{item.label}}" var-text="{{item.text}}">
                        <span class="box-label" style="width: {{item.labelWidth ? item.labelWidth + 'px' : ''}}">
                            <slot name="c-{{item.key}}-label" var-label="{{item.label}}">
                                {{item.label}}
                            </slot>
                        </span>
                        <span class="box-text">
                            <slot name="c-{{item.key}}-text" var-text="{{item.text}}">
                                {{item.text}}
                            </slot>
                        </span>
                        <span class="box-icon" s-if="item.canCopy">
                            <s-clip-board class="clipboard-default" text="{{item.text}}" />
                        </span>
                    </slot>
                </div>
            </template>
        </div>
        <slot name="{{type}}-footer"></slot>
    </div>`;

    static components = {
        's-append': AppLegend,
        's-loading': Loading,
        's-alert': Alert,
        's-clip-board': ClipBoard
    };
}
