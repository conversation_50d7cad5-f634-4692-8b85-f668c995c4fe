import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {Collapse, Table, Alert, Loading, Button} from '@baidu/sui';
import {FilledWarn} from '@baidu/sui-icon';
import {DETAIL_TYPE} from './config';

const columns = [
    {name: 'instanceId', label: '监控对象', width: 200},
    {name: 'insufficientStateCount', label: '数据不足', width: 100},
    {name: 'alarmStateCount', label: '异常', width: 100},
    {name: 'okStateCount', label: '正常', width: 100},
    {name: 'opereation', label: '操作', width: 100}
];

// 注意bcm同学返回跳转链接参数顺序必须为scope，typeName，ProductRegion，DeployId，InstanceId，ProductName！

export default class WarnStatus extends Component{

    static template = html`
        <div class="warn-status">
            <s-loading loading="{{loading}}">
                <s-alert skin="success" s-if="{{condition === 0}}">
                    当前实例状态健康，暂无报警事件
                    <a target="_blank" href="{{href}}">报警详情</a>
                </s-alert>
                <s-alert skin="warning" s-else-if="{{condition === -1}}">
                    当前实例数据不足，请检查设备信息
                    <a target="_blank" href="{{href}}">报警详情</a>
                </s-alert>
                <s-collapse s-else>
                    <s-collapse-panel key="1">
                        <span slot="header" class="header-slot">
                            <outlined-info-circle color="#FF9326" width="14" class="mr3"/>
                            实例正在处于报警状态，报警总数{{count}}个
                        </span>
                        <s-table
                            datasource="{{datasource}}"
                            columns="{{columns}}"
                            max-height="{{250}}"
                        >
                            <div slot="h-insufficientStateCount">
                                <span class="status error">数据不足</span>
                            </div>
                            <div slot="h-alarmStateCount">
                                <span class="status warning">异常</span>
                            </div>
                            <div slot="h-okStateCount">
                                <span class="status normal">正常</span>
                            </div>
                            <div slot="c-opereation">
                                <s-button
                                    skin="stringfy"
                                    class="table-btn-slim"
                                    s-if="tab === 'other'"
                                >
                                    <a target="_blank" href="/bcm/pro/#/alarm/rule?scope=BCE_PALO&typeName={{typeName}}&dimensions=ProductRegion:{{region}};DeployId:{{deployId}};ProductName:palo;&region={{region}}">报警详情</a>
                                </s-button>
                                <s-button
                                    skin="stringfy"
                                    on-click="getHref(row)"
                                    s-else
                                >
                                    报警详情
                                </s-button>
                            </div>
                        </s-table>
                    </s-collapse-panel>
                </s-collapse>
            </s-loading>
        </div>
    `;

    static components = {
        's-collapse': Collapse,
        's-collapse-panel': Collapse.Panel,
        's-table': Table,
        's-alert': Alert,
        'outlined-info-circle': FilledWarn,
        's-loading': Loading,
        's-button': Button
    };

    static computed = {
        firstDimension(): string {
            const dimensions = this.data.get('dimensions');
            return dimensions[0] || '';
        },
        href(): string {
            const other = this.data.get('tab') === 'other';
            const region = window.$context.getCurrentRegionId();
            const firstId = this.data.get('firstId');
            const typeName = this.data.get('typeName');
            const deployId = this.data.get('deployId');
            const type = this.data.get('type');
            if (type === DETAIL_TYPE.decoupled) {
                return `/bcm/pro/#/alarm/rule?scope=BCE_PALO&dimensions=ProductRegion:${region};DeployId:${deployId}${firstId ? `;InstanceId:${firstId}` : ''};ProductName:palo&region=${region}`;
            }
            return other
                ? `/bcm/pro/#/alarm/rule?scope=BCE_PALO&typeName=${typeName}&dimensions=ProductRegion:${region};DeployId:${deployId};ProductName:palo&region=${region}`
                : `/bcm/pro/#/alarm/rule?scope=BCE_PALO&typeName=${typeName}&dimensions=ProductRegion:${region};DeployId:${deployId}${firstId ? `;InstanceId:${firstId}` : ''};ProductName:palo&region=${region}`;
        },
        typeName(): string {
            const tab = this.data.get('tab');
            switch (tab) {
                case 'palofe':
                    return 'LeaderNode';
                case 'palobe':
                    return 'ComputeNode';
                case 'other':
                    return 'Cluster';
            }
        }
    }

    initData() {
        return {
            columns: columns,
            datasource: [],
            condition: 0,
            loading: true,
            region: window.$context.getCurrentRegionId()
        }
    }

    attached() {
        this.getSummaryData();
        this.watch('dimensions', () => {
            this.getSummaryData();
        });
    }

    async getSummaryData() {
        const {dimensions = []} = this.data.get('');
        let count = 0;
        let insufficientStateCount = 0;
        this.data.set('loading', true);
        this.data.set('datasource', []);
        await Promise.all(dimensions?.map(async (item: any) => {
            let params = {
                dimensions: item,
                scope: 'BCE_PALO'
            };
            const instanceId = this.getInstanceIdFromStr(item);
            let data = await this.$http.paloPost('bcmSummary', params);
            this.data.push('datasource', {
                instanceId: instanceId,
                ...data
            });
            data.alarmStateCount > 0 && count++;
            data.insufficientStateCount > 0 && insufficientStateCount++;;
        }));
        this.data.set('count', count);
        this.data.set('condition', count > 0 ? count : (count == 0 && insufficientStateCount > 0 ? -1 : 0));
        this.data.set('loading', false);  
    }
    getInstanceIdFromStr(str: string) {
        const parts = str.split(';');
        let instanceId = '';
        parts.forEach((part: string) => {
            const [key, value] = part.split(':');
            if (key.trim() === 'InstanceId') {
                instanceId = value.trim();
            }
        })
        return instanceId;
    }

    getHref(row: any) {
        const typeName = this.data.get('typeName');
        const region = this.data.get('region');
        const deployId = this.data.get('deployId');
        redirect(`/bcm/pro/#/alarm/rule?scope=BCE_PALO&typeName=${typeName}&dimensions=ProductRegion:${region};DeployId:${deployId}${row.instanceId ? `;InstanceId:${row.instanceId}` : ''};ProductName:palo&region=${region}`);
    }
}