/**
 * 修改缓存配置
 * @file index.ts 
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {
    Dialog,
    Form,
    InputNumber,
    Button,
    Notification,
    Checkbox,
    Switch,
    Alert
} from '@baidu/sui';
import {ServiceFactory} from '@baiducloud/runtime';
import './index.less';
export default class ConfigEditDialog extends Component {
    static template = html`
    <template>
        <s-dialog 
            open="{= open =}"
            on-close="close"
            width="{{800}}"
            class="edit-config-dialog"
            title="修改缓存配置"
        >
            <s-alert skin="info" class="mb16">
                修改缓存配置需要重启Compute Node
            </s-alert>
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                inlineMessage="true"
                label-align="left"
            >
                <s-form-item label="开启缓存：" required class="form-item-switch-middle cacheup-form-item mb16">
                    <s-switch checked="{=formData.cache_endable=}" />
                </s-form-item>
                <div class="second-form-item">
                    <s-form-item s-if="formData.cache_endable" label="缓存容量：" prop="cache_size" required>
                        <s-input-number
                            class="mr5"
                            value="{=formData.cache_size=}"
                            min="{{cache_size_min}}"
                            max="{{cache_size_max}}"
                        />
                        GB
                    </s-form-item>
                    <s-form-item label="重启方式：" prop="roll_restart" required>
                        <s-checkbox
                            label="滚动重启"
                            value="{=formData.roll_restart=}"
                            checked="{=formData.roll_restart=}"
                        />
                        <p slot="help">集群不重启平滑切换，不影响线上服务，但速度较慢</p>
                    </s-form-item>
                </div>
            </s-form>
            <footer slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button on-click="submit" skin="primary" loading="{{loading.confirm}}">确定</s-button>
            </footer>
        </s-dialog> 
    </template>  
    `;
    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-button': Button,
        's-input-number': InputNumber,
        's-checkbox': Checkbox,
        's-switch': Switch,
        's-alert': Alert
    };

    static computed = {
        cache_size_min: function () {
            const diskSize = this.data.get('diskSize');
            return Math.max(Math.ceil(diskSize / 100), 10);
        },
        cache_size_max: function () : number {
            const diskSize = this.data.get('diskSize');
            return Math.floor(diskSize * 0.6);
        },
    }

    initData() {
        return {
            open: true,
            inputWidth: 340,
            formData: {
                cache_endable: false,
                cache_size: 20,
                roll_restart: true
            },
            loading: {
                confirm: false,
            },
            rules: {
                cache_size: [
                    {required: true, message: '请输入缓存容量'}
                ],
                roll_restart: [
                    { required: true, message: '请选择重启方式' },
                    {
                        validator: (rule: any, value: any, callback: (arg0: string | undefined) => void) => {
                            if (!value) {
                                return callback('请选择重启方式');
                            }
                            callback();
                        }
                    }
                ]
            },
        };
    }

    attached() {
        const diskSize = this.data.get('diskSize');
        const cache_endable = this.data.get('formData.cache_endable');
        if (!cache_endable) {
            this.data.set('formData.cache_size', Math.ceil(diskSize * 0.1));
        }
    }

    close() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    async submit() {
        try {
            await this.ref('form').validateFields();

            const { deployId, formData } = this.data.get('');
            const {cache_endable, cache_size} = formData;
            const kernelConfList =  [
                {
                    configName: 'enable_file_cache',
                    expectedConfigValue: cache_endable,
                    kernelType: 'FE'
                },
                {
                    configName: 'enable_file_cache',
                    expectedConfigValue: cache_endable,
                    kernelType: 'BE'
                },
                {
                    configName: 'file_cache_path_total',
                    expectedConfigValue: cache_endable ? cache_size : 0,
                    kernelType: 'BE'
                }
            ]
            this.data.set('loading.confirm', true);
            await this.$http.paloPost('kernelUpdate', {
                deployId,
                kernelConfList,
                region: ServiceFactory.resolve('$context').getCurrentRegion().id
            });
            Notification.success('修改缓存配置成功！');
            this.fire('success', {});
            this.close();
        }
        catch (err) {
            console.error(err);
        }
        finally {
            this.data.set('loading.confirm', false);
        }
    }
}
