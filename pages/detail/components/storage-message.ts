/**
 * 集群详情-存算分离
 *
 * @file index.ts
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {AppLegend, ClipBoard} from '@baidu/sui-biz';
import {Eye} from '@baidu/xicon-san';
import {Table, Button, Notification, Pagination, Tooltip, Loading, Dialog} from '@baidu/sui';
import TileInfo from './tile-info';
import EipBind from './eip-bind';
import PasswordConfirmDialog from './password-reset';
import {renderStatus, formatTime, formateSize} from '@common/utils'
import {protocolColumns} from './columns';
import InstantEditor from '@components/instant-editor';
import {diskDatasource4Decoupled} from '../../create/components/create-utils';

const klass = 'palo-cluster-detail-info';

const TEXT_MAP = {
    deployId: '集群ID：',
    deployName: '集群名称：',
    region: '区域：',
    productType: '付费方式：',
    expireTime: '到期时间：',
    availableZone: '可用区：',
    adminUsername: '管理员账号：',
    vpc: '所在网络（VPC）:',
    subnet: '子网：',
    securityGroup: '安全组：',
    engineVersion: '内核版本：',
    cuNums: '计算资源总计：',
    computeGroupNums: '计算组：',
    feSsdCacheCapacity: 'SSD缓存容量：',
    xxxCacheCapacity: 'CDS缓存容量：',
    storageUsage: '存储用量：',
    paloDecoupledEIP: '公网地址：',
    createTime: '创建时间：'
};

const renderItem = (
    label: string | number,
    key: string,
    text: string | string[] | number | Object = '',
    isShow: boolean = true,
    wide: boolean = false,
    canCopy: boolean = false,
    labelWidth: number) => ({label, key, text, isShow, wide, canCopy, labelWidth});


export default class Info extends Component{
    static template = html`
    <div class="${klass} palo-tab-info">
        <s-loading s-if="{{isLoading}}" size="large" loading="{{true}}" style="width: 100%; height: 100px; margin-top: 100px"></s-loading>
        <template s-else>
            <tile-info
                s-for="item,index in infoList"
                isLast="{{index === infoList.length - 1}}"
                type="{{item.type}}"
                title="{{item.title}}"
                list="{{item.list}}"
                showAlert="{{item.showAlert}}"
            >
                <span slot="c-deployName-text">
                    {{text}}
                    <s-clip-board text="{{text}}" class="ml4" />
                    <instant-editor
                        value="{{text}}"
                        request="{{editName}}"
                        check="{{check}}"
                        class="ml8"
                        placeholder="请输入集群名称"
                        desc="支持字母(a-z及A-Z)、数字(0-9),长度小于20个字符"
                    />
                </span>
                <span slot="c-adminUsername-text" class="${klass}__adminUser">
                    {{text}}
                    <s-button
                        skin="stringfy"
                        on-click="openPasswordResetDialog"
                        class="table-btn-slim ml8 passreset"
                        disabled="{{passwordDisabled}}"
                    >
                        密码重置
                    </s-button>
                </span>
                <span slot="c-engineVersion-text">
                    {{text}}
                    <sui-tooltip content="{{'没有可升级的版本'}}" placement="top">
                        <s-button
                            skin="stringfy"
                            class="table-btn-slim ml8"
                            disabled="{{true}}"
                        >版本升级</s-button>
                    </sui-tooltip>
                </span>
                <span slot="c-config-text">
                    {{text}}
                    <s-button
                        skin="stringfy"
                        on-click="editConfig"
                        class="table-btn-slim ml8 passreset"
                        disabled="{{editConfigDisabled}}"
                    >
                        修改
                    </s-button>
                </span>
                <span slot="c-paloDecoupledEIP-text">
                    <span s-if="{{text === 'binded'}}">
                        {{detail.paloDecoupledEIP}}
                        <s-button skin="stringfy" on-click="unBindEip">{{text | bindEipText}}</s-button>
                    </span>
                    <span s-elif="{{text === 'binding' || text === 'unbinding'}}">
                        {{text | filterEipStatus}}
                        <s-button skin="stringfy" on-click="refreshEip">刷新</s-button>
                    </span>
                    <span s-else>
                        {{text | filterEipStatus}}
                        <s-button skin="stringfy" on-click="bindEip">{{text | bindEipText}}</s-button>
                    </span>
                </span>
                <span slot="c-cuNums-text">
                    {{text}}CU
                </span>
                <span slot="c-computeGroupNums-text">
                    {{text}}个
                </span>
                <span slot="c-feSsdCacheCapacity-text">
                    {{text}}G
                </span>
                <span slot="c-xxxCacheCapacity-text">
                    {{text}}G
                </span>
                <s-table slot="accessInfo-footer" columns="{{protocolColumns}}" datasource="{{protocolSource}}">
                    <div slot="c-protocolIp">
                        {{row.protocolIp}}<s-clip-board class="clipboard-default ml4" text="{{row.protocolIp}}" />
                    </div>
                </s-table>
            </tile-info>
        </template>
    </div>`;

    static computed: SanComputedProps = {
        infoList() {
            const detail = this.data.get('detail');
            const canCopy = true;
 
            const {
                deployId,
                deployName,
                expireTime,
                productType,
                createTime = '',
                adminUsername,
                availableZone,
                vpc,
                subnet,
                cuNums,
                securityGroup,
                securityGroupId,
                computeGroupNums,
                storageUsage,
                engineVersion = '',
                paloDecoupledEIPStatus
            } = detail;

            const securityGroupDatasource = this.data.get('securityGroupDatasource');

            const BaseInfo = {
                title: '基本信息',
                type: 'baseinfo',
                list: [
                    renderItem(TEXT_MAP.deployId, 'deployId', deployId, true, false, canCopy, 72),
                    renderItem(TEXT_MAP.deployName, 'deployName', deployName, true, false, false, 72),
                    renderItem(TEXT_MAP.engineVersion, 'engineVersion', engineVersion, true, false, false, 72),
                    renderItem(TEXT_MAP.createTime, 'createTime', formatTime(createTime), true, false, false, 72),
                    renderItem(TEXT_MAP.expireTime, 'expireTime', formatTime(expireTime), true, false, false, 72),
                    // renderItem(TEXT_MAP.productType, 'productType', PayType[productType], true, false, false, 72),
                    renderItem(TEXT_MAP.adminUsername, 'adminUsername', adminUsername, true, false, false, 72),
                    renderItem(TEXT_MAP.region, 'region', window.$context.getCurrentRegion().label, true, false, false, 72),
                    renderItem(TEXT_MAP.availableZone, 'availableZone', '可用区' + availableZone?.slice(4,), true, false, false, 72),
                ]
            };

            const AccessInfo = {
                title: '网络连接信息',
                type: 'accessInfo',
                list: [
                    renderItem(TEXT_MAP.vpc, 'vpc', vpc, true, false, false, 100),
                    renderItem(TEXT_MAP.subnet, 'subnet', subnet, true, false, false, 90),
                    renderItem(TEXT_MAP.securityGroup, 'securityGroup', securityGroupId 
                        ? securityGroupDatasource.find((item: any) => item.id === securityGroupId)?.name 
                        : (securityGroup ?? '-'), true, false, false, 90),
                    renderItem(TEXT_MAP.paloDecoupledEIP, 'paloDecoupledEIP', paloDecoupledEIPStatus, true, false, false, 90),
                ]
            };

            const ConfigInfo = {
                title: '资源配置',
                type: 'configInfo',
                list: [
                    renderItem(TEXT_MAP.computeGroupNums, 'computeGroupNums', computeGroupNums, true, false, false, 120),
                    renderItem(TEXT_MAP.cuNums, 'cuNums', cuNums, true, false, false, 120),
                    renderItem(TEXT_MAP.storageUsage, 'storageUsage', formateSize(storageUsage), true, false, false, 120),
                ]
            };

            diskDatasource4Decoupled.forEach(diskItem => {
                if (detail[diskItem.extra] !== 0) {
                    ConfigInfo.list.push(renderItem(diskItem.text + '：', diskItem.extra, detail[diskItem.extra] + 'G', true, false, false, 120))
                }
            });

            return [
                BaseInfo,
                AccessInfo,
                ConfigInfo,
            ];
        },
        protocolSource() {
            const detail = this.data.get('detail');
            const {
                mysqlProtocalEndpoint,
                httpProtocalEndpoint,
                jdbcUrl,
                paloUIAddress
            } = detail;
            return [
                {
                    protocolType: 'MySQL协议',
                    protocolIp: mysqlProtocalEndpoint
                },
                {
                    protocolType: 'HTTP协议',
                    protocolIp: httpProtocalEndpoint
                },
                {
                    protocolType: 'JDBC协议',
                    protocolIp: jdbcUrl
                },
                {
                    protocolType: 'WEB UI',
                    protocolIp: paloUIAddress
                }
            ]
        }
    }

    static filters: SanFilters = {
        filterEipStatus(value: string) {
            switch (value) {
                case 'unbind': return '未分配';
                case 'binding': return 'EIP绑定中，请稍后刷新';
                case 'unbinding': return 'EIP解绑中，请稍后刷新'
            }
        },
        filterStatus(displayActualStatus: string, status: string) {
            return renderStatus(displayActualStatus, status);
        },
        bindEipText(value: string) {
            switch (value) {
                case 'unbind': return '绑定EIP';
                case 'binded': return '解绑EIP';
                case 'binding': return '刷新';
                case 'unbinding': return '刷新'
            }
        }
    };

    static components = {
        's-append': AppLegend,
        'tile-info': TileInfo,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'sui-tooltip': Tooltip,
        's-loading': Loading,
        's-eye': Eye,
        's-clip-board': ClipBoard,
        'instant-editor': InstantEditor
    };

    initData() {
        return {
            detail: {},
            table: {
                datasource: [],
                columns: []
            },
            isLoading: false,
            modules: [],
            protocolColumns: protocolColumns,
            editName: this.editName.bind(this),
            check: this.check.bind(this),
            securityGroupDatasource: []
        };
    }

    attached() {
        this.watch('detail', (value) => {
            value?.vpcId && this.getSecurityGroup();
        });
    }

    async getSecurityGroup() {
        const vpcId = this.data.get('detail.vpcId');
        const res = await this.$http.paloPost('paloSecurityList',{vpcId});
        this.data.set('securityGroupDatasource', res.result);
    }


    async unBindEip() {
        const deployId = this.data.get('detail.deployId');
        try {
            await Dialog.warning({
                content: `您确定解绑公网地址吗？`,
                title: '提示'
            });
            let params = {
                deployId: deployId,
                moduleTemplateName: 'decoupled'
            };
            try {
                await this.$http.paloPost('paloEipUnbind', params);
                Notification.success('操作成功');
                this.fire('refresh', {});
            } catch (e) {
                Notification.error('解绑失败，请重试！');
            }
        } catch (e) {}
    }

    bindEip() {
        const dialog = new EipBind({
            data: {
                openEip: true,
                detail: this.data.get('detail'),
                bindType: 'decoupled'
            }
        });
        dialog.on('success', () => {
            this.fire('refresh', {});
        });
        dialog.attach(document.body);
        return dialog;
    }

    openPasswordResetDialog() {
        const deployId = this.data.get('detail.deployId');
        const dialog = new PasswordConfirmDialog({
            data: {
                deployId
            }
        });
        dialog.on('success', () => {});
        dialog.attach(document.body);
        return dialog;
    }

    // 名称输入校验
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length >= 20) {
            return callback('不能超过20个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }
        
    // 名称编辑
    async editName(name: string, rowIndex: number) {
        const {deployId} = this.data.get('detail');
        await this.$http.paloPost('paloSeperateEditName', {
            deployId: deployId,
            editNewName: name
        })
        Notification.success('修改成功');
        this.fire('refresh', {});
    }

    refreshEip() {
        this.fire('refresh', {});
    }
};