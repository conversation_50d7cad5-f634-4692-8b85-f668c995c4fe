import {Component} from 'san';
import {Table, Button, Pagination, Loading, Tooltip, Cascader, InputNumber, DatePicker, Notification} from '@baidu/sui';
import {AppListPage, SearchBox, TableColumnToggle, Empty} from '@baidu/sui-biz';
import {OutlinedRefresh, OutlinedDownload} from '@baidu/sui-icon';
import {html} from '@baiducloud/runtime';
import {debounce} from '@common/decorators';
import {customIdentity, getTimeStamp, compareVersions} from '@common/utils/index';
import EllipsisTip from '@components/ellipsis-tip';
import _ from 'lodash';
import moment from 'moment';
import { dateRangePickerShortcuts, columns, columnsSource } from './config';
import {AllEnum} from '@common/config/index';
const allEnum = AllEnum.toArray();
import './index.less';

const klass = 'palo-query-list';
export default class QueryPage extends Component {
    static template = html`
        <div class="${klass}">
            <s-loading loading="{{table.loading}}" mask="{{true}}">
                <app-list-page class="${klass}_content">
                    <div slot="pageTitle">
                        <h2 class="title">查询分析</h2>
                    </div>
                    <div slot="bulk">
                        类型
                        <s-cascader
                            datasource="{{datasource}}"
                            value="{=value=}"
                            on-change="changeType"
                            class="ml8"
                            width="180">
                        </s-cascader>
                        <span s-if="{{type === '1'}}" class="ml8 mr8">
                            运行时长
                            <s-input-number
                                min="{{0.5}}"
                                step="{{0.1}}"
                                width="60"
                                class="ml8"
                                value="{=slowQueryTime=}"
                                on-change="refreshWithPage"
                            />
                            秒
                        </span>
                        <span s-if="{{type === 'scanBytes'}}" class="ml8 mr8">
                            数据量
                            <s-input-number
                                min="{{1}}"
                                step="{{0.1}}"
                                stepStrictly
                                width="60"
                                class="ml8"
                                value="{=queryScanBytes=}"
                                on-change="refreshWithPage"
                            />
                            GB
                        </span>
                        <span s-if="{{type === 'scanRows'}}" class="ml8 mr8">
                            行数
                            <s-input-number
                                min="{{1}}"
                                step="{{1}}"
                                width="60"
                                class="ml8"
                                stepStrictly
                                value="{=queryScanRows=}"
                                on-change="refreshWithPage"
                            />
                            千万
                        </span>
                        执行开始时间：
                        <s-date-range-picker
                            value="{= date =}"
                            mode="second"
                            class="ml8"
                            shortcut="{{shortCutDate}}"
                            range="{{range}}"
                            on-change="onDatePickerChange"
                        />
                    </div>
                    <div slot="filter" class="filter-content">
                        <s-searchbox
                            placeholder="{{searchbox.placeholder}}"
                            on-keywordTypeChange="onKeywordTypeChange"
                            text-datasource="{{searchbox.textDataSource}}"
                            value="{= searchbox.keyword =}"
                            keyword-type="{= searchbox.keywordType =}"
                            datasource="{{searchbox.keywordTypes}}"
                            on-search="onSearch"
                            width="100"
                        />
                        <s-button on-click="download" class="ml5 s-icon-button">
                            <s-icon-download />
                        </s-button>
                        <s-button on-click="getComList" class="ml5 s-icon-button">
                            <s-icon-refresh />
                        </s-button>
                        <s-table-column-toggle
                            datasource="{{columnsSource}}"
                            value="{{columnsSelected}}"
                            on-change="tableColumnSelect"
                            class="ml5"
                        />
                    </div>
                    <s-table
                        columns="{{table.columns}}"
                        error="{{table.error}}"
                        datasource="{{table.datasource}}"
                        width="{{1100}}"
                        loading="{{loading}}"
                        on-sort="onSort"
                        on-filter="onFilter"
                    >
                        <ellipsis-tip
                            slot="c-queryId"
                            content="{{row.queryId}}"
                            text="{{row.queryId}}"
                            placement="top"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                            copy="{{true}}"
                        >
                        </ellipsis-tip>
                        <ellipsis-tip
                            slot="c-db"
                            content="{{row.db}}"
                            text="{{row.db}}"
                            placement="top"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                        >
                        </ellipsis-tip>
                        <ellipsis-tip
                            slot="c-user"
                            content="{{row.user}}"
                            text="{{row.user}}"
                            placement="left"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                        >
                        </ellipsis-tip>
                        <ellipsis-tip
                            slot="c-catalog"
                            content="{{row.catalog}}"
                            text="{{row.catalog}}"
                            placement="left"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                        >
                        </ellipsis-tip>
                        <ellipsis-tip
                            slot="c-stmt"
                            content="{{row.stmt}}"
                            text="{{row.stmt}}"
                            placement="top"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                            copy="{{true}}"
                        >
                        </ellipsis-tip>
                        <ellipsis-tip
                            slot="c-frontendIp"
                            content="{{row.frontendIp}}"
                            text="{{row.frontendIp}}"
                            placement="top"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                            copy="{{true}}"
                        >
                        </ellipsis-tip>
                        <ellipsis-tip
                            slot="c-clientIp"
                            content="{{row.clientIp}}"
                            text="{{row.clientIp}}"
                            placement="top"
                            showTip="{{true}}"
                            alwaysTip="{{true}}"
                            copy="{{true}}"
                        >
                        </ellipsis-tip>
                        <div slot="empty">
                            <s-empty s-if="{{code === 501}}" vertical emptyText="" image="{{image}}">
                                <span slot="action" class="empty-area">
                                {{installing ? '审计日志查询插件正在安装中，请稍后......' : '当前集群还没安装审计日志查询，请先安装审计日志查询'}}
                                    <s-button
                                        on-click="installPlugin"
                                        class="mt8"
                                        id="install-button"
                                        skin="primary"
                                        disabled="{{canNotInstall}}"
                                        loading="{{installing}}"
                                    >
                                        {{installText}}
                                    </s-button>
                                </span>
                            </s-empty>
                            <s-empty s-elif="{{code === 502}}" vertical emptyText="" image="{{image}}">
                                <span slot="action" class="empty-area">
                                    审计日志库或表不存在，请提工单修复
                                </span>
                            </s-empty>
                            <s-empty s-else vertical emptyText="" image="{{image}}">
                                <span slot="action" class="empty-area">
                                    {{emptyText}}
                                </span>
                            </s-empty>
                        </div>
                    </s-table>
                    <s-pagination
                        class="mt10"
                        slot="pager"
                        layout="{{'total, pageSize, pager'}}"
                        total="{{pager.totalCount}}"
                        page="{{pager.pageNo}}"
                        page-size="{{pager.pageSize}}"
                        on-pagerChange="onPageChange"
                        on-pagerSizeChange="onPageSizeChange">
                    </s-pagination>
                </app-list-page>
            </s-loading>
        </div>
    `;
    static components = {
        'app-list-page': AppListPage,
        's-icon-refresh': OutlinedRefresh,
        's-table': Table,
        's-button': Button,
        's-loading': Loading,
        's-pagination': Pagination,
        's-tooltip': Tooltip,
        's-searchbox': SearchBox,
        's-cascader': Cascader,
        's-input-number': InputNumber,
        's-date-range-picker': DatePicker.DateRangePicker,
        's-table-column-toggle': TableColumnToggle,
        's-icon-download': OutlinedDownload,
        's-empty': Empty,
        'ellipsis-tip': EllipsisTip,
    };

    initData() {
        return {
            table: {
                columns: columns,
                loading: true,
                datasource: [],
            },
            pager: {
                pageSize: 10,
                pageNo: 1,
                totalCount: 0
            },
            datasource: [
                {
                    value: '1',
                    text: '慢查询',
                },
                {
                    value: '2',
                    text: '大查询',
                    children: [
                        {
                            value: 'scanBytes',
                            text: '扫描数据量',
                        },
                        {
                            value: 'scanRows',
                            text: '扫描行数',
                        },
                    ]
                }
            ],
            type: '1',
            value: ['1'],
            slowQueryTime: 0.5,
            queryScanBytes: 1,
            queryScanRows: 1,
            shortCutDate: dateRangePickerShortcuts,
            columnsSource: columnsSource,
            columnsSelected: columnsSource.map(item => item.value),
            searchbox: {
                keyword: '',
                keywordType: ['stmt'],
                keywordTypes: [
                    {
                        value: 'stmt',
                        text: 'SQL语句'
                    },
                    {
                        value: 'user',
                        text: '用户名'
                    }
                ],
                allTextDataSource: []
            },
            date: {
                begin: moment().subtract(15, 'minutes').toDate(),
                end: new Date()
            },
            code: 0,
            installing: false,
            range: {
                begin: moment().subtract(2, 'days').startOf('day').toDate(),
                end: moment().endOf('day').toDate()
            },
        };
    }

    static computed = {
        installText(): string {
            return this.data.get('installing') ? '安装中...' : '立即安装';
        },
        emptyText(): string {
            const keyword = this.data.get('searchbox.keyword');
            const modules = this.data.get('detail.modules');
            const version = modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
            return compareVersions('0.14.7', version) > 0 ? '当前集群版本过低，请提工单尝试升级集群' : keyword ? '搜索结果为空，请重新搜索' : '暂无数据';
        },
        canNotInstall(): boolean {
            const instances = this.data.get('detail.instances');
            const hasNotRunningInstance = instances?.find(item => item.status !== 'Running');
            return hasNotRunningInstance;
        },
        image() {
            const code = this.data.get('code');
            const keyword = this.data.get('searchbox.keyword');
            if (code === 501) {
                return require('@static/img/noPlugin.svg');
            }
            else if (code === 502) {
                return require('@static/img/tableNotFound.svg');
            } 
            else if (keyword) {
                return require('@static/img/noResult.svg');
            }
            else {
                return require('@static/img/empty2.svg');
            }
        }
    };

    attached() {
        if (this.data.get('isLoading')) {
            this.watch('isLoading', value => {
                this.sendRequest();
            })
        }
        else {
            this.sendRequest();
        }
    }

    sendRequest() {
        const {status,  modules} = this.data.get('detail');
        const version = modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
        if (compareVersions('0.14.7', version) > 0) {
            this.data.set('table.loading', false);
        }
        else if (status === 'AuditInstalling') {
            this.data.set('installing', true);
            this.data.set('code', 501);
            this.data.set('table.loading', false);
        }
        else {
            this.getComList();
        }
    }

    onDatePickerChange(e: {value: [Date, Date]}) {
        this.getComList(); 
    }

    // search-box 的 keyword type改变时调用
    onKeywordTypeChange(e: {value: string[]}) {
        this.data.merge('searchbox', {
            keywordType: [e.value[0]],
            keyword: ''
        });
    }

    onSearch(args?: {value: string}): void {
        this.data.set('pager.pageNo', 1);
        this.refresh();
    }

    refreshWithPage(page: number) {
        this.data.set('pager.pageNo', 1);
        this.refresh();
    }

    changeType(target: { value: string[] }) {
        switch (target.value[0]) {
            case '1':
                this.data.set('type', '1');
                break;
            case '2':
                this.data.set('type', target.value[1]);
        }
        this.refresh();
    }

    tableColumnSelect(e: {value: string[]}) {
        this.data.set('columnsSelected', e.value);
        this.setTableColumn(e.value);
    }

    setTableColumn(value: any) {
        let temp = _.filter(columns, item => value.indexOf(item.name) > -1);
        const {orderBy, order, state, isQuery} = this.data.get('');
        if (order) {
            temp = _.map(temp, item => {
                if (item.name === orderBy) {
                    return {
                        ...item,
                        order: order,
                    };
                }
                return {...item};
            });
        };
        if (state) {
            temp = _.map(temp, item => {
                if (item.name === 'state') {
                    return {
                        ...item,
                        filter: {
                            options: [
                                ...allEnum,
                                {
                                    text: 'EOF',
                                    value: 'EOF'
                                },
                                {
                                    text: 'ERR',
                                    value: 'ERR'
                                },
                                {
                                    text: 'OK',
                                    value: 'OK'
                                }
                            ],
                            value: state
                        },
                    };
                }
                return {...item};
            });
        }
        if (isQuery === 0 || isQuery === 1) {
            temp = _.map(temp, item => {
                if (item.name === 'isQuery') {
                    return {
                        ...item,
                        filter: {
                            options: [
                                ...allEnum,
                                {
                                    text: '查询',
                                    value: 1
                                },
                                {
                                    text: '非查询',
                                    value: 0
                                }
                            ],
                            value: isQuery
                        },
                    };
                }
                return {...item};
            });
        }
        this.data.set('table.columns', temp);
    }

    onPageChange(target: {value: {page: number; pageSize: number}}) {
        this.data.set('pager.pageNo', target.value.page);
        this.getComList();
    }

    onPageSizeChange(target: {value: {page: number; pageSize: number}}) {
        this.data.set('pager', {pageNo: 1, pageSize: target.value.pageSize});
        this.getComList();
    }

    onFilter(args: {field: {name: string}, filter: {value: string | number}}) {
        this.data.set('pager.pageNo', 1);
        this.data.set(args.field.name, args.filter.value);
        this.getComList();
    }

    onSort(args: {value: {order: number, orderBy: number}}) {
        this.data.set('orderBy', args.value.orderBy);
        this.data.set('order', args.value.order);
        this.data.set('pager.pageNo', 1);
        this.getComList();
    }

    download() {
        const params = this.getParams();
        let FILENAME = 'slow_and_large_query.xlsx';
        let xhr = new XMLHttpRequest();
        let saveBlob = this.saveBlob;
        xhr.open('POST', '/api/palo/audit/export/exportData');
        xhr.setRequestHeader('Content-Type', 'application/octet-stream');
        xhr.setRequestHeader('X-Region', window.$context.getCurrentRegion().id);
        xhr.setRequestHeader('csrfToken', this.$cookie.get('bce-user-info').replace(/^"|"$/g, ''));
        xhr.responseType = 'blob';
        xhr.onload = function (e: Event) {
            const contentDisposition = xhr.getResponseHeader("content-disposition");
            const replacePlusWithSpace = (input: string) => {
                return input.replace(/\+/g, ' ');
            }
            if (this.status === 200) {
                FILENAME = decodeURIComponent(contentDisposition.match(/filename="([^"]+)"/)[1]);
                saveBlob(this.response, FILENAME);
            } else {
                if (this.status === 400) {
                    const errorMsg = replacePlusWithSpace(decodeURIComponent(contentDisposition.match(/errorMsg="([^"]+)"/)[1]));
                    const errorCode = replacePlusWithSpace(decodeURIComponent(contentDisposition.match(/errorCode="([^"]+)"/)[1]));
                    if (errorCode === '503') {
                        Notification.error(`当前导出结果超出${errorMsg}条，请通过集群进行导出`);
                    }
                    else {
                        Notification.error(`下载报错：${errorMsg}`);
                    }
                }
            }
        };
        xhr.onerror = function (e) {
            Notification.error(`下载报错：${JSON.stringify(e)}`);
        };
        xhr.send(JSON.stringify(params));
    }

    saveBlob(blob, fileName: string) {
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(blob);
        a.download = fileName;
        a.dispatchEvent(new MouseEvent('click'));
    }

    getParams() {
        const { deployId, pager, orderBy, order, type, slowQueryTime, date, queryScanBytes, queryScanRows, state, isQuery, detail } = this.data.get('');
        const engineVersion = detail.modules?.find(item => item.moduleDisplayName === 'Compute Node')?.version;
        const {keywordType, keyword: searchValue} = this.data.get('searchbox') as any;
        let queryType = type;
        let filterConditionsKey = 'slowQueryTime';
        let filterConditionsValue = slowQueryTime;
        if (type === 'scanBytes') {
            queryType = 2;
            filterConditionsKey = 'queryScanBytes';
            filterConditionsValue = queryScanBytes;
        }
        else if (type === 'scanRows') { 
            queryType = 2;
            filterConditionsKey = 'queryScanRows';
            filterConditionsValue = queryScanRows;
        }
        const params = _.pick({
            deployId,
            type: queryType,
            order,
            orderBy,
            pageNo: pager.pageNo,
            pageSize: pager.pageSize,
            filterConditionsKey,
            filterConditionsValue,
            startTime: getTimeStamp(date.begin),
            endTime: getTimeStamp(date.end),
            searchKey: keywordType[0],
            searchValue,
            state,
            isQuery,
            engineVersion,
        }, customIdentity);
        return params;
    }

    @debounce(1000)
    async getComList() {
        const params = this.getParams();
        this.data.set('table.loading', true);
        try {
            const res = await this.$http.paloPost('slowAndLargeQuery', params);
            this.data.set('table.datasource', res.auditLogsList || []);
            this.data.set('code', res.code || 0);
            this.data.set('pager.totalCount', res?.total || 0);
            this.data.set('table.loading', false);

            if (res.code === 504) {
                Notification.error('集群无法连接，请检查集群状态是否正常');
            }
        }
        catch (e) {
            this.data.set('table.loading', false);
        }
    }

    async installPlugin() {
        const deployId = this.data.get('deployId');
        this.data.set('installing', true);
        try {
            const res = await this.$http.paloPost('auditLoader', {deployId});
            if (res.success) {
                setTimeout(async () => {
                    const data = await this.$http.paloPost('paloDeployDetail', { deployId });
                    if (data.status === 'Running') {
                        this.data.set('installing', false);
                        Notification.error('安装成功');
                        this.refresh();
                    }
                    this.refresh();
                }, 6000);
            }
        }
        catch (e) {
            // Notification.error(`安装失败：${e.error.message}`);
            this.data.set('installing', false);
        }
    }
    refresh() {
        this.getComList();
    }
}