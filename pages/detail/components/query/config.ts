import moment from 'moment';
import { formatTime } from '@common/utils/index';
import {AllEnum} from '@common/config/index';
const allEnum = AllEnum.toArray();
const tans2MB = (size: number) => {
    return (size / 1024 / 1024).toFixed(2) || '-';
};

export type sqlType = {
    queryId: string;
    isQuery: boolean;
    catalog: string;
    db: string;
    stmt: string;
    time: number;
    queryTime: number;
    state: string;
    scanBytes: number;
    scanRows: number;
    returnRows: number;
    peakMemoryBytes: number;
    frontendIp: string;
    user: string;
    clientIp: string;
}

export const dateRangePickerShortcuts = [
    {
        text: '近15分钟',
        onClick(picker) {
            picker.setValueByShortCut({
                begin: moment().subtract('minute', 15).toDate(),
                end: moment().toDate()
            });
        }
    },
    {
        text: '近30分钟',
        onClick(picker) {
            picker.setValueByShortCut({
                begin: moment().subtract('minute', 30).toDate(),
                end: moment().toDate()
            });
        }
    },
    {
        text: '近1小时',
        onClick(picker) {
            picker.setValueByShortCut({
                begin: moment().subtract('hour', 1).toDate(),
                end: moment().toDate()
            });
        }
    },
];

export const columnsSource = [
    {
        value: 'queryId',
        text: 'SQL ID',
        disabled: true
    },
    {
        value: 'isQuery',
        text: 'SQL 类型',
        disabled: true
    },
    {
        value: 'user',
        text: '用户名',
    },
    {
        value: 'catalog',
        text: 'Catalog',
        disabled: true
    },
    {
        value: 'db',
        text: '数据库',
        disabled: true
    },
    {
        value: 'stmt',
        text: 'SQL 语句',
        disabled: true
    },
    {
        value: 'time',
        text: '执行开始时间',
    },
    {
        value: 'queryTime',
        text: '运行时间(s)'
    },
    {
        value: 'state',
        text: '状态'
    },
    {
        value: 'scanBytes',
        text: '扫描量（MB）'
    },
    {
        value: 'scanRows',
        text: '扫描行'
    },
    {
        value: 'returnRows',
        text: '返回行数'
    },
    {
        value: 'clientIp',
        text: '源地址'
    },
    {
        value: 'frontendIp',
        text: 'FE IP'
    },
    {
        value: 'peakMemoryBytes',
        text: '内存使用量(MB)'
    },
];

export const columns = [
    {
        name: 'queryId',
        label: 'SQL ID',
        width: 200,
        fixed: 'left'
    },
    {
        name: 'isQuery',
        label: 'SQL 类型',
        width: 120,
        render: (row: sqlType) => {
            return row.isQuery ? '查询' : '非查询';
        },
        filter: {
            options: [
                ...allEnum,
                {
                    text: '查询',
                    value: 1
                },
                {
                    text: '非查询',
                    value: 0
                }
            ],
            value: allEnum[0].value
        },
    },
    {
        name: 'user',
        label: '用户名',
        width: 120,
    },
    {
        name: 'catalog',
        label: 'Catalog',
        width: 140,
    },
    {
        name: 'db',
        label: '数据库',
        width: 120
    },
    {
        name: 'stmt',
        label: 'SQL 语句',
        width: 160
    },
    {
        name: 'time',
        label: '执行开始时间',
        width: 150,
        sortable: true,
        render: (row: sqlType) => {
            return formatTime(row.time);
        }
    },
    {
        name: 'queryTime',
        label: '运行时间(s)',
        width: 120,
        render: (row: sqlType) => {
            return row.queryTime ? row.queryTime / 1000 : '--';
        },
        sortable: true,
    },
    {
        name: 'state',
        label: '状态',
        width: 100,
        filter: {
            options: [
                ...allEnum,
                {
                    text: 'EOF',
                    value: 'EOF'
                },
                {
                    text: 'ERR',
                    value: 'ERR'
                },
                {
                    text: 'OK',
                    value: 'OK'
                }
            ],
            value: allEnum[0].value
        },
    },
    {
        name: 'scanBytes',
        label: '扫描量（MB）',
        width: 130,
        render: (row: sqlType) => {
            return tans2MB(row.scanBytes)
        },
        sortable: true,
    },
    {
        name: 'scanRows',
        label: '扫描行',
        width: 150,
        sortable: true,
    },
    {
        name: 'returnRows',
        label: '返回行数',
        width: 150,
        sortable: true,
    },
    {
        name: 'clientIp',
        label: '源地址',
        width: 120
    },
    {
        name: 'frontendIp',
        label: 'FE IP',
        width: 120
    },
    {
        name: 'peakMemoryBytes',
        label: '内存使用量(MB)',
        width: 120,
        render: (row: sqlType) => {
            return tans2MB(row.peakMemoryBytes);
        }
    },
];