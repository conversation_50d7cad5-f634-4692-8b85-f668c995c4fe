/**
 * 手动变配
 *
 * @file upgrade-manual.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {OrderConfirm} from '@baiducloud/billing-sdk/san';
import {OrderItem} from '@baiducloud/billing-sdk';
import {Steps, Form, InputNumber, Select, Alert, Notification} from '@baidu/sui';
import {getDiskNameFormType, getDiskTypeFormType} from '../../../create/components/create-utils';
import {PayType, PAYTYPE} from '@common/config';
import './index.less';

const klass = 'upgrade-manual';

const diskTypeMap = {
    enhanced_ssd_pl1: 'enhancedSsdPl1',
    enhanced_ssd_pl2: 'enhancedSsdPl2',
    premium_ssd: 'premiumSsd'
}
export default class extends Component {
    static template = html`
    <div class="${klass}">
        <s-steps
            current="{{current}}"
            type="normal"
            style="width: 323px;"
            space="223"
            on-change="stepChange">
            <s-step
                s-for="step in steps.datasource"
                title="{{step.title}}"
                description="{{step.description}}"
            />
        </s-steps>
        <s-alert s-if="{{showAlert}}" skin="info" showIcon="{{false}}">当前计算组已经开启分时弹性，如需调整配置，请先关闭分时弹性策略，<a href="javascript:void(0)" on-click="closeFlexible">立即关闭</a></s-alert>
        <div style="display:{{current === 1 ? 'block' : 'none'}}; width: 100%;">
            <s-form
                class="${klass}-form-wrapper"
                s-ref="form"
                label-align="left"
                rules="{{rules}}"
                data="{=formData=}"
            >
                <s-form-item label="计算组名称：">
                    {{computeItem.computeGroupName}}
                </s-form-item>
                <s-form-item label="计费方式：">
                    {{computeItem.productType | formatProductType}}
                </s-form-item>
                <s-form-item label="计算资源：">
                    {{computeItem.cuNums}}CU
                </s-form-item>
                <s-form-item label="缓存配置：">
                    {{diskName}} {{computeItem.cacheCapacity}}GB
                </s-form-item>
                <s-form-item label="目标计算资源：" prop="cuNums">
                    <s-select
                        class="mr8"
                        value="{{formData.cuNums}}"
                        on-change="onCuNumsChange"
                    >
                        <s-option
                            s-for="item in cuDatasource"
                            value="{{item.value}}"
                            label="{{item.label}}"
                            disabled="{{item.disabled}}"
                        >
                            <div class="cuNum-option">
                                <span>{{item.label}}</span>
                                <span s-if="item.disabled" class="sellout">售罄</span>
                            </div>
                        </s-option>
                    </s-select> CU
                </s-form-item>
                <s-form-item label="缓存配置：" prop="cacheCapacity">
                    <s-select
                        class="mr8"
                        value="{{diskName}}"
                        disabled
                    />
                    <s-input-number
                        class="mr5"
                        width="100"
                        value="{{formData.cacheCapacity}}"
                        max="{{cdsLimit.cds_max}}"
                        min="{{cdsLimit.cds_min}}"
                        step="{{cdsLimit.cds_step_lengh}}"
                        on-change="onCacheCapacityChange"
                    />GB
                    <p class="mt8 water-level" s-if="{{formData.cacheCapacity < cdsLimit.cds_water_level}}">
                        低于{{cdsInfo.cds_water_level}}水位线，会触发cds降配，存量节点缓存会全部失效
                    </p>
                </s-form-item>
                <p class="error" s-if="showError">单次配置变更中不支持对计算资源及缓存，进行升配和降配两种配置变更操作</p>
            </s-form>
        </div>
        <div s-if="current === 2">
            <order-confirm
                items="{{items}}"
                sdk="{{sdk}}"
                merge-by="orderServiceType"
                theme="default"
                useCoupon="{{computeItem.productType === ${PAYTYPE.PREPAY}}}"
            />
        </div>
    </div>`;

    static components = {
        's-steps': Steps,
        's-step': Steps.Step,
        's-option': Select.Option,
        'order-confirm': OrderConfirm,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        's-select': Select,
        's-alert': Alert
    };

    static filters = {
        formatProductType(value: string): string {
            return PayType[value];
        }
    }

    static computed = {
        diskName(): string | undefined {
            const computeItem = this.data.get('computeItem');
            return getDiskNameFormType(computeItem.diskType);
        },
        showAlert(): boolean {
            const computeItem = this.data.get('computeItem');
            const current = this.data.get('current');
            const enableFlex = this.data.get('enableFlex')
            return current === 1 && computeItem.productType === PAYTYPE.POSTPAY && enableFlex;
        },
        cdsLimit(): object {
            const cdsInfo = this.data.get('cdsInfo');
            const expectCuNums = this.data.get('formData.cuNums');
            const diskType = this.data.get('computeItem.diskType');
            const formateDiskType = diskTypeMap[diskType];
            const targetItem = cdsInfo[formateDiskType]?.find(i => i.expectCuNums === expectCuNums) || {};

            return {
                cds_recommend: targetItem.cdsRecommend,
                cds_water_level: targetItem.cdsWaterLevel,
                cds_max: targetItem.cdsMax,
                cds_min: targetItem.cdsMin,
                cds_step_lengh: targetItem.cdsStepLength
            }
        },
        resizeType(): number {
            const computeItem = this.data.get('computeItem');
            const formData = this.data.get('formData');
            const cdsInfo = this.data.get('cdsInfo');
            const formateDiskType = diskTypeMap[computeItem.diskType];
            const targetItem = cdsInfo[formateDiskType]?.find(i => i.expectCuNums === formData.cuNums) || {};

            if (computeItem.cuNums !== formData.cuNums) {
                return targetItem.cdsWaterLevel !== formData.cacheCapacity ? 2 : 1;
            }
            else if(targetItem.cdsWaterLevel !== formData.cacheCapacity) {
                return 0;
            }
            return -1;
        }
    }

    initData() {
        return {
            steps: {
                datasource: [
                    {
                        title: '变更Palo配置'
                    },
                    {
                        title: '确认订单'
                    }
                ]
            },
            formData: {
                cuNums: 0,
                cacheCapacity: 0
            },
            items: [],
            price: 0,
            dayCount: 0,
            moduleSlots: [],
            packageStockList: [],
            rules: {
                cuNums: [{required: true, message: '请选择计算资源'}],
                cacheCapacity: [{required: true, message: '请设置缓存配置大小'}]
            },
            pricing: true,
            cdsInfo: {},
            showError: false
        };
    }

    async attached() {
        const computeItem = this.data.get('computeItem');
        this.data.set('formData.cuNums', computeItem.cuNums);
        this.data.set('formData.cacheCapacity', parseInt(computeItem.cacheCapacity));
        await this.queryPrice();
    }

    initOrder() {
        this.ref('form').validateFields().then(async () => {
            const {price, dayCount} = this.data.get('');
            const {computeGroupName, productType, cuNums, cacheCapacity, diskType} = this.data.get('computeItem');
            const formData = this.data.get('formData');
            const currentItems = [
                {label: '计算组名称', value: computeGroupName},
                {label: '计算组ID', value: computeGroupName},
                {label: '当前计算资源', value: cuNums + 'CU'},
                {label: '目标计算资源', value: formData.cuNums + 'CU'},
                {label: '当前缓存配置', value: getDiskNameFormType(diskType) + ' ' + cacheCapacity + 'GB'},
                {label: '目标缓存配置', value: getDiskNameFormType(diskType) + ' ' + formData.cacheCapacity + 'GB'},
            ];
            const orderItem = new OrderItem({
                type: 'NEW',
                serviceName: '数据仓库（Palo）',
                serviceType: 'PALO',
                configName: '价格',
                count: 1,
                duration: productType === PAYTYPE.POSTPAY ? 1 : dayCount,
                region: window.$context.getCurrentRegion().id,
                managePrice: false,
                productType,
                timeUnit: productType === PAYTYPE.POSTPAY ? 'MINUTE' : 'DAY',
                price,
                configDetail: currentItems
            });
            this.data.set('items', [orderItem]);
        });
    }

    getParams() {
        const {diskType,  computeGroupId, productType} = this.data.get('computeItem');
        const {formData, resizeType} = this.data.get('');
        const {deployId, deployName, engineVersion} = this.data.get('deploy');

        return {
            name: deployName,
            productType,
            deployId,
            region: window.$context.getCurrentRegion().id,
            resizeType,
            cgId: computeGroupId,
            computeGroupInfo: [
                {
                    name: 'CU',
                    cuNums: formData.cuNums,
                    desiredVersion: engineVersion,
                    diskSlotInfo: {
                        type: getDiskTypeFormType(diskType),
                        size: formData.cacheCapacity
                    }
                }
            ]
        };
    }

    getCoupons() {
        const items = this.data.get('items') || [];
        const couponId = items[0]?.couponId ? items[0]?.couponId : '';
        // 不选择优惠券
        if (!couponId) {
            return [];
        }
        return [{
            type: 'coupon',
            values: [couponId]
        }];
    }

    async checkParams() {
        const params = this.getParams();
        const {cacheCapacity, cuNums} = this.data.get('formData');
        const {cacheCapacity: originalCacheCapacity, cuNums: originalCuNums} = this.data.get('computeItem');
        if (params.resizeType === -1) {
            Notification.error('配置未改变');
            return;
        }
        // 检查是否一个升配一个降配，提示错误不能进入下一步
        else if ((cacheCapacity > originalCacheCapacity && cuNums < originalCuNums) || (cuNums > originalCuNums&& cacheCapacity < originalCacheCapacity)) {
            this.data.set('showError', true);
            return;
        }
        try {
            await this.$http.paloPost('upgradeCuCheck', params);
            return true;
        }
        catch(e) {
            // 提示参数错误
            return false;
        }
        
    }


    onCuNumsChange(target: {value: string}) {
        this.data.set('formData.cuNums', target.value);
        const cdsInfo = this.data.get('cdsInfo');
        const diskType = this.data.get('computeItem.diskType');
        const formateDiskType = diskTypeMap[diskType];
        const targetItem = cdsInfo[formateDiskType]?.find(i => i.expectCuNums === target.value) || {};
        this.onCacheCapacityChange({ value: targetItem?.cdsWaterLevel });
        this.data.set('showError', false);
    }

    onCacheCapacityChange(target: {value: number}) {
        this.data.set('formData.cacheCapacity', target.value);
        this.queryPrice();
        this.data.set('showError', false);
    }

    async queryPrice() {
        const {productType, diskType, computeGroupId, expireTimestamp} = this.data.get('computeItem');
        const {formData, deploy} = this.data.get('');
        const isPostPay = productType === PAYTYPE.POSTPAY;
        this.data.set('pricing', true);
        const method = isPostPay ? 'consumerGroupPrice' : 'getUpgradePrice';
        const postpayParams = {
            region: window.$context.getCurrentRegion().id,
            productType,
            time: 1,
            modules: [{
                name: 'lakepalo',
                type: 'cu',
                cuNum: formData.cuNums,
                desiredVersion: deploy.engineVersion,
                diskSlotInfo: {
                    type: getDiskTypeFormType(diskType),
                    size: formData.cacheCapacity
                }
            }],
            originalPrice: false,
        };
        const prepayParams = {
            ...postpayParams,
            cgId: computeGroupId,
            expireTimestamp,
            deployId: deploy.deployId,
        };
        try {
            const result = await this.$http.paloPost(method, isPostPay ? postpayParams : prepayParams);
            const price = isPostPay ? result.total : result.price;
            const dayCount = isPostPay ? 0 : result.dayCount;
            this.data.set('price', price);
            this.data.set('dayCount', dayCount);
            this.data.set('pricing', false);
            this.fire('price-change', {
                price: price,
                dayCount: dayCount,
                pricing: false
            });
        }
        catch (e) {
            this.data.set('pricing', false);
            this.data.set('price', 0);
            this.fire('price-change', {
                price: 0,
                dayCount: 0,
                pricing: false
            });
        }
    }

    closeFlexible() {
        this.fire('tab-change', {});
    }
}
