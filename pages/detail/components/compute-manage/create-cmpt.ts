/**
 * 创建计算组
 *
 * @file create-cmpt.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {OrderConfirm} from '@baiducloud/billing-sdk/san';
import {OrderItem} from '@baiducloud/billing-sdk';
import {Steps, Form, InputNumber, Select, Alert, Input} from '@baidu/sui';
import {getDiskNameFormType, getDiskTypeFormType} from '../../../create/components/create-utils';
import {PAYTYPE} from '@common/config';
import {formatTime, daysUntil, getTimeStamp} from '@common/utils';
import {AppLegend, AppDetailCell} from '@baidu/sui-biz';
import {nameValidator} from '@common/utils/rules';
import './index.less';

const klass = 'create-cmpt';

export const timeDatasource = [1, 2, 3, 4, 5, 6, 7, 8, 9, 12].map(i => ({
    label: i === 12 ? '1年' : i + '个月',
    value: i,
    discount: i === 12
}));
export default class extends Component {
    static template = html`
    <div class="${klass}">
        <s-steps
            current="{{current}}"
            type="normal"
            style="width: 323px;"
            space="223"
            on-change="stepChange">
            <s-step
                s-for="step in steps.datasource"
                title="{{step.title}}"
                description="{{step.description}}"
            />
        </s-steps>
        <div style="display:{{current === 1 ? 'block' : 'none'}}; width: 100%;">
            <s-form
                class="${klass}-form-wrapper"
                s-ref="form"
                label-align="left"
                rules="{{rules}}"
                data="{=formData=}"
            >
                <app-legend label="当前集群信息：" noHighlight>
                    <s-detail-cell
                        divide="2"
                        labelWidth="100px"
                        datasource="{{datasource}}"
                    ></s-detail-cell>
                </app-legend>
                <app-legend label="计算组配置：" noHighlight>
                    <s-form-item label="付费方式：" prop="productType">
                     <div class="payment-card-list">
                        <div
                            s-for="item in productTypeList"
                            on-click="onProductTypeChange(item.value)"
                            class="payment-card-item {{formData.productType === item.value ? 'active' : ''}}"
                        >
                            <i class="payment-card-item__icon {{item.class}}"></i>
                            <div class="payment-card-item__info">
                                <h4 class="payment-card-item__info-title">{{item.text}}</h4>
                                <p class="payment-card-item__info-desc">{{item.desc}}</p>
                            </div>
                            <span s-if="item.tag" class="payment-card-item__tag">{{item.tag}}</span>
                        </div>
                    </div>
                    </s-form-item>
                    <s-form-item label="计算组名称：" prop="cgName">
                        <s-input
                            width="250"
                            autocomplete="new-password"
                            value="{= formData.cgName =}"
                            on-change="clusterNameChange"
                        />
                        <p slot="help">支持字母(a-z及A-Z)、数字(0-9)，长度小于20个字符</p>
                    </s-form-item>
                    <s-form-item label="计算资源：" prop="cuNums">
                        <s-select
                            class="mr8"
                            value="{{formData.cuNums}}"
                            width="160"
                            on-change="onCuNumsChange"
                        >
                        <s-option
                            s-for="item in cuDatasource"
                            value="{{item.value}}"
                            label="{{item.label}}"
                            disabled="{{item.disabled}}"
                        >
                            <div class="cuNum-option">
                                <span>{{item.label}}</span>
                                <span s-if="item.disabled" class="sellout">售罄</span>
                            </div>
                        </s-option>
                        </s-select> CU
                    </s-form-item>
                    <s-form-item label="缓存配置：" prop="diskSlotInfo">
                        <s-select
                            class="mr8"
                            value="{{formData.diskSlotInfo.type}}"
                            datasource="{{diskDatasource}}"
                            on-change="onDiskTypeChange"
                        />
                        <s-input-number
                            class="mr5"
                            width="100"
                            value="{{formData.diskSlotInfo.size}}"
                            min="{{cdsMin}}"
                            step="{{cdsStepSize}}"
                            max="{{cdsMax}}"
                            on-change="onDiskSizeChange"
                        />GB
                    </s-form-item>
                    <s-form-item label="购买时长：" prop="time" s-if="{{needTimeSelect}}">
                        <s-select
                            class="mr8"
                            value="{{formData.time}}"
                            on-change="onTimeChange"
                        >
                        <s-option
                            s-for="item in timeDatasource"
                            value="{{item.value}}"
                            label="{{item.label}}"
                        >
                            <div class="cuNum-option">
                                <span>{{item.label}}</span>
                                <span s-if="item.discount" class="sale">SALE</span>
                            </div>
                        </s-option>
                        </s-select>
                    </s-form-item>
                    </app-legend>
                </s-form>
        </div>
        <div s-if="current === 2">
            <order-confirm
                items="{=items=}"
                sdk="{{sdk}}"
                merge-by="orderServiceType"
                theme="default"
                useCoupon="{{formData.productType === 'prepay'}}"
            />
        </div>
    </div>`;

    static components = {
        's-steps': Steps,
        's-step': Steps.Step,
        's-option': Select.Option,
        'order-confirm': OrderConfirm,
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        's-select': Select,
        's-alert': Alert,
        'app-legend': AppLegend,
        's-input': Input,
        's-detail-cell': AppDetailCell,
    };

    static computed = {
        cuDatasource(): object[] {
            const moduleSlots = this.data.get('moduleSlots') || [];
            const packageStockList = this.data.get('packageStockList') || [];
            const availableZone = this.data.get('deploy.availableZone');

            const res = moduleSlots.map(item => ({
                label: item.cuCount,
                value: item.cuCount,
                diskDatasource: item.decoupledDiskSlotInfo || [],
                disabled: _.findIndex(packageStockList, i => i.logicalZone === availableZone && i.packageVersion === item.slot_type && i.available) === -1
            }));
            return res;
        },
        needTimeSelect(): boolean {
            const deploy = this.data.get('deploy');
            const productType = this.data.get('formData.productType');
            return !deploy?.expireTime && productType === PAYTYPE.PREPAY;
        }
    }

    initData() {
        return {
            steps: {
                datasource: [
                    {
                        title: '计算组配置'
                    },
                    {
                        title: '确认订单'
                    }
                ]
            },
            items: [],
            price: {
                total: 0,
                paloCuPrice: 0,
                paloCachePrice: 0
            },
            rules: {
                cgName: [
                    {required: true, message: '请输入计算组名称'},
                    {validator: nameValidator}
                ],
                cuNums: [{required: true, message: '请选择计算资源'}],
                diskSlotInfo: [{
                    validator: (rule: any, value: any, callback: any) => {
                        const diskSlotInfo = this.data.get('formData.diskSlotInfo');
                        if (!diskSlotInfo.type || !diskSlotInfo.size) {
                            callback(new Error('请选择缓存配置'));
                        }
                        callback();
                    }
                }],
                time: [{required: true, message: '请选择购买时长'}]
            },
            pricing: true,
            productTypeList: [
                {
                    alias: 'PREPAID',
                    text: '包年包月',
                    value: 'prepay',
                    desc: '先付费后使用，价格更低廉',
                    tag: '推荐',
                    class: 'prepaid'
                },
                {
                    alias: 'POSTPAID',
                    text: '按量付费',
                    value: 'postpay',
                    desc: '先使用后付费，按需开通',
                    class: 'postpaid'
                }
            ],
            labelCol: {
                span: 4
            },
            timeDatasource: timeDatasource
        };
    }

    async attached() {
        const deploy = this.data.get('deploy');
        this.data.set('datasource', [
            {
                label: '地域：',
                value: window.$context.getCurrentRegion().label
            },
            {
                label: '可用区：',
                value: '可用区' + deploy.availableZone?.slice(4,)
            },
            {
                label: '所在网络(VPC)：',
                value: deploy.vpc
            },
            {
                label: '子网：',
                value: deploy.subnet
            },
            {
                label: '到期时间：',
                value: formatTime(deploy.expireTime)
            }
        ]);
        try {
            await this.getCuslots();
        }
        catch (e) {
            console.error(e);
        }
        this.fire('price-change', {
            price: {
                total: 0,
                paloCuPrice: 0,
                paloCachePrice: 0,
            }
        });
    }

    async getCuslots() {
        const result = await this.$http.paloPost('listCuSlot');
        const {moduleSlots, packageStockList} = result;
        this.data.set('moduleSlots', moduleSlots);
        this.data.set('packageStockList', packageStockList);
    }

    checkValidate() {
        return this.ref('form').validateFields();
    }

    initOrder() {
        const price = this.data.get('price');
        const {availableZone, expireTime} = this.data.get('deploy');
        const {productType, cuNums, time, diskSlotInfo} = this.data.get('formData');

        const baseItems = {
            type: 'NEW',
            serviceType: 'PALO',
            configName: '价格',
            count: 1,
            duration:  productType === PAYTYPE.PREPAY && expireTime ? daysUntil(expireTime) : time,
            region: window.$context.getCurrentRegion().id,
            managePrice: false,
            productType,
            timeUnit: productType === PAYTYPE.POSTPAY ? 'MINUTE' : expireTime ? 'day' : 'MONTH',
        };

        const cuItem = new OrderItem({
            ...baseItems,
            serviceName: '计算组',
            orderServiceType: 'palo_group',
            price: price.paloCuPrice,
            configDetail: [
                {label: '可用区', value: '可用区' + availableZone?.slice(4,)},
                {label: '计算资源', value: cuNums + 'CU'},
            ]
        });
        const diskItem = new OrderItem({
            ...baseItems,
            serviceName: '缓存配置',
            price: price.paloCachePrice,
            orderServiceType: 'palo_storage',
            configDetail: [
                {label: '可用区', value: '可用区' + availableZone?.slice(4,)},
                {label: '缓存类型', value: getDiskNameFormType(diskSlotInfo.type)},
                {label: '缓存容量', value: diskSlotInfo.size}
            ]
        });
        this.data.set('items', [cuItem, diskItem]);
    }

    getParams() {
        const {
            expireTime,
            engineVersion,
            availableZone,
            deployId
        } = this.data.get('deploy');
        const {productType, cuNums, diskSlotInfo, cgName, time} = this.data.get('formData');

        return {
            ...productType === PAYTYPE.PREPAY && expireTime ? {
                expireTimestamp: getTimeStamp(expireTime),
            } : {},
            cgName,
            region: window.$context.getCurrentRegion().id,
            availableZone,
            deployId,
            productType,
            ...(!expireTime && time && productType === PAYTYPE.PREPAY) ? {
                time
            } : {},
            computeGroupInfos: [
                {
                    name: 'CU',
                    cuNums,
                    desiredVersion: engineVersion,
                    diskSlotInfo: {
                        type: getDiskTypeFormType(diskSlotInfo.type),
                        size: diskSlotInfo.size
                    }
                }
            ]
        };
    }

    // 获取选择的代金券数据
    getCoupons() {
        const items = this.data.get('items') || [];
        const couponId = items[0]?.couponId ? items[0]?.couponId : '';
        // 不选择优惠券
        if (!couponId) {
            return [];
        }
        return [{
            type: 'coupon',
            values: [couponId]
        }];
    }

    async checkParams() {
        await this.checkValidate();
        const params = this.getParams();
        try {
            await this.$http.paloPost('createCuCheck', params);
            return true;
        }
        catch(e) {
            return false;
        }
        
    }

    onCuNumsChange(target: {value: string}) {
        this.data.set('formData.cuNums', target.value);
        const cuDatasource = this.data.get('cuDatasource');
        const cuItem = _.find(cuDatasource, item => item.value === target.value) || {};
        const diskDatasource = cuItem?.diskDatasource.map(i => ({
            label: getDiskNameFormType(i.cdsType),
            value: i.cdsType,
            ...i
        })) || [];
        this.data.set('diskDatasource', diskDatasource);
        this.onDiskTypeChange({value: diskDatasource[0].value});
    }

    onDiskTypeChange(e: { value: any }) {
        const diskDatasource = this.data.get('diskDatasource');
        const diskItem = _.find(diskDatasource, i => i.value === e.value) || {};
        const {cdsMin, cdsMax, cdsRecommend, cdsStepSize, cdsType} = diskItem;
        this.data.set('formData.diskSlotInfo.type', cdsType);
        this.data.set('cdsMin', cdsMin);
        this.data.set('cdsMax', cdsMax);
        this.data.set('cdsStepSize', cdsStepSize);
        this.onDiskSizeChange({value: cdsRecommend});
    }

    onProductTypeChange(productType: string) {
        this.data.set('formData.productType', productType);
        this.queryPrice();
    }

    onDiskSizeChange(target: {value: number}) {
        this.data.set('formData.diskSlotInfo.size', target.value);
        this.queryPrice();
    }

    onTimeChange(target: {value: number}) {
        this.data.set('formData.time', target.value);
        this.queryPrice();
    }

    async queryPrice() {
        const formData = this.data.get('formData');
        const {productType, cuNums, diskSlotInfo, time} = formData;
        const {deployId, engineVersion, expireTime} = this.data.get('deploy');
        const needTimeSelect = this.data.get('needTimeSelect');
        if (!cuNums || !diskSlotInfo.size || !diskSlotInfo.type || (needTimeSelect && !time)) {
            // 参数为空时不触发询价，否则会报错
            return;
        }
        this.data.set('pricing', true);
        try {
            const result = await this.$http.paloPost('getCreatePrice', {
                region: window.$context.getCurrentRegion().id,
                deployId,
                productType,
                expireTimestamp: getTimeStamp(expireTime),
                ...(!expireTime && time) ? {
                    time
                } : {},
                modules: [{
                    name: 'lakepalo',
                    type: 'cu',
                    cuNum: cuNums,
                    desiredVersion: engineVersion,
                    diskSlotInfo: {
                        type: getDiskTypeFormType(diskSlotInfo.type),
                        size: diskSlotInfo.size
                    }
                }],
                originalPrice: false
            });
            this.data.set('price', {...result});
            this.data.set('pricing', false);
            this.fire('price-change', {
                price: {...result},
                productType,
                formData: {...formData}
            });
        }
        catch (e) {
            this.data.set('pricing', false);
            this.data.set('price', {
                total: 0,
                paloCuPrice: 0,
                paloCachePrice: 0,
            });
        }
    }
}
