.palo-compute-list {
    .s-list-content {
        background: #ffffff;
        .page-title {
            color: #151b26;
            font-weight: 500;
            font-size: 16px;
            background-color: #fff;
            padding: 24px 16px 0;
        }
        .table-full-wrap {
            margin: 0;
        }
        .s-table-row {

            .instance-editor {
                display: none;
            }
    
            &:hover {
    
                .instance-editor {
                    display: inline-block;
                }
            }
        }
    }
}


.footer {
    padding: 24px 16px;

    .protocol-wrap {
        display: flex;
        align-items: center;
    }
    .protocol-wrap-checkbox {
        margin-top: -2px;
        margin-right: -2px;
    }
    .protocol-error {
        color: #f33e3e;
        font-size: 12px;
        line-height: 20px;
        margin-left: 24px;
    }
    .price-button-wrap {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .price-wrap {
            display: flex;
            align-items: center;
            color: #5c5f66;
            .total-price-title {
                display: inline-block;
            }
            .price {
                color: #f33d3d;
                font-size: 20px;
                font-weight: 500;
            }
        }
    }
}

.footer-only-button {
    display: flex;
    justify-content: flex-end;
    margin: 24px 24px;
}

.upgrade-manual {
    &-form-wrapper {
        .s-form-item-label {
            width: 120px;
        }
    }

    .s-step .s-step-content {
        padding: 0 8px;

        .s-step-line {
            z-index: -1;
        }
    }
    .water-level {
        color: #84868c;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
    }
    .error {
        color: #f33d3d;
        margin-top: -16px;
    }
}

.automatic-config {
    .toggle-tip {
        border-radius: 6px;
        padding: 12px;
        background: #F7F7F9;
        color: var(--G4, #5C5F66);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        display: flex;
        justify-content: space-between;
    }
    .ellipsis {
        display: -webkit-box;
        line-clamp: 1;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
      
    .content.expand {
        -webkit-line-clamp: unset;
        line-clamp: unset;
    }
    .no-padding {
        padding: 0;
    }
    .error-tip {
        color: #f33d3d;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        width: 160px;
    }
    .no-flex-tip {
        color: #84868C;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
    }
}

.upgrade-drawer {
    .s-drawer-content {
        position: relative;

        .content-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
    }
    .s-drawer-footer {
        border-top: 1px solid #e8e9eB;
    }
    &-content {
        margin: 24px;
    }
}

.create-cmpt {
    .s-legend {
        border: none!important;
        padding-top: 0!important;
        .s-legend-label {
            margin-bottom: 24px;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    .s-steps {
        margin-top: 0;
    }
    .payment-card {
        &-list {
            display: flex;
        }
        &-item {
            position: relative;
            display: inline-flex;
            align-items: center;
            width: 260px;
            padding: 13px 0 13px 20px;
            border: 1px solid rgba(232, 233, 235, 1);
            border-radius: 6px;
            cursor: pointer;
            margin-right: 8px;

            &:hover {
                border: 1px solid rgba(36, 104, 242, 1);
            }

            &.active {
                background: #eef3fe;
                border: 1px solid rgba(36, 104, 242, 1);

                .payment-card-item__info-title {
                    color: #2468f2;
                }
            }

            &__icon {
                display: inline-block;
                width: 32px;
                height: 32px;
                margin-right: 20px;

                &.prepaid {
                    background-image: url(~@static/prepaid.png);
                }
                &.postpaid {
                    background-image: url(~@static/postpaid.png);
                }
            }

            &__info {
                &-title {
                    font-size: 14px;
                    line-height: 22px;
                    font-weight: 500;
                }

                &-desc {
                    font-size: 12px;
                    color: var(--text-sub-color);
                    line-height: 20px;
                    margin-top: 4px;
                }
            }

            &__tag {
                position: absolute;
                right: 0;
                top: 0;
                background-image: linear-gradient(90deg, #f3413f 22%, #f86454 100%);
                border-top-right-radius: 4px;
                line-height: 20px;
                padding: 0 4px;
                color: #fff;
                border-bottom-left-radius: 12px;
                clip-path: polygon(100% 0, 100% 100%, 12% 100%, 0 0);
                text-align: center;
                text-indent: 4px;
            }
        }
    }
    .cuNum-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .sale {
            background: #F33e3e;
            border-radius: 2px;
            display: inline-block;
            height: 20px;
            height: 20px;
            padding-left: 4px;
            padding-right: 4px;
            font-size: 12px;
            color: #FFFFFF;
            line-height: 20px;
            font-weight: 400;
        }
    }
    &-form-wrapper {
        .s-form-item-label {
            width: 110px;
        }

        .s-form-item-help {
            padding-bottom: 24px;
        }
    }
}

.create-compute-drawer, .upgrade-manual {
    .order-confirm-default .order-legend {
        border: 1px solid #e8e9eB;
        border-radius: 6px;
        .legend-header {
            line-height: 24px;
            padding: 24px 0 16px;
        }
        .content {
            display: flex;
            justify-content: space-between;
            .item {
                width: 50%;
    
                label {
                    width: 90px;
                }
            }
        }
    }
    .s-drawer-footer {
        border: 1px solid #e8e9eB;
    }
}
