/**
 * 计算组新建
 *
 * @file create-drawer.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {<PERSON><PERSON>, Drawer, Button, Notification, Tabs, Checkbox, Loading, Tooltip, Table} from '@baidu/sui';
import {BillingSDK} from '@baiducloud/billing-sdk';
import {TotalPrice} from '@baiducloud/billing-sdk/san';
import CreateCmpt, { timeDatasource } from './create-cmpt';
import {daysUntil} from '@common/utils';
import {PAYTYPE} from '@common/config';
import {getDiskNameFormType} from '../../../create/components/create-utils';
import {ContextService} from '@common/index';
import './index.less';

const klass = 'create-compute-drawer';

export default class extends Component {
    static template = html`
    <template>
        <s-drawer
            size="{{800}}"
            open="{=open=}"
            title="{{name}}"
            class="${klass}"
            maskClose="{{false}}"
        >
            <div class="${klass}-content">
                <create-cmpt
                    s-ref="createCmpt"
                    deploy="{{deploy}}"
                    sdk="{{sdk}}"
                    formData="{=formData=}"
                    current="{{current}}"
                    on-price-change="onPriceChange"
                />
            </div>
            <div slot="footer" class="footer">
                <template s-if="{{current === 2}}">
                    <div class="protocol-wrap">
                        <s-checkbox
                            value="{=checked=}"
                            checked="{=checked=}"
                        />
                        <span>我已阅读并同意
                            <a href="https://console.bce.baidu.com/iam/agreement-v2.html" target="_blank">
                                《百度智能云线上订购协议》
                            </a>
                        </span>
                    </div>
                    <span s-if="{{showError}}" class="protocol-error">请先阅读并同意服务协议信息</span>
                </template>
                <div class="protocol-wrap" s-if="{{current === 1}}">
                    <template s-if="{{deploy.autoRenewInfo}}">当前集群已开启自动续费，如需修改请到
                        <a href="https://console.bce.baidu.com/billing/renew/list">续费管理</a>
                        页面进行调整
                    </template>
                    <template s-else-if="{{deploy.productType === 'prepay'}}">当前集群未开启自动续费，未避免资源到期被释放建议，到<a href="https://console.bce.baidu.com/billing/renew/list">续费管理</a>
                        页面进行调整
                    </template>
                </div>
                <div class="price-button-wrap">
                    <div class="price-wrap">
                        <span
                            s-if="{{current === 1}}"
                        >
                            {{computedProductType}}：<span class="price">￥{{price.total}}{{timeUnitText}}</span>
                        </span>
                        <template s-else>
                            <total-price sdk="{{sdk}}" />
                            <span s-if="{{formData.productType === 'prepay'}}" class="price">{{timeUnitText}}</span>
                        </template>
                        <s-tooltip class="com-warning-tip ml16" s-if="{{current === 1}}">
                            <a href="javascript:void(0)">明细</a>
                            <div class="create-page-popover" slot="content">
                                <h3 style="margin-bottom:10px;font-weight:bold">
                                    {{priceDetailTitle}}
                                </h3>
                                <s-table
                                    columns="{{columns}}"
                                    datasource="{{priceDetail}}"
                                >
                                    <span slot="c-deploy">{{row.deploy | raw}}</span>
                                    <span slot="c-price" class="price-detail-num">{{row.price}}</span>
                                </s-table>
                            </div>
                        </s-tooltip>
                    </div>
                    <div>
                        <s-button on-click="onCancel" class="mr16" width="{{48}}">{{cancelText}}</s-button>
                        <s-button
                            on-click="onConfirm"
                            skin="primary"
                            disabled="{{okDisabled && false}}"
                            width="{{48}}"
                        >
                            {{okText}}
                        </s-button>
                    </div>
                </div>
            </div>
            <div slot="footer" s-else class="footer-only-button">
                <s-button on-click="onCancel" class="mr16" width="{{48}}">取消</s-button>
                <s-button
                    on-click="onConfirm"
                    skin="primary"
                    width="{{48}}"
                >
                    确认
                </s-button>
            </div>
        </s-drawer>
    </template>`;

    static components = {
        's-drawer': Drawer,
        's-alert': Alert,
        's-button': Button,
        'create-cmpt': CreateCmpt,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-checkbox': Checkbox,
        's-loading': Loading,
        'total-price': TotalPrice,
        's-tooltip': Tooltip,
        's-table': Table,
    };

    static computed = {
        okText(): string {
            return this.data.get('current') === 1 ? '下一步' : '提交订单';
        },
        cancelText(): string {
            return this.data.get('current') === 2 ? '上一步' : '取消';
        },
        okDisabled(): boolean {
            const pricing = this.data.get('pricing');
            const canConfirm = this.data.get('canConfirm');
            return pricing || !canConfirm;
        },
        timeUnitText(): string {
            const productType = this.data.get('formData.productType');
            const expireTime = this.data.get('deploy.expireTime');
            const time = this.data.get('formData.time');
            const timeUnit = timeDatasource.find(item => item.value === time)?.label;

            return productType === 'postpay' ? '/分钟' : expireTime ? `/${daysUntil(expireTime)}` : `${timeUnit ? '/' + timeUnit : ''}`;
        },
        computedProductType(): string {
            const productType = this.data.get('formData.productType');
            return productType === 'postpay' ? '按量付费' : '包年包月';
        }
    };

    initData() {
        return {
            name: '创建计算组',
            formData: {
                cgName: '',
                productType: 'prepay',
                time: '',
                cuNums: null,
                diskSlotInfo: {
                    type: '',
                    size: null
                }
            },
            open: true,
            okText: '',
            confirming: false,
            current: 1,
            price: 0,
            checked: false,
            pricing: true,
            canConfirm: true,
            loading: true,
            showError: false,
            region: ContextService.getCurrentRegion().id,
            columns: [
                {
                    name: 'item', label: '计费项', width: 100
                },
                {name: 'deploy', label: '配置', width: 200},
                {name: 'price', label: '参考价格', width: 100}
            ],
            priceDetail: [],
        };
    }

    attached() {
        this.watch('checked', value => {
            this.data.set('showError', !value);
        })
        this.data.set('sdk',
            new BillingSDK({
                OrderType: 'NEW',
                region: window.$context.getCurrentRegion().id,
                serviceType: 'palo'
            }, window.$context)
        );
    }

    async onConfirm() {
        this.data.set('showError', false);
        try {
            const checked = this.data.get('checked');
            this.data.set('canConfirm', true);
            if (this.data.get('current') === 1) {
                const checkPass = await this.ref('createCmpt')?.checkParams();
                if (!checkPass) {
                    // 参数检查未通过，不能进入下一步
                    return;
                }
                this.ref('createCmpt')?.initOrder();
                this.data.set('current', 2);
            }
            else {
                if (!checked) {
                    Notification.error('请先勾选协议');
                    this.data.set('showError', true);
                    return;
                }
                this.data.set('confirming', true);
                const params = this.ref('createCmpt')?.getParams();
                const coupon = this.ref('createCmpt')?.getCoupons() || [];
                const deploy = this.data.get('deploy');
                try {
                    const data = await this.$http.paloPost('createCuConfirm', {
                        items: [
                            {
                                config: {
                                    ...params,
                                    ...deploy?.autoRenewInfo ? {
                                        autoRenewInfo: deploy.autoRenewInfo,
                                    } : {},
                                },
                                paymentMethod: coupon
                            }
                        ]
                    });
                    this.data.set('confirming', false);
                    // 如果是预付费，加一个跳转成功的跳转，在新标签页打开billing页面
                    const sdk = this.data.get('sdk');
                    try {
                        const sdkResult = await sdk.checkPayInfo(data);
                        sdkResult.url && redirect(sdkResult.url);
                    } catch ({ url = null }) {
                        url && redirect(url);
                    }
                    this.onClose();
                }
                catch {
                    this.data.set('confirming', false);
                }
            }
        }
        catch {
            this.data.set('confirming', false);
        }
    }

    onPriceChange(e: {price: {paloCuPrice: number, paloCachePrice: number}, productType: string, formData: any}) {
        this.data.set('price', e.price);
        const {timeUnitText, deploy} = this.data.get('');
        const region = ContextService.SERVICE_TYPE.PALO.region;
        if (!e.formData?.productType) {
            return;
        }
        const priceDetail =  [
            {
                item: '计算组',
                deploy: `<p>付费方式：${PAYTYPE.getTextFromValue([e.formData.productType])}</p>
                         <p>计算资源：${e.formData.cuNums}CU</p>`,
                price: e.price.paloCuPrice + '元' + timeUnitText
            },
            {
                item: '缓存及存储',
                deploy: `<p>缓存类型：${getDiskNameFormType(e.formData.diskSlotInfo.type)}</p>
                         <p>缓存容量：${e.formData.diskSlotInfo.size}GB</p>
                         <p>存储配置：按实际用量计费</p>`,
                price: e.price.paloCachePrice + '元' + timeUnitText
            }
        ];
        this.data.set('priceDetail', priceDetail);
        this.data.set('priceDetailTitle', region[deploy.region] + ' 可用区' + deploy.availableZone.slice(4));
    }

    onCancel() {
        if (this.data.get('current') === 2) {
            this.data.set('current', 1);
        }
        else {
            this.onClose();
        }
    }
    // 关闭
    onClose() {
        this.data.set('open', false);
        this.dispose();
    }
}
