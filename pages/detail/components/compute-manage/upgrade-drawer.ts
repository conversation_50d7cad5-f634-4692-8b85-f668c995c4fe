/**
 * 计算组变配
 *
 * @file upgrade-drawer.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, redirect} from '@baiducloud/runtime';
import {<PERSON><PERSON>, Drawer, Button, Notification, Tabs, Checkbox, Loading} from '@baidu/sui';
import UpgradeManual from './upgrade-manual';
import AutomaticConfig from './automatic-config';
import {BillingSDK} from '@baiducloud/billing-sdk';
import {TotalPrice} from '@baiducloud/billing-sdk/san';
import {PAYTYPE} from '@common/config';
import './index.less';

const klass = 'upgrade-drawer';

export default class extends Component {
    static template = html`
    <template>
        <s-drawer
            size="{{640}}"
            open="{=open=}"
            title="{{name}}"
            class="${klass}"
            maskClose="{{false}}"
        >
            <s-loading loading="{{loading}}" class="content-loading">
                <div class="${klass}-content">
                    <div s-if="{{computeItem.productType === 'postpay'}}">
                        <s-tabs active="{{activeKey}}" on-change="handleChangeActive">
                            <s-tabpane
                                s-for="item in tabs"
                                label="{{item.label}}"
                                key="{{item.key}}"
                            >
                            <div s-if="{{item.key === 'manual'}}">
                                <upgrade-manual
                                    s-ref="manualConfig"
                                    computeItem="{{computeItem}}"
                                    deploy="{{deploy}}"
                                    current="{{current}}"
                                    moduleSlots="{{moduleSlots}}}"
                                    cdsInfo="{{cdsInfo}}"
                                    sdk="{{sdk}}"
                                    packageStockList="{{packageStockList}}"
                                    cuDatasource="{{cuDatasource}}"
                                    enableFlex="{{enableFlex}}"
                                    on-price-change="onPriceChange"
                                    on-tab-change="handleChangeActive({value: {key: 'automatic'}})"
                                />
                            </div>
                            <div s-if="{{item.key === 'automatic'}}">
                                <automatic-config
                                    s-ref="automaticConfig"
                                    computeItem="{{computeItem}}"
                                    moduleSlots="{{moduleSlots}}}"
                                    packageStockList="{{packageStockList}}"
                                    cuDatasource="{{cuDatasource}}"
                                    on-change-params="handleChangeParams"
                                    on-change-enable-flex="handleChangeEnableFlex"
                                />
                            </div>
                            </s-tabpane>
                        </s-tabs>
                    </div>
                    <upgrade-manual
                        s-else
                        s-ref="manualConfig"
                        computeItem="{{computeItem}}"
                        current="{{current}}"
                        deploy="{{deploy}}"
                        moduleSlots="{{moduleSlots}}}"
                        cdsInfo="{{cdsInfo}}"
                        sdk="{{sdk}}"
                        packageStockList="{{packageStockList}}"
                        cuDatasource="{{cuDatasource}}"
                        on-price-change="onPriceChange"
                    />
                </div>
            </s-loading>
            <div slot="footer" class="footer" s-if="{{activeKey === 'manual'}}">
                <div class="protocol-wrap" s-if="{{current === 2}}">
                    <s-checkbox
                        value="{=checked=}"
                        checked="{=checked=}"
                        class="protocol-wrap-checkbox"
                    />
                    <span>我已阅读并同意
                        <a href="https://console.bce.baidu.com/iam/agreement-v2.html" target="_blank">
                            《百度智能云线上订购协议》
                        </a>
                        <p s-if="{{showError}}" class="protocol-error">请先阅读并同意服务协议信息</p>
                    </span>
                </div>
                <div class="price-button-wrap">
                    <span
                        s-if="{{current === 1}}"
                        class="price-wrap"
                    >
                        {{computeItem.productType | formatPayType}}：<span class="price">￥{{price}}/{{timeUnitText}}</span>
                    </span>
                    <total-price s-else class="price-wrap" sdk="{{sdk}}" />
                    <div>
                        <s-button
                            on-click="onCancel"
                            class="mr16"
                            width="{{46}}"
                        >
                            {{cancelText}}
                        </s-button>
                        <s-button
                            on-click="onConfirm"
                            skin="primary"
                            loading="{{checking}}"
                            disabled="{{okDisabled}}"
                        >
                            {{okText}}
                        </s-button>
                    </div>
                </div>
            </div>
            <div slot="footer" s-else class="footer-only-button">
                <s-button on-click="onCancel" class="mr16" width="{{48}}">取消</s-button>
                <s-button
                    on-click="onConfirm"
                    skin="primary"
                    width="{{48}}"
                >
                    确认
                </s-button>
            </div>
        </s-drawer>
    </template>`;

    static components = {
        's-drawer': Drawer,
        's-alert': Alert,
        's-button': Button,
        'upgrade-manual': UpgradeManual,
        'automatic-config': AutomaticConfig,
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-checkbox': Checkbox,
        's-loading': Loading,
        'total-price': TotalPrice
    };

    static filters = {
        formatPayType(value: string): string {
            return value === PAYTYPE.POSTPAY ? '按量付费' : '配置费用';
        }
    };

    static computed = {
        okText(): string {
            const current = this.data.get('current');
            const checking = this.data.get('checking');
            return current === 1 ? checking ? '校验中' : '下一步' : '提交订单';
        },
        cancelText(): string {
            return this.data.get('current') === 2 ? '上一步' : '取消';
        },
        okDisabled(): boolean {
            const pricing = this.data.get('pricing');
            const confirming = this.data.get('confirming');
            return pricing || confirming;
        },
        cuDatasource(): object[] {
            const moduleSlots = this.data.get('moduleSlots') || [];
            const packageStockList = this.data.get('packageStockList') || [];
            const availableZone = this.data.get('deploy.availableZone');

            const res = moduleSlots.map(item => ({
                label: item.cuCount,
                value: item.cuCount,
                diskDatasource: item.decoupledDiskSlotInfo || [],
                disabled: _.findIndex(packageStockList, i => i.logicalZone === availableZone && i.packageVersion === item.slot_type && i.available) === -1
            }));
            return res;
        },
        timeUnitText(): string {
            const productType = this.data.get('computeItem.productType');
            const dayCount = this.data.get('dayCount');
            return productType === PAYTYPE.POSTPAY ? '分钟' : `${dayCount}天`;
        }
    };

    initData() {
        return {
            name: '变更配置',
            open: true,
            confirming: false,
            tabs: [
                {
                    label: '手动变更',
                    key: 'manual'
                },
                {
                    label: '分时自动变配',
                    key: 'automatic'
                },
            ],
            activeKey: 'manual',
            current: 1,
            price: 0,
            checked: false,
            pricing: true,
            moduleSlots: [],
            packageStockList: [],
            cdsInfo: {},
            loading: true,
            showError: false,
            enableFlex: false,
            dayCount: 0,
            checking: false
        };
    }

    async inited() {
        try {
            await this.getCuslots();
            await this.getCdsInfo();
        }
        finally {
            this.data.set('loading', false);
        }
    }

    attached() {
        this.watch('checked', value => {
            this.data.set('showError', !value);
        })
        this.data.set('sdk',
            new BillingSDK({
                OrderType: 'NEW',
                region: window.$context.getCurrentRegion().id,
                serviceType: 'palo'
            }, window.$context)
        );
    }

    async getCuslots() {
        const result = await this.$http.paloPost('listCuSlot');
        const {moduleSlots, packageStockList} = result;
        this.data.set('moduleSlots', moduleSlots);
        this.data.set('packageStockList', packageStockList);
    }

    async getCdsInfo() {
        const {computeGroupId, diskType} = this.data.get('computeItem');
        const result = await this.$http.paloPost('getCdsInfo', {
            region: window.$context.getCurrentRegion().id,
            deployId: this.data.get('deploy.deployId'),
            cgId: computeGroupId,
            diskType,
        });
        this.data.set('cdsInfo', result);
    }

    onPriceChange(target: {price: number, dayCount?: number, pricing: boolean}) {
        this.data.set('price', target.price);
        this.data.set('dayCount', target.dayCount);
        this.data.set('pricing', target.pricing);
    }

    async onConfirm() {
        this.data.set('showError', false);
        const enableFlex = this.data.get('enableFlex');
        if (this.data.get('activeKey') === 'automatic') {
            this.onAutomaticConfirm();
        }
        else {
            if (enableFlex) {
                Notification.error('请先关闭分时弹性');
            }
            else {
                this.onManualConfirm();
            }
        }
    }

    async onAutomaticConfirm() {
        try {
            const params = await this.ref('automaticConfig').validateAndGetParams();
            if (params.strategies.length < 2 && params.enabled) {
                Notification.error('请至少设置2条弹性规则');
                return;
            }
            const data = await this.$http.paloPost('scheduleCreate', params);
            Notification.success('分时弹性设置成功');
            this.onClose();
        }
        catch {
            this.data.set('confirming', false);
        }
    }

    handleChangeParams(params: any) {
        this.data.set('params', params);
    }

    handleChangeEnableFlex(target: boolean) {
        this.data.set('enableFlex', target);
    }

    async onManualConfirm() {
        try {
            const checked = this.data.get('checked');
            if (this.data.get('current') === 1) {
                this.data.set('checking', true);
                const checkPass = await this.ref('manualConfig')?.checkParams();
                if (!checkPass) {
                    // 参数检查未通过，不能进入下一步
                    this.data.set('checking', false);
                    return;
                }
                this.ref('manualConfig')?.initOrder();
                this.data.set('checking', false);
                this.data.set('current', 2);
            }
            else {
                if (!checked) {
                    Notification.error('请先勾选协议');
                    this.data.set('showError', true);
                    return;
                }
                this.data.set('confirming', true);
                const params = this.ref('manualConfig')?.getParams();
                const coupon = this.ref('manualConfig')?.getCoupons() || [];
                try {
                    const data = await this.$http.paloPost('upgradeCuConfirm', {
                        items: [
                            {
                                config: params,
                                paymentMethod: coupon
                            }
                        ]
                    });
                    this.data.set('confirming', false);
                    // 如果是预付费，加一个跳转成功的跳转，在新标签页打开billing页面
                    const sdk = this.data.get('sdk');
                    try {
                        const sdkResult = await sdk.checkPayInfo(data);
                        sdkResult.url && redirect(sdkResult.url);
                    } catch ({url = null}) {
                        url && redirect(url);
                    }
                    this.onClose();
                }
                catch {
                    this.data.set('confirming', false);
                }
            }
        }
        catch {
            this.data.set('confirming', false);
        }
    }

    handleChangeActive(target: {value: {key: string}}) {
        this.data.set('activeKey', target.value.key);
    }

    onCancel() {
        if (this.data.get('current') === 2) {
            this.data.set('current', 1);
        }
        else {
            this.onClose();
        }
    }
    // 关闭
    onClose() {
        this.data.set('open', false);
        this.dispose();
    }
}
