/**
 * 分时自动变配
 *
 * @file automatic-config.ts
 * <AUTHOR>
 */

import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Form, InputNumber, Select, Alert, Radio, Table, Button, TimePicker, Dialog, Tooltip} from '@baidu/sui';
import {OutlinedUp, OutlinedDown, OutlinedPlus} from '@baidu/sui-icon';
import './index.less';

const klass = 'automatic-config';

// 获取当前的整点时间作为时间选择器默认值
const now = new Date();
const defaultTime = new Date(
  now.getFullYear(),
  now.getMonth(),
  now.getDate(),
  now.getHours()
);

export default class extends Component {
    static template = html`
    <div class="${klass}">
        <s-alert type="info" showIcon="{{false}}">
            {{alertContent}}
        </s-alert>
        <s-radio-group
            datasource="{{datasource}}"
            value="{{enableFlex}}"
            enhanced="{{true}}"
            radioType="button"
            name="enableFlex"
            class="mt24 mb8"
            on-change="changeEnableFlex"
        ></s-radio-group>
        <div class="toggle-tip" s-if="{{enableFlex === 'close'}}">
            <div class="left-wrap {{expand ? 'expand' : 'ellipsis'}}">
                <p>1.在集群或计算组非正常运行状态时（如：停机、重启、升级等）会重试，超过30分钟则不会被执行。</p>
                <p>2.如果账号欠费或余额不足，规则无效且不会被执行。</p>
                <p>3.相邻的规则不能出现重复的目标计算资源规格。</p>
                <p>4.规则之间要至少间隔1小时，因此最多可配置23条规则。</p>
                <p>5.执行规则时，计算组缓存配置会自动随计算资源规格同比例伸缩变化。</p>
                <p>6.变配时部分请求的响应时间可能会出现抖动。</p>
            </div>
            <div class="right-wrap">
                <s-outlined-up s-if="{{expand}}" on-click="toggleStatus" />
                <s-outlined-down s-else on-click="toggleStatus" />
            </div>
        </div>
        <span s-else class="no-flex-tip">请配置2条及以上弹性规则以启用分时弹性变配</span>
        <s-table
            datasource="{{tableData}}"
            loading="{{loading}}"
            columns="{{columns}}"
            class="mt24"
            width="{{552}}"
        >
            <div slot="c-index">
                <span>{{rowIndex + 1}}</span>
            </div>
            <div slot="c-executeTime">
                <s-time-picker
                    steps="{{steps}}"
                    mode="HH:mm"
                    clearable="{{false}}"
                    disabled="{{row.state === 'save'}}"
                    getPopupContainer="{{getPopupContainer}}"
                    value="{=row.executeTime=}"
                    on-change="changeExecuteTime($event, rowIndex)"
                />
                <div s-if="{{row.errorTime}}" class="error-tip">
                    {{row.errorTime}}
                </div>
            </div>
            <div slot="c-targetCuNum">
                <s-select
                    datasource="{{cuNumList}}"
                    value="{=row.targetCuNum=}"
                    disabled="{{row.state === 'save'}}"
                    getPopupContainer="{{getPopupContainer}}"
                    on-change="changeTargetCuNum($event, rowIndex)"
                    width="160"
                    >
                    <s-option
                        s-for="item in cuDatasource"
                        value="{{item.value}}"
                        label="{{item.label}}"
                        disabled="{{item.disabled}}"
                    >
                        <div class="cuNum-option">
                            <span>{{item.label}}</span>
                            <span s-if="item.disabled" class="sellout">售罄</span>
                        </div>
                    </s-option>
                </s-select>
                <div s-if="{{row.errorCuNum}}" class="error-tip">
                    {{row.errorCuNum}}
                </div>
            </div>
            <div slot="c-action">
                <s-button
                    class="no-padding"
                    skin="stringfy"
                    s-if="{{row.state === 'save'}}"
                    on-click="edit(row, rowIndex)"
                >
                    编辑
                </s-button>
                <s-button
                    class="no-padding"
                    skin="stringfy"
                    s-if="{{row.state === 'save'}}"
                    on-click="delete(row, rowIndex)"
                >
                    删除
                </s-button>
                <s-button
                    class="no-padding"
                    skin="stringfy"
                    s-if="{{row.state === 'edit'}}"
                    on-click="save(row, rowIndex)"
                >
                    保存
                </s-button>
                <s-button
                    class="no-padding"
                    skin="stringfy"
                    s-if="{{row.state === 'edit'}}"
                    on-click="cancel(row, rowIndex)"
                >
                    取消
                </s-button>
            </div>
        </s-table>
        <s-tooltip>
            <div slot="content">
                <p>最多可配置23条规则；</p>
                <p>存在未保存状态规则时无法添加新规则</p>
            </div>
            <s-button
                skin="stringfy"
                class="mt16"
                on-click="addRule"
                disabled="{{addDisabled}}"
            >
                <s-icon-plus class="button-icon mr4" />
                添加规则
            </s-button>
        </s-tooltip>
        <div s-if="{{totalError}}" class="error-tip">{{totalError}}</div>
    </div>`;

    static components = {
        's-form': Form,
        's-form-item': Form.Item,
        's-input-number': InputNumber,
        's-select': Select,
        's-alert': Alert,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-outlined-up': OutlinedUp,
        's-outlined-down': OutlinedDown,
        's-table': Table,
        's-button': Button,
        's-time-picker': TimePicker,
        's-icon-plus': OutlinedPlus,
        's-option': Select.Option,
        's-tooltip': Tooltip
    };

    static computed = {
        alertContent(): string {
            return this.data.get('enableFlex') === 'open'
                ? '开启分时弹性后，会按照以下规则进行分时弹性变配，缩容时部分请求的响应时间可能会出现抖动'
                : '关闭分时弹性后，计算组将不再执行分时策略';
        },
        addDisabled(): boolean {
            const tableData = this.data.get('tableData');
            const hasNotSaved = tableData.some(item => item.state !== 'save');
            return tableData.length >= 23 || hasNotSaved;
        }
    }

    initData() {
        return {
            datasource: [
                {
                    label: '开启分时弹性',
                    value: 'open',
                },
                {
                    label: '关闭分时弹性',
                    value: 'close',
                },
            ],
            enableFlex: 'open',
            expand: false,
            steps: [1, 60, 60],
            tableData: [],
            columns: [
                {
                    label: '序号',
                    name: 'index',
                    width: 40,
                },
                {
                    label: '执行周期',
                    name: 'executeCycle',
                    width: 60,
                },
                {
                    label: '执行时间',
                    name: 'executeTime',
                    width: 100,
                },
                {
                    label: '目标计算资源',
                    name: 'targetCuNum',
                    width: 140,
                },
                {
                    label: '操作',
                    name: 'action',
                    width: 60,
                },
            ],
            getPopupContainer: () => document.body,
            loading: false,
        };
    }

    async attached() {
        try {
            const res = await this.$http.paloPost('scheduleList', {
                computeGroupId: this.data.get('computeItem').computeGroupId,
            });
            this.data.set('tableData', res.strategies.map(item => ({
                ...item,
                targetCuNum: item.desiredCuNum,
                fixedTime: item.startTime,
                executeTime: this.parseTimeToTodayDate(item.startTime),
                state: 'save',
                executeCycle: '每天',
            })));
            this.data.set('enableFlex', res.enabled ? 'open' : 'close');
            const params = this.getParams();
            this.fire('change-params', params);
            this.fire('change-enable-flex', res.enabled);
        } catch (error) {
            console.error(error);
        }
    }

    parseTimeToTodayDate(timeStr: string) {
        const [hours, minutes, seconds] = timeStr.split(':').map(Number);
        const now = new Date();
        const date = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
            hours,
            minutes,
            seconds
        );
        return date;
    }

    changeEnableFlex(target: {value: string}) {
        this.data.set('enableFlex', target.value);
        const params = this.getParams();
        this.fire('change-params', params);
        this.fire('change-enable-flex', target.value === 'open');
    }

    toggleStatus() {
        this.data.set('expand', !this.data.get('expand'));
    }

    addRule() {
        this.data.push('tableData', {
            executeCycle: '每天',
            fixedTime: defaultTime.toTimeString().split(' ')[0],
            executeTime: defaultTime,
            targetCuNum: null,
            state: 'edit',
        });
    }

    delete(row, index) {
        const tableData = this.data.get('tableData');
        const length = tableData.length;
        const content = length <= 2
            ? '剩余规则小于2，计算组将不再进行分时变配？'
            : '确认删除当前分时弹性规则';
        Dialog.confirm({
            title: '提示',
            content,
            onOk: () => {
                tableData.splice(index, 1);
                this.data.set('loading', true);
                this.data.set('tableData', [...tableData]);
                this.nextTick(() => {
                    // 因为都是排序过的 直接校验下面的相邻的就好
                    // 需要考虑是否删除的是最后一个
                    const nextIndex = index % tableData.length;
                    const sameCpuNumWithNeighbor = this.checkSameWithNeighbor(nextIndex);
                    // 校验相邻时间计算资源是否相同
                    if (sameCpuNumWithNeighbor) {
                        this.data.merge(`tableData[${nextIndex}]`, {
                            errorCuNum: '相邻的规则不能出现重复的目标计算资源规格',
                            state: 'edit',
                        });
                    }
                    this.data.set('loading', false);
                    if (length <= 2) {
                        this.changeEnableFlex({value: 'close'});
                    }
                });
            },
            onCancel: () => {},
        })

    }

    checkAlreadyExist(fixedTime: string, index: number) {
        const tableData = this.data.get('tableData');
        const sameItem = tableData.filter((row, idx) => row.fixedTime === fixedTime && idx !== index);
        return sameItem.length >= 1;
    }
    
    checkSameWithNeighbor(rowIndex: number) {
        const tableData = this.data.get('tableData');
        if (!Array.isArray(tableData) || rowIndex < 0 || rowIndex >= tableData.length) {
            return;
        }
        if (tableData.length < 2) {
            return false;
        }

        const item = tableData[rowIndex];
        const sorted = [...tableData].sort((a, b) => a.fixedTime.localeCompare(b.fixedTime));
      
        // 找到该项在排序后的索引
        const sortedIndex = sorted.findIndex(i => i === item);
      
        const prev = sorted[(sortedIndex - 1 + sorted.length) % sorted.length];
        const next = sorted[(sortedIndex + 1) % sorted.length];
      
        return (
          (prev && prev.targetCuNum === item.targetCuNum) ||
          (next && next.targetCuNum === item.targetCuNum)
        );
    }
    
    save(row, index) {
        this.data.set(`tableData[${index}].errorTime`, '');
        this.data.set(`tableData[${index}].errorCuNum`, '');
        // 校验时间/计算资源是否填写，没有的话报错提示
        if (!row.fixedTime) {
            this.data.set(`tableData[${index}].errorTime`, '请选择执行时间');
            return;
        }
        if (!row.targetCuNum) {
            this.data.set(`tableData[${index}].errorCuNum`, '请选择目标计算资源');
            return;
        }
        // 校验时间是否重复
        const sameTimeExist = this.checkAlreadyExist(row.fixedTime, index);
        if (sameTimeExist) {
            this.data.set(`tableData[${index}].errorTime`, '已经有该时间的任务');
            return;
        }
        const sameCpuNumWithNeighbor = this.checkSameWithNeighbor(index);
        // // 校验相邻时间计算资源是否相同
        if (sameCpuNumWithNeighbor) {
            this.data.set(`tableData[${index}].errorCuNum`, '相邻的规则不能出现重复的目标计算资源规格');
            return;
        }
        this.data.set(`tableData[${index}].state`, 'save');
        this.data.set(`tableData[${index}].backup`, null);
        this.data.set('loading', true);
        const newArr = this.sortTableData();
        this.nextTick(() => {
            this.data.set('tableData', [...newArr]);
            this.data.set('loading', false);
        });
        const params = this.getParams();
        this.fire('change-params', params);
        // 如果有总的未保存提示 则检测当前保存状态 如果全保存了 那就去掉
        if (this.data.get('totalError')) {
            newArr.every(item => item.state === 'save') && this.data.set('totalError', '');
        }
    }

    edit(row, index) {
        this.data.set(`tableData[${index}].state`, 'edit');
        this.data.set(`tableData[${index}].backup`, {...row});
    }

    cancel(row, index) {
        // 恢复数据，也包括恢复编辑保存态
        const backup = this.data.get(`tableData[${index}].backup`);
        if (backup) {
            this.data.set(`tableData[${index}]`, {
                ...backup,
                backup: null
            });
        }
        else {
            // back为null，删除该条内容
            const tableData = this.data.get('tableData');
            tableData.splice(index, 1);
            this.data.set('loading', true);
            this.nextTick(() => {
                this.data.set('tableData', [...tableData]);
                this.data.set('loading', false);
            });
        }

    }

    sortTableData() {
        const sortedTable = this.data.get('tableData').sort((a, b) => {
            const t1 = new Date('1970-01-01T' + a.fixedTime);
            const t2 = new Date('1970-01-01T' + b.fixedTime);
            return t1 - t2;
        });
        return sortedTable;
    }

    changeTargetCuNum(target: {value: string}, index) {
        this.data.set(`tableData[${index}].targetCuNum`, target.value);
    }

    changeExecuteTime(target: {value: Date}, index) {
        const fixedTime = target.value.toTimeString().split(' ')[0];
        this.data.merge(`tableData[${index}]`, {fixedTime, executeTime: target.value});
    }

    getParams() {
        const tableData = this.data.get('tableData');
        return {
            enabled: this.data.get('enableFlex') === 'open',
            computeGroupId: this.data.get('computeItem').computeGroupId,
            strategies: tableData.map(item => ({
                strategyId: item.strategyId || '',
                startTime: item.fixedTime,
                desiredCuNum: item.targetCuNum,
            })),
        };
    }

    // 校验是否所有规则都保存了 如果全保存 则返回参数
    async validateAndGetParams() {
        const tableData = this.data.get('tableData');
        const hasNotSaved = tableData.some(item => item.state !== 'save');
        if (hasNotSaved) {
            this.data.set('totalError', '存在未保存状态');
            return Promise.reject('存在未保存状态');
        }
        return Promise.resolve(this.getParams());
    }
}
