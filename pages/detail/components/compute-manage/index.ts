/**
 *
 * @file 计算组列表页
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {AppListPage, SearchBox} from '@baidu/sui-biz';
import {
    Button,
    Table,
    Pagination,
    Tooltip,
    Loading,
    Dialog,
    Notification,
    Radio,
    Alert,
    Form
} from '@baidu/sui';
import {OutlinedWarning, OutlinedRefresh} from '@baidu/sui-icon';
import {PAYTYPE, clusterStatus, PayType} from '@common/config';
import {renderStatus} from '@common/utils/index';
import {CreateBtn} from '../../../list/create-btn';
import {AllEnum, computeGroupStatusEnum} from '@common/config/index';
import {diskDatasource4Decoupled, getDiskNameFormType} from '../../../create/components/create-utils';
import {pickEmpty, formatTime, equalsIgnoreCase, isExpired} from '@common/utils';
import {clusterStatusMap} from '@common/config/constant';
import InstantEditor from '@components/instant-editor';
import UpgradeDrawer from './upgrade-drawer';
import CreateComputeDrawer from './create-compute-drawer';
import './index.less';
const klass = 'palo-compute-list';
const allEnum = AllEnum.toArray();

const template = html`
    <div>
        <s-page pageTitle="{{pageTitle}}" class="${klass}">
            <div slot="pageTitle" class="ml8 page-title">
                计算组
            </div>
            <div slot="bulk">
                <create-btn
                    skin="primary"
                    on-click="createCompute"
                    disabled="{{detail.status === 'Starting'}}"
                >创建计算组</create-btn>
            </div>
            <div slot="filter">
                <s-searchbox
                    class="cluster-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    on-keywordTypeChange="onKeywordTypeChange"
                    text-datasource="{{searchbox.textDataSource}}"
                    value="{= searchbox.keyword =}"
                    keyword-type="{= searchbox.keywordType =}"
                    datasource="{{searchbox.keywordTypes}}"
                    on-search="onSearch"
                    width="170"
                />
                <s-button on-click="refresh" class="refresh-button">
                    <outlined-refresh />
                </s-button>
            </div>
            <s-table
                columns="{{table.schema}}"
                datasource="{{table.datasource}}"
                loading="{{table.loading || isLoading}}"
                on-sort="onSort"
                on-filter="onFilter"
            >
                <div slot="c-computeGroupName">
                    <div>
                        {{row.computeGroupName}}
                        <instant-editor
                            value="{{row.computeGroupName}}"
                            info="{{rowIndex}}"
                            request="{{editName}}"
                            check="{{check}}"
                            placeholder="请输入计算组名称"
                            desc="支持字母(a-z及A-Z)、数字(0-9),长度小于20个字符"
                        />
                    </div>
                    <div class="ellipsis">
                        {{row.computeGroupId}}
                    </div>
                </div>
                <div slot="c-actualStatus">
                    {{row.displayActualStatus | filterStatus(row.actualStatus) | raw}}
                </div>
                <div slot="c-productType">
                    <span>{{PayType[row.productType]}}</span>
                </div>
                <div slot="c-diskType">
                    {{row.diskType | filterDiskType}}
                </div>
                <div slot="c-createTime">
                    {{row.createTime | formatTime}}
                </div>
                <div slot="c-action">
                    <s-button
                        skin="stringfy"
                        class="no-padding"
                        on-click="upgrade(row)"
                        disabled="{{row.upgradeDisabled}}"
                    >
                        配置变更
                    </s-button>
                    <s-button
                        skin="stringfy"
                        on-click="restart(row)"
                        disabled="{{!row.canRestart}}"
                        class="no-padding ml8"
                    >
                        重启
                    </s-button>
                    <s-button
                        skin="stringfy"
                        on-click="delete(row)"
                        disabled="{{!row.canDelete || detail.status !== 'Running' || pager.totalCount === 1}}"
                        class="no-padding ml8"
                    >
                        删除
                    </s-button>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                layout="{{'total, pageSize, pager'}}"
                total="{{pager.totalCount}}"
                page="{{pager.pageNo}}"
                pageSize="{=pager.pageSize=}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
        </s-page>
        <s-dialog
            open="{{restartOpen}}"
            title="重启计算组"
            on-confirm="confirmRestart"
            on-close="cancelRestart"
            confirming="{{restartConfirming}}"
        >
            <s-alert skin="info" showIcon="{{false}}">
                确定要重启计算组吗？计算组重启中可能会有短时间服务中断
            </s-alert>
            <s-form-item label="重启方式：" class="mt24">
                <s-radio-group
                    datasource="{{restartTypes}}"
                    value="{=restartType=}"
                    enhanced="{{true}}"
                    radioType="button"
                    name="restartType"
                ></s-radio-group>
                <p slot="help">{{restartTip}}</p>
            </s-form-item>
        </s-dialog>
    </div>`;
 
 export default class ComputeList extends Component {
    static pageName = 'palolistpage';

     static template = template;

    static components = {
        's-page': AppListPage,
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        's-tooltip': Tooltip,
        's-warning': OutlinedWarning,
        's-loading': Loading,
        'create-btn': CreateBtn,
        'outlined-refresh': OutlinedRefresh,
        's-searchbox': SearchBox,
        'instant-editor': InstantEditor,
        's-dialog': Dialog,
        's-alert': Alert,
        's-radio-group': Radio.RadioGroup,
        's-form-item': Form.Item
    }
     
    static filters = {
        filterStatus(displayActualStatus: string, actualStatus: string) {
            const item = computeGroupStatusEnum.fromValue(actualStatus);
            return `<span class="status ${item.classname}">${displayActualStatus}</span>`;
        },
        formatTime,
        filterDiskType: getDiskNameFormType
    }

     
     static computed = {
         restartTip(): string {
             const restartType = this.data.get('restartType');
             return restartType === 'SCROLL'
                 ? '节点滚动重启，不影响线上服务，但速度较慢'
                 : '全量重启，服务会中断一段时间，但速度较快';
        }
    }
 
    initData() {
        return {
             pageTitle: '计算组',
             clusterStatus,
             PayType,
             PAYTYPE,
             actionName: '',
             currentCluster: null,
             table: {
                 schema: [
                     {
                         name: 'computeGroupName',
                         label: '计算组名称/ID',
                         width: '15%',
                         minWidth: 150,
                     },
                     {
                        name: 'actualStatus',
                        label: '状态',
                        width: 120,
                        filter: {
                            options: [
                                ...allEnum,
                                ...computeGroupStatusEnum.toArray()
                            ],
                            value: allEnum[0].value
                        },
                     },
                     {
                        name: 'cuNums',
                        label: '计算资源(CU)',
                        width: 120,
                        sortable: true,
                     },
                     {
                         name: 'diskType',
                         label: '缓存类型',
                         width: '10%',
                         filter: {
                            options: [
                                ...allEnum,
                                ...diskDatasource4Decoupled
                            ],
                            value: allEnum[0].value
                        },
                     },
                     {
                        name: 'cacheCapacity',
                        label: '缓存容量(G)',
                        width: '10%',
                        sortable: true,
                    },
                     {
                         name: 'productType',
                         label: '付费方式',
                         width: 120,
                         filter: {
                            options: [
                                ...allEnum,
                                ...PAYTYPE.toArray()
                            ],
                            value: allEnum[0].value
                        },
                     },
                     {
                         name: 'createTime',
                         label: '创建时间',
                         width: '10%',
                         sortable: true,
                     },
                     {
                         name: 'action',
                         label: '操作',
                         width: '16%'
                     }
                 ],
                 datasource: [],
                 loading: false
             },
             pager: {
                 pageSize: 10,
                 pageNo: 1,
                 totalCount: 0,
            },
            editName: this.editName.bind(this),
            check: this.check.bind(this),
            searchbox: {
                keyword: '',
                keywordType: ['computeGroupName'],
                keywordTypes: [
                    {
                        value: 'computeGroupName',
                        text: '计算组名称'
                    },
                    {
                        value: 'computeGroupId',
                        text: '计算组ID'
                    }
                ]
            },
            restartTypes: [
                {label: '滚动重启', value: 'SCROLL'},
                {label: '全量冷重启', value: 'FULL'}
            ],
            restartType: 'SCROLL',
            restartOpen: false
        };
    }

    attached() {
        this.refresh();
    }

    onPagerChange(e: { value: { pageSize: number; page: number; } | undefined; }) {
        this.refresh(e.value);
    };

    onPagerSizeChange(e: { value: { pageSize: number; page: number; } | undefined; }) {
        this.refresh(e.value);
    };

    onRegionChange() {
       this.refresh({
           pageSize: this.data.get('pager').pageSize,
           page: 1
       })
    }
     
    async onSort(event: {value: {order: string; orderBy: string}}) {
        const {orderBy, order} = event.value;
        this.data.set('order', order);
        this.data.set('orderBy', orderBy);
        this.refresh();
    }
     
     checkDelete(item: any) {
        const isRightStatus = [computeGroupStatusEnum.Running, computeGroupStatusEnum.Audit_stopped].includes(item.actualStatus);
        const productType = item.productType;
         return productType === PAYTYPE.POSTPAY
             ? isRightStatus
             : isExpired(item.expireTime);
    }

    async refresh(e?: {pageSize: number; page: number;}) {
        const {pager, orderBy,  order, searchbox, actualStatus, productType, diskType, deployId} = this.data.get('');
        const params = pickEmpty({
            pageSize: e?.pageSize || pager.pageSize,
            pageNo: e?.page || pager.pageNo,
            orderBy,
            order,
            deployId,
            status: actualStatus,
            payType: productType,
            diskType,
            computeGroupName: searchbox.keywordType[0] === 'computeGroupName' ? searchbox.keyword: '',
            computeGroupId: searchbox.keywordType[0] === 'computeGroupId' ? searchbox.keyword: '',
        });
        try {
            this.data.set('table.loading', true);
            const {computeGroupItems, total} = await this.$http.paloPost('paloComputeGroupList', params);
            this.data.set('table.datasource', computeGroupItems?.map(item => ({
                ...item,
                upgradeDisabled: !equalsIgnoreCase(item.actualStatus, 'running'),
                canRestart: item.actualStatus === clusterStatusMap.Running,
                canDelete: this.checkDelete(item)
            })) || []);
            this.data.set('pager.totalCount', total);
         }
         catch (e) {
             this.data.set('table.datasource', []);
             this.data.set('pager.totalCount', 0);
         } finally {
             this.data.set('table.loading', false);
         }
    }
     
    async onFilter(event: {field: any; filter: any}) {
        const {field, filter} = event;
        this.data.set(`${field.name}`, filter.value);
        await this.refresh();
    }
     
    /**
    */
    onSearch(args?: {value: string}): void {
        this.data.set('pager.page', 1);
        this.refresh();
    }
     
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length >= 20) {
            return callback('不能超过20个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }
     
    // 名称编辑
     async editName(name: string, rowIndex: number) {
         const deployId = this.data.get('deployId');
        const item = this.data.get(`table.datasource[${rowIndex}]`);
         await this.$http.paloPost('paloEditGroupName', {
            deployId,
            computeGroupId: item.computeGroupId,
            editNewName: name
        })
        Notification.success('修改成功');
        this.data.set(`table.datasource[${rowIndex}].computeGroupName`, name);
     }

     restart(item: any) {
         this.data.set('restartOpen', true);
         this.data.set('restartItem', item);
     }

     cancelRestart() {
         this.data.set('restartOpen', false);
     }

    async confirmRestart() {
         const {restartType, restartItem, deployId} = this.data.get('');
         try {
             this.data.set('restartConfirming', true);
             await this.$http.paloPost('computeGroupRestart', {
                 deployId,
                 cgId: restartItem.computeGroupId,
                 type: restartType,
                 region: window.$context.getCurrentRegion().id
             });
             Notification.success('重启成功');
             this.refresh();
             this.data.set('restartOpen', false);
         } catch (e) {
            console.error(e);
         } finally {
             this.data.set('restartConfirming', false);
         }
     }
     
     async delete(item: any) {
        const tipContent = item?.strategyNums < 2
            ? `剩余规则小于2，计算组不再进行分时变配`
            : `计算组${item?.computeGroupName}删除后无法恢复，确认删除当前计算组？`;
        try {
            await Dialog.warning({
                content: tipContent,
                title: '删除计算组',
                onOk: async () => {
                    try {
                        await this.$http.paloPost('paloGroupDelete', {
                            region: window.$context.getCurrentRegion().id,
                            deployId: this.data.get('deployId'),
                            cgIds: [item.computeGroupId],
                        });
                        Notification.success('删除成功');
                        this.refresh();
                    } catch (e) {
                        console.error(e);
                    }
                }
            });
        } catch (e) {}
    }
     
     createCompute() {
        // event.stopPropagation();
        const { deployId, detail } = this.data.get('');
        const dialog = new CreateComputeDrawer({
            data: {
                deployId,
                deploy: detail
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            // 刷新列表数据
            Notification.success('计算组创建成功');
            this.refresh();
        });
    }
     
    upgrade(item: any) {
        event.stopPropagation();
        const {deployId, detail} = this.data.get('');
        const dialog = new UpgradeDrawer({
            data: {
                computeItem: item,
                deployId,
                deploy: detail,
            }
        });
        dialog.attach(document.body);
        dialog.on('success', () => {
            // 刷新列表数据
            Notification.success('变配提交成功');
            this.refresh();
        });
    }
}
 