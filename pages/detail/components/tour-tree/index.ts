/*
 * @Description: 资源监控 - 引导tree组件
 * @Author: zhao<PERSON><PERSON><EMAIL>
 * @Date: 2025-02-28 16:52:49
*/
import _ from 'lodash';
import {html, decorators} from '@baiducloud/runtime';
import {Component} from 'san';
import {Tree2} from '@baidu/sui';

const klass = 'palo-monitor-tree';
export default class MonitorTree extends Component {
    static template = html`
        <div class="${klass} mt16" s-if="datasource.length">
            <div class="selected-line" style="top: {{lineOffsetTop}}px;"></div>
            <s-tree 
                expandedKeys="{{expandedKeys}}"
                selectedKeys="{{selectedKeys}}"
                on-select="handleSelect"
                treeData="{{datasource}}"
                on-expand="handleExpand"
            />
        </div>
    `;
    static components = {
        's-tree': Tree2,
    };
    static computed = {
        datasource() {
            const indicatorObj = this.data.get('indicatorObj');
            return _.map(indicatorObj, (value, key) => ({
                label: value.label,
                key,
                children: value.indicators,
            }));
        },
        defaultSelectedKeys(): string[] {
            return [this.data.get('indicatorList')[0]?.key];
        },
        selectedKeys() {
            const activeKey: string = this.data.get('activeKey');
            const defaultSelectedKeys: string[] = this.data.get('defaultSelectedKeys');
            return activeKey ? [activeKey] : defaultSelectedKeys;
        },
        expandedKeys(): string[] {
            const datasource = this.data.get('datasource');
            return _.map(datasource, item => item.key);
        }
    };
    initData() {
        return {
            lineOffsetTop: 32,
            hiddenLine: false
        };
    }
    // 点击树节点 - 右侧图表定位到对应位置
    handleSelect({keys, info}) {
        const {data, el} = info.node;
        const {treeLevel} = data.raw;
        this.handleScrollLine(el?.offsetTop);
        // 仅点击叶节点时触发事件
        if (!treeLevel) {
            return;
        }
        this.fire('select', info.key)
    }
    handleExpand() {
        this.nextTick(() => {
            this.nextTick(() => {
                const selectNode = document.querySelector('.s-tree2-treenode-selected');
                this.handleScrollLine(selectNode?.offsetTop);
            });
        });
    }
    handleScrollLine(top) {
        this.data.set('lineOffsetTop', top > 0 ? top : 7);
    }
}
