
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Dialog, Select, Input, Table, Notification} from '@baidu/sui';
import {SearchBox} from '@baidu/sui-biz';
import './index.less';

export default class EipBind extends Component {
    static template = html`
    <template>
        <s-dialog
            open="{{openEip}}"
            title="弹性公网IP绑定"
            on-confirm="bindEipSubmit"
        >
                <s-searchbox
                    datasource="{{searchbox.datasource}}"
                    keyword-type="{=searchbox.keywordType=}"
                    value="{=searchbox.value=}"
                    class="searchbox"
                    on-search="getIps"
                />
                <s-table
                    maxHeight="{{200}}"
                    columns="{{columns}}"
                    selection="{= selection =}"
                    datasource="{{datasource}}"
                ></s-table>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-select': Select,
        's-input': Input,
        's-table': Table,
        's-searchbox': SearchBox
    };
    
    initData() {
        return {
            openEip: false,
            searchbox: {
                keywordType: ['INSTANCE_EIP'],
                datasource: [
                    {text: '公网IP', value: 'INSTANCE_EIP'},
                    {text: '实例名称', value: 'INSTANCE_NAME'}
                ],
                value: ''
            },
            datasource: [],
            selection: {
                mode: 'single',
                selectedIndex: []
            },
            columns: [
                {name: 'name', label: '实例名称'},
                {name: 'eip', label: '公网IP'},
                {
                    name: 'bandWidth',
                    label: '最大带宽',
                    render(item, key, col, rowIndex, colIndex, data) {
                        return `${item.bandWidth}Mbps`;
                    }
                }
            ],
        };
    }

    attached() {
        this.getIps();
    }

    getIps() {
        let params = {
            keyword: this.data.get('searchbox.value'),
            keywordType: this.data.get('searchbox.keywordType[0]'),
            order: 'desc',
            orderBy: 'createTime'
        };
        this.$http.paloPost('paloEipInstanceList', params).then(
            (res: {result: any}) => {
                const {result} = res;
                this.data.set('datasource', result);
            },
            err => {
                this.data.set('datasource', []);
            }
        );
    }

    bindEipSubmit() {
        let data = this.data.get('selection');
        if (!data.selectedIndex.length) {
            Notification.warning('请选择实例后重试！');
            return;
        }
        let idx = data.selectedIndex[0];
        let dataS = this.data.get('datasource');
        let params = {
            eip: dataS[idx].eip,
            eipShortId: dataS[idx].eipId,
            instanceId: this.data.get('detail').deployId,
            instanceType: this.data.get('bindType')
        };
        this.$http.paloPost('paloEipBind', params).then(() => {
            Notification.success('操作成功');
            this.data.set('openEip', false);
            this.fire('success', {});
        });
    }
}
