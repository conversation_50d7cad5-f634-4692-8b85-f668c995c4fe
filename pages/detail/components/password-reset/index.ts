/**
 * 密码重置
 * @file index.ts 
 * <AUTHOR>
 */

import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {Component} from 'san';
import {
    Dialog,
    Form,
    Input,
    Button,
    Notification,
} from '@baidu/sui';

import './index.less';
import {
    required,
    passwordValidator
} from '@common/utils/rules';
import {PasswordInput} from '@components/password-input';
export default class PasswordConfirmDialog extends Component {
    static template = html`
    <template>
        <s-dialog 
            open="{= open =}"
            on-close="close"
            class="password-confirm-dialog"
            title="密码重置"
        >
            <s-form
                s-ref="form"
                data="{=formData=}"
                rules="{{rules}}"
                inlineMessage="true"
                label-align="left"
            >
                <s-form-item
                    prop="newPassword"
                    label="数据库新密码："
                >
                    <password-input
                        width="320"
                        value="{= formData.newPassword =}"
                    />
                    <p slot="help">支持小写字母(a-z)、大写字母(A-Z)、数字(0-9)、!@#$%^&*()_+-=[]{};:,<.>/?\| 并且至少包含其中三种, 长度8~16个字符</p>
                </s-form-item>
                <s-form-item
                    prop="newPasswordConfirm"
                    label="确认密码："
                    class="mt16"
                >
                    <password-input
                        width="320"
                        value="{= formData.newPasswordConfirm =}"
                    />
                </s-form-item>
            </s-form>
            <footer slot="footer">
                <s-button on-click="close">取消</s-button>
                <s-button on-click="submit" skin="primary" loading="{{loading.confirm}}">确定</s-button>
            </footer>
        </s-dialog> 
    </template>  
    `;
    static components = {
        's-dialog': Dialog,
        's-form': Form,
        's-form-item': Form.Item,
        's-input': Input,
        's-button': Button,
        'password-input': PasswordInput
    };

    initData() {
        return {
            open: true,
            inputWidth: 340,
            formData: {
                newPassword: '',
                newPasswordConfirm: '',
            },
            rules: {
                newPassword: [
                    required,
                    {
                        validator: passwordValidator
                    }
                ],
                newPasswordConfirm: [
                    required,
                    {
                        validator: (rule: any, value: any, callback: (arg0: string | undefined) => void) => {
                            const newPassword = this.data.get('formData.newPassword');
                            if (value !== newPassword) {
                                return callback('两次输入的密码不一致');
                            }
                            callback();
                        }
                    }
                ]
            },
            loading: {
                confirm: false,
            }
        };
    }

    async attached() {
    }

    close() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    async submit() {
        await this.ref('form').validateFields();
        const deployId = this.data.get('deployId');
        try {
            const formData = this.data.get('formData');
            this.data.set('loading.confirm', true);
            await this.$http.paloPost('paloSubmitPassword', {
                ...formData,
                deployId,
            });
            Notification.success('密码重置成功！');
            this.close();
        }
        catch (err) {
            console.error(err);
        }
        finally {
            this.data.set('loading.confirm', false);
        }
    }
}
