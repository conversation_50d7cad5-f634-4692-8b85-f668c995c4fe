
.warn-status {
    width: 100%;
    .s-loading {
        width: 100%;
    }
    .s-collapse {
        border: 0;
        border-radius: 4px;
    }
    .s-collapsepanel-header  {
        background-color: #ffe8e6;
        font-size: 12px;
        color: #151B26;
        line-height: 20px;
        font-weight: 400;
        padding: 6px 16px;
        .header-slot {
            svg {
                margin-top: -2px;
            }
        }
    }
    .s-collapsepanel-content {
        background-color: #ffe8e6;
        .s-table .s-table-thead  {
            background-color: #ffe8e6;
        }
        .s-table-tbody {
            background-color: #ffe8e6;
        }
        .s-table .s-table-cell {
            
            .s-table-cell-text  {
                padding: 7px 0;
            }
        }

        .s-table .s-table-container .s-table-header table>thead>tr th {
            padding: 5px 0;
        }
        .s-table-cell-instanceId {
            padding-left: 12px;
        }
        .s-table-cell-alarmStateCount {
            .s-table-cell-text {
                margin-left: -6px;
            }
        }
        .s-table-cell-okStateCount {
            .s-table-cell-text {
                margin-left: -14px;
            }
        }
        .s-table-cell-opereation {
            .s-table-cell-text {
                margin-left: -24px;
            }
        }
    }
}

.palo-monitor-filter-panel {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;

    .s-select-options-wrapper .s-select-check-all .s-checkbox .s-checkbox-input {
        vertical-align: -5px;
    }
}

.palo-monitor-time-panel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 8px;
}

.monitor-filter {
    .s-dialog-wrapper {
        width: 1000px!important;
        .filter-container {
            display: flex;
            justify-content: space-between;
        }
    }
    .checkbox-area {
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        .s-checkbox {
            display: flex;
            align-items: center;
            width: 310px;
            margin-top: 16px;
            .s-checkbox-input {
                min-width: 16px;
            }
        }
        .alias {
            font-size: 12px;
            color: #84868C;
            line-height: 20px;
            font-weight: 400;
            margin-left: 24px;
        }
    }

}



.cluster-monitor {
    padding: 24px;
    .s-tabs .s-tabnav .s-tabnav-scroll .s-tabnav-nav .s-tabnav-nav-selected:after {
        width: auto;
    }
    .chart-container {
        height: calc(~"100vh - 325px");
        width: 100%;
        display: flex;
        .palo-monitor-tree {
            position: relative;
            height: 100%;
            min-width: 200px;
            border-left: 2px solid #E8E9EB;
            padding-left: 6px;
            padding-right: 16px;
            overflow-y: auto;
            overflow-x: hidden;
            .selected-line {
                position: absolute;
                left: 0px;
                width: 2px;
                height: 28px;
                background-color: #2468F2;
                transition: all .2s;
            }
            .hidden-line {
                height: 0;
            }
            .s-tree2 {
               .custom-tree-node {
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    transition: all .2s;
               }
            }
        }
        .chart-panel {
            width: 100%;
            min-width: 860px;
            max-height: 100%;
            min-height: 100px;
            overflow-y: auto;
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            column-gap: 16px;
        }
    }
}