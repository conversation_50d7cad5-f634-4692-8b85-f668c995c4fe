import _ from 'lodash';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {AppDetailPage, AppTabPage} from '@baidu/sui-biz';
import {Button, Table, Pagination, Loading, Notification} from '@baidu/sui';
import {OutlinedLeft, OutlinedRefresh} from '@baidu/sui-icon';
import {updateUrlQuery} from '@common/utils/index';
import Info from './components/storage-message';
import ComputeList from './components/compute-manage';
import Monitor from './components/monitor';
import PermissionManage from './components/permission-manage';
import './index.less';
import { clusterStatusFilter } from '@common/config';

const klass = 'palo-detail';

// 这里需要按照顺序来
const Pages = ['/palo/decoupled-detail', '/palo/monitor'];

const template = html`
    <app-detail-page class="${klass}" pageTitle="{{detail.deployName}}" backTo="{{backTo}}">
        <div slot="pageTitle" class="${klass}-instanceinfo">
                <div class="${klass}-left">
                    <span class="back-btn" on-click="onBack">
                        <outlined-left width="{{16}}" />
                        <span class="back-text">返回</span>
                    </span>
                    <s-loading loading="{{loading}}" s-if="loading" size="small" class="ml8"></s-loading>
                    <template s-else>
                        <h4 class="${klass}-instancename">{{detail.deployName}}</h4>
                        <span class="${klass}__status ml8">{{detail | renderStatus | raw}}</span>
                    </template>
                </div>
            <div class="palo-detail-right">
                <s-button on-click="refresh" name="refresh" style="margin: 0 5px;" class="refresh-button">
                    <outlined-refresh is-button="{{false}}"></outlined-refresh>
                </s-button>
            </div>
        </div>
        <app-tab-page
            active="{=page=}"
            on-change="onTabChange"
            skin="accordion"
            position="left"
            class="palo-tab-page"
        >
            <app-tab-page-panel
                label="集群信息"
                style="{{page | show(1)}}"
                url="#/palo/decoupled-detail?deployId={{deployId}}"
            >
                <message
                    s-if="page === 1"
                    s-ref="info"
                    deployId="{{deployId}}"
                    detail="{{detail}}"
                    isLoading="{{loading}}"
                    on-refresh="refresh"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="计算组"
                style="{{page | show(2)}}"
                url="#/palo/decoupled-detail?deployId={{deployId}}"
            >
                <compute-list
                    s-if="page === 2"
                    s-ref="computeList"
                    deployId="{{deployId}}" 
                    detail="{{detail}}"
                    isLoading="{{loading}}"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="集群监控"
                style="{{page | show(3)}}"
                url="#/palo/detail?deployId={{deployId}}"
            >
                <monitor
                    s-if="page === 3"
                    s-ref="monitor"
                    deployId="{{deployId}}" 
                    detail="{{detail}}"
                    isLoading="{{loading}}"
                    type="decoupled"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="权限管理"
                style="{{page | show(4)}}"
                url="#/palo/decoupled-detail?deployId={{deployId}}"
            >
                <permission-manage
                    s-if="page === 4"
                    s-ref="permissionManage"
                    deployId="{{deployId}}"
                    query="{{query}}"
                />
            </app-tab-page-panel>
        </app-tab-page>
    </app-detail-page>
`;
@decorators.asPage(...Pages)
export default class PaloDetail extends AppDetailPage {
    static pageName = 'palo-detail';

    static template = template;

    static components = {
        'app-detail-page': AppDetailPage,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPane,
        's-loading': Loading,
        'outlined-refresh': OutlinedRefresh,
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        'outlined-left': OutlinedLeft,
        'message': Info,
        'compute-list': ComputeList,
        'monitor': Monitor,
        'permission-manage': PermissionManage,
    };

    initData() {
        return {
            detail: {},
            backTo: '/palo/#/palo/decoupledlist',
            deployId: '',
            page: 1,
            loading: true
        };
    }

    static filters = {
        show(page: string, key: string) {
            return page === key ? '' : 'display:none';
        },
        renderStatus(detail: any) {
            const item = clusterStatusFilter.fromValue(detail.desireStatus);
            return `<span class="status ${item?.classname}">${detail.displayActualStatus}</span>`;
        }
    };


    inited() {
        const query = this.data.get('route.query');
        this.data.set('query', query);
        // 监听路由参数变化，更新 query
        this.watch('route.query', val => {
            this.data.set('query', val);
        });
    }

    async attached() {
        const {page = 1, deployId = ''} = this.data.get('route.query');
        this.data.set('page', Number(page));
        this.data.set('deployId', deployId);
        await this.getClusterDetail();
    }

    onTabChange(target: {value: {key: number}}) {
        updateUrlQuery({page: target.value.key});
        this.data.set('page', Number(target.value.key));
        this.refresh();
    }


    async getClusterDetail() {
        const {deployId} = this.data.get('');
        if (!deployId) {
            Notification.error('缺少集群ID，请返回列表页重试');
            return;
        }
        this.data.set('loading', true);
        const data = await this.$http.paloPost('paloSeperateDetail', {deployId});
        this.data.set('detail', data);
        this.data.set('loading', false);
    }

    onBack() {
        redirect(this.data.get('backTo'));
    }

    async refresh() {
        const {page} = this.data.get('');
        if (page === 2) {
            this.ref('computeList')?.refresh();
        }
        else if (page === 1) {
            await this.getClusterDetail();
        }
        else if (page === 3) {
            this.ref('monitor')?.refresh();
        }
    }

    // region 切换
    onRegionChange() {
        redirect('#/palo/decoupledlist');
    }
}
