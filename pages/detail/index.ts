/**
 * palo detail
 *
 * @file detail.js详情页
 * <AUTHOR>
 */

import _ from 'lodash';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {AppDetailPage, AppTabPage} from '@baidu/sui-biz';
import {Button, Table, Pagination, Dialog, Loading, Notification} from '@baidu/sui';
import {OutlinedLeft, OutlinedRefresh} from '@baidu/sui-icon';
import {renderStatus, updateUrlQuery} from '@common/utils/index';
import {paloStatusConfig, BOXER1_MAX_ID} from '@common/config/constant';
import {clusterStatus} from '@common/config';
import Info from './components/message';
import Monitor from './components/monitor';
import TaskList from './components/task-list';
import BackUpPage from './components/tolerant-backup';
import QueryPage from './components/query';
import PermissionManage from './components/permission-manage';
import './index.less';

const klass = 'palo-detail';

// 这里需要按照顺序来
const Pages = ['/palo/detail', '/palo/monitor'];

const clusterIdToRestart = [
    '647777157585702912',
    '656342610566320128'
];

const template = html`
    <app-detail-page class="${klass}" pageTitle="{{detail.deployName}}" backTo="{{backTo}}">
        <div slot="pageTitle" class="${klass}-instanceinfo">
                <div class="${klass}-left">
                    <span class="back-btn" on-click="onBack">
                        <outlined-left width="{{16}}" />
                        <span class="back-text">返回</span>
                    </span>
                    <s-loading loading="{{loading}}" s-if="loading" size="small" class="ml8"></s-loading>
                    <template s-else>
                        <h4 class="${klass}-instancename">{{detail.deployName}}</h4>
                        <span class="${klass}__status ml8">{{detail | renderStatus | raw}}</span>
                    </template>
                </div>
            <div class="palo-detail-right">
                <div class="palo-detail-button">
                    <s-button disabled="{{resizeDisabled}}" on-click="goStretch" class="mr8">配置变更</s-button>
                    <s-button
                        s-if="detail.status === 'Shrinking'"
                        on-click="handleActions('cancelStretch')"
                        class="mr8"
                    >取消伸缩</s-button>
                    <s-button disabled="{{startDisabled}}" on-click="handleActions('start')" class="mr8">启动</s-button>
                    <s-button disabled="{{stopDisabled}}" on-click="handleActions('stop')" class="mr8">停止</s-button>
                    <s-button disabled="{{deleteDisabled}}" on-click="handleActions('delete')" class="mr8">删除</s-button>
                </div>
                <s-button on-click="refresh" name="refresh" style="margin: 0 5px;">
                    <outlined-refresh is-button="{{false}}"></outlined-refresh>
                </s-button>
            </div>
        </div>
        <app-tab-page
            active="{=page=}"
            on-change="onTabChange"
            skin="accordion"
            position="left"
            class="palo-tab-page"
        >
            <app-tab-page-panel
                label="集群信息"
                style="{{page | show(1)}}"
                url="#/palo/detail?deployId={{deployId}}"
            >
                <message
                    s-if="page === 1"
                    s-ref="info"
                    deployId="{{deployId}}"
                    detail="{{detail}}"
                    instances="{{instances}}"
                    modules="{{modules}}"
                    isLoading="{{loading}}"
                    on-refresh="refresh"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="集群监控"
                style="{{page | show(2)}}"
                url="#/palo/detail?deployId={{deployId}}"
            >
                <monitor
                    s-if="page === 2"
                    s-ref="monitor"
                    deployId="{{deployId}}" 
                    detail="{{detail}}"
                    isLoading="{{loading}}"
                    type="united"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="备份恢复"
                style="{{page | show(3)}}"
                url="#/palo/detail?deployId={{deployId}}"
            >
                <backup-page
                    s-if="page === 3"
                    pageType="{{route.query.pageType}}"
                    s-ref="backupPage"
                    deployId="{{deployId}}"
                    taskName="{{route.query.taskName}}"
                    detail="{{detail}}"
                    isLoading="{{loading}}"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="查询分析"
                style="{{page | show(4)}}"
                url="#/palo/detail?deployId={{deployId}}"
            >
                <query-page
                    s-if="page === 4"
                    s-ref="queryPage"
                    deployId="{{deployId}}"
                    detail="{{detail}}"
                    isLoading="{{loading}}"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="权限管理"
                style="{{page | show(5)}}"
                url="#/palo/detail?deployId={{deployId}}"
            >
                <permission-manage
                    s-if="page === 5"
                    s-ref="permissionManage"
                    deployId="{{deployId}}"
                    query="{{query}}"
                    detail="{{detail}}"
                    isLoading="{{loading}}"
                />
            </app-tab-page-panel>
            <app-tab-page-panel
                label="变更记录"
                style="{{page | show(6)}}"
                url="#/palo/detail?deployId={{deployId}}"
            >
                <task-list
                    s-if="page === 6"
                    s-ref="tasklist"
                    deployId="{{deployId}}"
                />
            </app-tab-page-panel>
        </app-tab-page>
    </app-detail-page>
`;
@decorators.asPage(...Pages)
export default class PaloDetail extends AppDetailPage {
    static pageName = 'palo-detail';

    static template = template;

    static components = {
        'app-detail-page': AppDetailPage,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPane,
        's-loading': Loading,
        'outlined-refresh': OutlinedRefresh,
        's-button': Button,
        's-table': Table,
        's-pagination': Pagination,
        'outlined-left': OutlinedLeft,
        'message': Info,
        'monitor': Monitor,
        'task-list': TaskList,
        'backup-page': BackUpPage,
        'query-page': QueryPage,
        'permission-manage': PermissionManage
    };

    initData() {
        return {
            clusterStatus,
            detail: {},
            sourceDetail: {},
            backTo: '/palo/#/palo/list',
            deployId: '',
            page: 1,
            loading: true
        };
    }

    static computed = {
        startDisabled(): boolean {
            const allowStart = (paloStatusConfig.allowStart || []).indexOf(this.data.get('detail.status')) > -1;
            return !allowStart || this.data.get('detail.deployId') - 0 <= BOXER1_MAX_ID;
        },
        stopDisabled(): boolean {
            const allowStop = (paloStatusConfig.allowStop || []).indexOf(this.data.get('detail.status')) > -1;
            return !allowStop || this.data.get('detail.deployId') - 0 <= BOXER1_MAX_ID;
        },
        deleteDisabled(): boolean {
            const allowDelete = (paloStatusConfig.allowDelete || []).indexOf(this.data.get('detail.status')) > -1;
            return !allowDelete || this.data.get('sourceDetail.taskStatus') === 'BUSY';
        },
        resizeDisabled(): boolean {
            // 扩容中的集群禁止再次扩容,boxer1不支持集群伸缩
            const allowStretch = (paloStatusConfig.allowStretch || []).indexOf(this.data.get('detail.status')) > -1;
            return !allowStretch || this.data.get('detail.deployId') - 0 <= BOXER1_MAX_ID;
        }
    };

    static filters = {
        show(page: string, key: string) {
            return page === key ? '' : 'display:none';
        },
        renderStatus(detail) {
            return renderStatus(detail.displayActualStatus, detail.status);
        }
    };

    inited() {
        const query = this.data.get('route.query');
        this.data.set('query', query);
        // 监听路由参数变化，更新 query
        this.watch('route.query', val => {
            this.data.set('query', val);
        });
    }

    async attached() {
        const {page = 1, deployId = ''} = this.data.get('route.query');
        this.data.set('page', Number(page));
        this.data.set('deployId', deployId);
        await this.getClusterDetail();
        await this.paloSourceDetail();
    }

    onTabChange(target: {value: {key: number}}) {
        updateUrlQuery({page: target.value.key});
        this.data.set('page', Number(target.value.key));
        this.refresh();
    }


    async getClusterDetail() {
        const {deployId} = this.data.get('');
        if (!deployId) {
            Notification.error('缺少集群ID，请返回列表页重试');
            return;
        }
        this.data.set('loading', true);
        const data = await this.$http.paloPost('paloDeployDetail', {deployId});
        this.data.set('detail', data || {});
        this.data.set('modules', data.modules);
        this.data.set('instances', data.instances?.map(item => ({
            ...item,
            restartable: _.includes(clusterIdToRestart, deployId) || item.moduleDisplayName !== 'Leader Node'
        })));
        this.data.set('loading', false);
    }

    paloSourceDetail() {
        const deployId = this.data.get('deployId');
        if (!deployId) {
            Notification.error('缺少集群ID，请返回列表页重试');
            return;
        }
        this.$http.paloPost('paloSourceDetail', {deployId}).then(data => {
            this.data.set('sourceDetail', data || {});
        });
    }

    async handleActions(key: string) {
        const detail = this.data.get('detail');
        const params = {
            deployId: detail.deployId
        };
        const actionMap = {
            start: {
                content: '启动',
                apiName: 'paloDeployStart',
                params
            },
            stop: {
                content: '停止',
                apiName: 'paloDeployStop',
                params
            },
            delete: {
                content: '删除',
                apiName: 'paloDeployDelete',
                params: {
                    ...params,
                    region: ServiceFactory.resolve('$context').getCurrentRegion().id
                }
            },
            cancelStretch: {
                content: '取消伸缩',
                apiName: 'paloCancelStretch',
                params: {
                    ...params,
                    orderId: detail.orderId
                }
            }
        };
        try {
            await Dialog.warning({
                content: `您确定${actionMap[key].content}集群${detail.deployName}吗？`,
                title: '提示',
                onOk: async () => {
                    try {
                        await this.$http.paloPost(actionMap[key].apiName, actionMap[key].params);
                        Notification.success('操作成功');
                        if (key === 'delete') {
                            redirect('/palo/#/palo/list');
                        } else {
                            this.refresh();
                        }
                    } catch (e) {
                        Notification.error('操作失败，请重试！');
                    }
                }
            });
        } catch (e) {}
    }

    goStretch() {
        const deployId = this.data.get('route.query.deployId');
        window.location.hash = `#/palo/stretch?deployId=${deployId}`;
    }

    onBack() {
        redirect(this.data.get('backTo'));
    }

    async refresh() {
        const {page} = this.data.get('');
        if (page === 2) {
            this.data.set('loading', true);
            setTimeout(() => {
                this.data.set('loading', false);
            }, 100);
            this.ref('monitor')?.refresh();
        }
        else if (page === 1) {
            await this.getClusterDetail();
            this.ref('info')?.refresh();
        }
        else if (page === 3) {
            this.ref('backupPage')?.refresh();
        }
        else if (page === 6) {
            this.ref('tasklist')?.refresh();
        }
        else if (page === 4) {
            this.ref('queryPage')?.refresh();
        }
        else if (page === 5) {
            this.ref('permissionManage')?.refresh();
        }
    }

    // region 切换
    onRegionChange() {
        redirect('#/palo/list');
    }
}
