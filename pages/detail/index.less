#bce-content:has(.palo-detail) {
    overflow-x: auto;
}

@borderColor: #E8E9EB;
@height: 120px;
.palo-detail {
    .detail-name {
        .instance-editor {
            display: none;
        }
    
        &:hover {
    
            .instance-editor {
                display: inline-block;
            }
        }
    }

    .palo-detail-instanceinfo {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .palo-detail-instancename {
        display: block;
        margin-left: 16px;
        font-size: 16px;
        color: var(--titleColor);
        font-weight: 500;
    }

    .app-tab-page {
        padding: 0;
        overflow-x: hidden;
    }

    .palo-detail-button {
        display: flex;
    }

    .palo-detail-button .s-button {
        padding: 0 17.5px;
        display: flex;
    }

    .palo-detail-status {
        margin-left: 12px;
        width: 70px;
    }

    .palo-detail-left {
        display: flex;
        align-items: center;
    }

    .palo-detail-right {
        display: flex;
        align-items: center;
    }

    .ellipsis {
        max-width: 95%;
        display: inline-block;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
    }

    .table-btn-slim {
        padding: 0  10px 0 0 !important
    }
    
    .passreset {
        margin-top: -2px;
    }
}

.palo-cluster-detail-info {
    position: relative;
    padding: 24px;
}

.detail-password {
    margin: 24px 0 0 24px;

    .s-form-item-label {
        width: 116px;
        text-align: left;
    }

    .detail-password-button {
        padding: 0 24px;
        margin-left: 360px;
        margin-top: 24px;
    }
}
.palo-detail-monitor {
    padding: 24px;
    position: relative;

    .warn-info-list-item {
        font-size: 14px;
        .s-badge-status-text {
            font-size: 14px;
        }
        .s-form-item-control-content {
            display: flex;
            justify-content: space-between;
        }
    }
    .title {
        height: 50px;
        .right {
            position: absolute;
            right: 24px;
            top: 24px;
        }
    }

    .s-form-item {
        margin-bottom: 0;
    }

    .alarm-charts {
        flex: 1;
        margin-top: 24px;
        .alarm-charts-item {
            display: inline-block;
            width: 50%;
            .ui-bcmchart {
                width: 94%;
                .ui-bcmchart-chart {
                    width: 100% !important;
                }
            }
        }
    }
    .bes-detail-type {
        .s-form-item-control-content {
            line-height: 26px;
        }
    }

    .s-doc-checkbox {
        .s-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            padding: 0 10px;
            .s-checkbox {
                min-width: 200px;
                margin-top: 8px;
            }
        }
    }
}

.hover-cursor:hover {
    cursor: pointer;
    color: rgb(36, 104, 242);
}
