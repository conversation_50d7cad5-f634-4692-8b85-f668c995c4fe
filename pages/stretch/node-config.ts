import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import _ from 'lodash';
import {
    Table,
    Radio,
    Tabs,
    Tag,
    Tooltip
} from '@baidu/sui';
import { ContextService } from '@common/index';
import './index.less';

const nodeTypes = [
    {
        value: 'leader',
        label: 'Leader Node'
    },
    {
        value: 'compute',
        label: 'Compute Node'
    }
];

const upgradeWayOptions = [
    {
        value: 0,
        label: '滚动变更'
    },
    {
        value: 1,
        label: '全量冷重启',
    },
    {
        value: 2,
        label: '蓝绿模式',
    }
];

const columns = [
    { name: 'slotType', label: '实例规格', width: 150 },
    { name: 'cpuNum', label: 'CPU（核）', width: 80 },
    { name: 'memory', label: '内存（GB）', width: 80 },
];

type moduleNode = {
  "type": string,
  "slotType": string,
  "moduleDisplayName": string,
  "version": string,
  "moduleDescription": string,
  "slotDescription": string,
  "cpuNum": number,
  "memory": number,
  "inventoryQuantity": number
}

type moduleSlotsType = {
  "palofe": {
    "通用型": {
      "modules": moduleNode[],
      "configs": any[]
    }
  },
  "palobe": {
    "通用型": {
      "modules": moduleNode[],
      "configs": any[]
    },
    "内存型": {
      "modules": moduleNode[],
      "configs": any[]
    }
  }
}

const tempalte = html`
    <div>
        <div class="broker-change-type mt24">
        <span class="required-flag">*</span>节点类型：
            <s-radio-group
                datasource="{{ nodeTypes }}"
                value="{{nodeType}}"
                class="ml16"
                enhanced
                radioType="button"
                on-change="handleTypeSelectChange"
            ></s-radio-group>
        </div>
        <div class="broker-tabs-config mt24">
            <span class="required-flag">*</span>节点配置：
            <s-tabs active="{{activeKey}}" class="node-config-tabs" on-change="handleChangeActive">
              <s-tabpane
                  s-for="nodeCategory in nodeCategories"
                  label="{{nodeCategory.label}}"
                  key="{{nodeCategory.key}}"
              >
              </s-tabpane>
            </s-tabs>
        </div>
        <s-table
            s-ref="table"
            columns="{{columns}}"
            datasource="{{nodeSource}}"
            loading="{{tableLoading}}"
            selection="{=selection=}"
            on-selected-change="handleSelectedChange"
            class="node-config-table"
            height="300"
        >
            <div class="slot-type-row" slot="c-slotType">
                <div>
                    {{row.slotType}}
                </div>
                <span
                  s-if="row.inventoryQuantity === 0 || row.inventoryQuantity < actualInstanceNum"
                  class="soldout-tag ml16"
                >
                  库存紧张
                </span>
                <span
                  s-if="{{rowIndex === defaultselectedIndex}}"
                  class="current-tag ml16"
                >
                  当前配置
                </span>
            </div>
        </s-table>
        <div class="broker-change-type mt24">
            <span class="required-flag">*</span>变配方式：
            <s-radio-group
                datasource="{{upgradeWayOptions}}"
                value="{= resizeMode =}"
                enhanced
                class="ml16"
                radioType="button"
            ></s-radio-group>
        </div>
        <span class="resize-mode-desc mt4" s-if="resizeMode === 0">
            集群滚动重启，可能影响服务性能，导入查询可能需要重试。
        </span>
        <span class="resize-mode-desc mt4" s-if="resizeMode === 1">集群重启，服务会中断一段时间，但速度较快。</span>
        <span class="resize-mode-desc mt4" s-if="resizeMode === 2">原节点数据将平滑迁移到新节点，集群不重启，不影响线上服务，但速度较慢</span>
    </div>
`;

export default class NodeConfig extends Component {

    static template = tempalte;

    static components = {
        's-tabs': Tabs,
        's-tabpane': Tabs.TabPane,
        's-table': Table,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-tag': Tag,
        's-tooltip': Tooltip,
    }

    initData() {
        return {
            nodeTypes: nodeTypes,
            nodeType: 'leader',
            columns: columns,
            activeKey: 'general',
            selectedIndex: [],
            resizeMode: 0,
            tableLoading: true,
            currentSlot: []
        };
    }

    static computed = {
        nodeCategories() {
            return [
                {label: '通用型', key: 'general'},
                {label: '内存型', key: 'memory'},
            ];
        },
        nodeSource() {
            const nodeType: string = this.data.get('nodeType');
            const nodeCategory: string = this.data.get('nodeCategories');
            const activeKey: string = this.data.get('activeKey');
            const moduleSlots: moduleSlotsType = this.data.get('moduleSlots');
            const typeIndex = _.findIndex(nodeCategory, ({key}: any) => key === activeKey);
            const type = nodeCategory[typeIndex]?.label;
            return nodeType === 'leader' ? (moduleSlots?.['palofe']?.[type]?.modules) : (moduleSlots?.['palobe']?.[type]?.modules);
        },
        selectionDisabledIndex() {
            const nodeType: string = this.data.get('nodeType');
            const nodeCategory: string = this.data.get('nodeCategories');
            const activeKey: string = this.data.get('activeKey');
            const actualInstanceNum: number = this.data.get('actualInstanceNum');
            const moduleSlots: moduleSlotsType = this.data.get('moduleSlots');
            const typeIndex = _.findIndex(nodeCategory, ({key}: any) => key === activeKey);
            const type = nodeCategory[typeIndex]?.label;
            const nodeSource = nodeType === 'leader'
              ? (moduleSlots?.palofe[type]?.modules)
              : (moduleSlots?.palobe[type]?.modules);
            let disabledIndex: number[] = [];
            nodeSource?.forEach((node: moduleNode, index: number) => {
                if (node.inventoryQuantity === 0 || node.inventoryQuantity < actualInstanceNum) 
                    {
                        disabledIndex.push(index);
                    }
            });
            return disabledIndex;
        },
        selection() {
            const selectedIndex: number[] = this.data.get('selectedIndex');
            const selectionDisabledIndex: number[] = this.data.get('selectionDisabledIndex');
            return {
                mode: 'single',
                selectedIndex: selectedIndex,
                disabledIndex: selectionDisabledIndex
            }
        },
        actualInstanceNum(): number {
            const detail = this.data.get('detail');
            const nodeType = this.data.get('nodeType');
            const modules = detail?.modules || [0, 0];
            const type = nodeType === 'leader' ? 'palofe' : 'palobe';
            const module = modules?.find(item => item.type === type);
            return module ? module?.actualInstanceNum : 0;
        },
        upgradeWayOptions() {
            const nodeType = this.data.get('nodeType');
            return nodeType === 'leader' ? upgradeWayOptions.slice(0, 2) : upgradeWayOptions;
        }
    };

    async attached() {
        const detail = this.data.get('detail');
        const parameter = {
            region: ContextService.getCurrentRegion().id,
            availableZone: detail.availableZone,
            productName: "palo"
        };
        const res = await this.$http.paloPost('palolistWithSlotandStock', parameter);
        this.data.set('moduleSlots', res.moduleSlots);
        this.data.set('tableLoading', false);
        const feIndex = _.findIndex(detail.modules, ({type}: any) => type === 'palofe');
        const slotType: string = detail.modules[feIndex].slotType;
        const nodeSource = this.data.get('nodeSource');
        const index = _.findIndex(nodeSource, (item: any) => item.slotType == slotType);
        this.data.set('selectedIndex', [index]);
        const currentSlot = feIndex === 0 ? [slotType, detail.modules[1].slotType] : [slotType, detail.modules[0].slotType];
        this.data.set('currentSlot', currentSlot);
        this.data.set('defaultselectedIndex', index);
    }

    handleSelectedChange(target: {value: {selectedIndex: number[]}}) {
        this.fire('price-query', {});
    }

    handleTypeSelectChange(target: {value: string}) {
      this.nextTick(() => {
          this.data.set('activeKey', 'general');
          this.data.set('nodeType', target.value);
          target.value === 'leader' && this.data.set('resizeMode', 0);
          const currentSlot = this.data.get('currentSlot');
          const nodeSource = this.data.get('nodeSource');
          const defaultselectedIndex = target.value === 'leader' ? _.findIndex(nodeSource, (item: moduleNode) => item.slotType === currentSlot[0]) : _.findIndex(nodeSource, (item: moduleNode) => item.slotType === currentSlot[1]);
          this.data.set('selectedIndex', [defaultselectedIndex]);
          this.data.set('defaultselectedIndex', defaultselectedIndex);
          this.fire('price-query', {});
        });
    };

    handleChangeActive(target: {value: {key: string}}) {
        this.data.set('activeKey', target.value.key);
        const currentSlot = this.data.get('currentSlot');
        const nodeSource = this.data.get('nodeSource');
        const nodeType = this.data.get('nodeType');
        const defaultselectedIndex = nodeType === 'leader' ? _.findIndex(nodeSource, (item: moduleNode) => item.slotType === currentSlot[0]) : _.findIndex(nodeSource, (item: moduleNode) => item.slotType === currentSlot[1]);
        this.data.set('selectedIndex', [defaultselectedIndex]);
        this.data.set('defaultselectedIndex', defaultselectedIndex);
    }

    getNodeConfigParam() {
       const slotNode = this.ref('table').getSelectedItems()[0];
       const resizeMode = this.data.get('resizeMode');
       const nodeType = this.data.get('nodeType');
       return {
          slotNode,
          resizeMode,
          nodeType
       }
    }
}