import {Component} from 'san';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import _ from 'lodash'
import {AppCreatePage} from '@baidu/sui-biz';
import {
    Button,
    Table,
    Notification,
    Row,
    Col,
    InputNumber,
    Select,
    Steps,
    Dialog,
    Loading,
    Tag,
    Tooltip,
    Link,
    Radio
} from '@baidu/sui';
import {AppLegend} from '@baidu/sui-biz';
import {PAYTYPE, baseOrderItem} from '@common/config';
import {leaderNumConfig, computeNumConfig} from '@common/config';
import {getDeployId, getRealTime} from '@common/utils/index';
import NodeConfig from './node-config';
import {OrderConfirm, ShoppingCart} from '@baiducloud/billing-sdk/san';
import {BillingSDK, OrderType, OrderItem} from '@baiducloud/billing-sdk';

import {ContextService} from '@common/index';
import {NODE_TYPE_LEADER, NODE_TYPE_COMPUTE, NODE_TYPE_STUDIO} from '../create/components/create-utils';

import './index.less';
const $flag = ServiceFactory.resolve('$flag');

const BasicInfo = {
    serviceType: 'PALO', // 购买的服务
    serviceName: 'PALO',
    productType: 'prepay', // 付费方式
    timeUnit: 'MONTH',
    type: OrderType.RESIZE, // 订单类型
};

const NodeModuleChangeEnum = {
    NODE_COUNT: 'NODE_COUNT',
    DISK_TYPE: 'DISK_TYPE',
    DISK_VOLUMN: 'DISK_VOLUMN'
}

const { asPage, invokeComp } = decorators;
const tempalte = html`
    <s-page
        class="{{klass}}" 
        backTo="{{backTo}}"
        pageTitle="配置变更"
    >
    <div s-if="pageLoading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%)">
        <s-loading loading/>
    </div>
    <template s-else>
        <s-steps class="mt20" current="{{step + 1}}" type="simple">
            <s-step
                s-for="i in steps.datasource"
                title="{{i.title}}"
                description="{{i.description}}"
            />
        </s-steps>
        <div
            class="content-card"
            style="display:{{step === 0 ? 'block' : 'none'}}"
        >
            <app-legend label="集群信息" noHighlight />
            <div>
                <s-row class="detail-form-row">
                    <s-col class="detail-form-label" span="12">
                        集群名称 :
                        <span class="ml8 cell-value">{{detail.deployName}}</span>
                    </s-col>
                    <s-col class="detail-form-label" span="12">
                        集群ID :
                        <span class="ml8 cell-value">{{detail.deployId}}</span>
                    </s-col>
                    </s-row>
                    <s-row class="detail-form-row">
                    <s-col class="detail-form-label" span="12">
                        付费方式 :
                        <span class="ml8 cell-value">{{productTypeText}}</span>
                    </s-col>
                    <s-col
                        class="detail-form-label"
                        s-if="{{detail.productType===PAYTYPE.PREPAY}}"
                        span="12"
                    >
                        到期时间 :
                        <span class="ml8 cell-value">{{detail.expireTime | getRealTime}}</span>
                    </s-col>
                </s-row>
                <s-loading s-if="{{!datasource.length}}" size="small" />
                <template s-else>
                    <s-table columns="{{columns}}" style="margin-top: 25px" datasource="{{baseDatasource | noStudioType}}">
                        <div slot="c-diskType">
                            <span>
                                {{getDiskTypeName(row.diskSlotInfo.type)}}
                            </span>
                        </div>
                        <div slot="c-diskVolumn">
                            <span>
                                {{row.diskSlotInfo.size}}
                            </span>
                        </div>
                    </s-table>
                </template>
            </div>
        </div>
        <div
            class="content-card"
            style="display:{{step === 0 ? 'block' : 'none'}}"
        >
            <app-legend label="配置变更" noHighlight />
            <div class="broker-change-type">
                变更类型：
                <s-radio-group
                    disabled="{{detail.isOldPackage}}"
                    datasource="{{ resizeSelectDatasource }}"
                    value="{{resizeSelectType}}"
                    class="ml16"
                    enhanced="{{true}}"
                    radioType="button"
                    on-change="handleResizeSelectChange"
                ></s-radio-group>
            </div>
            <node-config
                s-if="resizeSelectType === 'nodeConfigResize'"
                s-ref="node-config"
                detail="{{detail}}"
                on-price-query="handlePriceQuery"
            />
            <s-table
                columns="{{changeColumns}}"
                class="mt25"
                s-else
                datasource="{{datasource | noStudioType}}"
            >
                    <div slot="c-desireInstanceNum">
                        <s-input-number
                            disabled="{{(resizeSelectType !== 'nodeCountResize') || !row.isAvailable}}"
                            value="{=row.desireInstanceNum=}"
                            width="200"
                            stepStrictly="{{row.type === NODE_TYPE_COMPUTE ? computeNumConfig.stepStrictly : leaderNumConfig.stepStrictly}}"
                            min="{{row.type === NODE_TYPE_COMPUTE ? computeMinNum : leaderMinNum}}"
                            max="{{row.type === NODE_TYPE_COMPUTE ? computeMaxNum : leaderMaxNum}}"
                            step="{{row.type === NODE_TYPE_COMPUTE ? computeNumConfig.step : leaderNumConfig.step}}"
                            on-input="nodeModuleChange($event, row, NodeModuleChangeEnum.NODE_COUNT)"
                            on-change="nodeModuleChange($event, row, NodeModuleChangeEnum.NODE_COUNT)"
                        />
                        <s-tooltip>
                            <s-tag skin="danger" s-if="!row.isAvailable">售罄</s-tag>
                            <div slot="content">
                                售罄，请您<a href="/ticket" target="_blank" style="color: #108cee;">提交工单</a>。
                            </div>
                        </s-tooltip>
                    </div>
                    <div slot="c-diskType">
                        <s-select 
                            disabled="{{ resizeSelectType !== 'nodeDiskResize' || (detail.productType === 'prepay' && row.diskSlotInfo.type === 'premium_ssd' ) }}"
                            datasource="{{ diskTypeSelectDatasource }}" 
                            value="{= row.diskSlotInfo.type =}"
                            on-change="nodeModuleChange($event, row, NodeModuleChangeEnum.DISK_TYPE)"
                        />
                    </div>
                    <div slot="c-diskVolumn">
                        <s-input-number
                            disabled="{{resizeSelectType !== 'nodeDiskResize'}}"
                            value="{= row.diskSlotInfo.size =}"
                            width="200"
                            step-strictly
                            step="{{100}}"
                            min="{{row.type === NODE_TYPE_COMPUTE ? computeMinDiskSize : leaderMinDiskSize}}"
                            max="{{10000}}"
                            on-input="nodeModuleChange($event, row, NodeModuleChangeEnum.DISK_VOLUMN)"
                            on-change="nodeModuleChange($event, row, NodeModuleChangeEnum.DISK_VOLUMN)"
                        />
                    </div>
            </s-table>
        </div>
        <template s-if="step === 1">
            <div class="content-card">
                <order-confirm
                    sdk="{{sdk}}"
                    items="{{items}}"
                    useCoupon="{{detail.productType === 'prepay' && !isRefund}}"
                    merge-by="orderServiceType"
                    class="billing-confirm"
                    theme="default"
                />
            </div>
        </template>
        </template>
        <div slot="pageFooter" >
            <div class="wrapper">
                <ui-button
                    class="step-button"
                    s-if="step !== 0"
                    on-click="backToOrder"
                    size="large"
                    disabled="{{confirming}}"
                >
                    上一步
                </ui-button>
                <ui-button
                    s-if="step === 0"
                    skin="primary" 
                    size="large"
                    class="step-button"
                    on-click="goToConfirm"
                    loading="{{nextStepLoading}}"
                >下一步</ui-button>
                <ui-button
                    class="step-button"
                    s-if="step !== 0"
                    on-click="onConfirm"
                    skin="primary"
                    size="large"
                    disabled="{{confirming}}"
                >
                    提交订单
                </ui-button>
                <div class="shopping-cart shopping-cart-item" style="flex: 1;">
                        <div class="prices-wrapper">
                            <div class="price-content">
                                    <div class="price-item price-item-num-0">
                                        <div><span class="grey-text">配置费用</span></div>
                                        <div>
                                                <span class="price" s-if="pricing">
                                                   -
                                               </span>
                                               <span class="price" s-else>
                                                    {{items[0].price}}
                                               </span>
                                                <span class="grey-text"></span>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </s-page>
`;

// 注册路由服务
@asPage('/palo/stretch')
@invokeComp('@app-row', '@app-cell')
export default class PaloList extends Component {
    static pageName = 'palo-stretch';

    static template = tempalte;

    static components = {
        's-page': AppCreatePage,
        'ui-button': Button,
        's-row': Row,
        's-col': Col,
        's-table': Table,
        's-input-number': InputNumber,
        's-select': Select,
        's-option': Select.Option,
        's-steps': Steps,
        's-step': Steps.Step,
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        's-loading': Loading,
        's-tag': Tag,
        's-tooltip': Tooltip,
        's-link': Link,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        'app-legend': AppLegend,
        'node-config': NodeConfig,
    }

    initData() {
        return {
            leaderNumConfig,
            computeNumConfig,
            NODE_TYPE_COMPUTE,
            NodeModuleChangeEnum,
            klass: ['palo-stretch'],
            backTo: `/palo/list`,
            steps: {
                datasource: [{
                    title: '集群伸缩'
                }, {
                    title: '确认创建'
                }, {
                    title: '创建成功'
                }],
                current: 1
            },
            sdk: new BillingSDK(BasicInfo, window.$context),
            items: [],
            detail: null,
            step: 0,
            // 订单确认中
            confirming: false,
            // 正在询价（还没获取到价格），初始值true不让点击下一步
            pricing: true,
            param: {
                deployId: getDeployId()
            },
            PAYTYPE,
            columns: [
                { name: 'moduleDisplayName', label: '节点类型' },
                { name: 'desireInstanceNum', label: '节点数量' },
                { name: 'slotDescription', label: '节点配置详情' },
                { name: 'diskType', label: '磁盘类型' },
                { name: 'diskVolumn', label: '磁盘容量 (GB)' }
            ],
            changeColumns: [
                { name: 'moduleDisplayName', label: '节点类型' },
                { name: 'desireInstanceNum', label: '节点数量' },
                { name: 'diskType', label: '磁盘类型' },
                { name: 'diskVolumn', label: '磁盘容量 (GB)' }
            ],
            computeMinNum: 1,
            leaderMinNum: 1,
            computeMaxNum: computeNumConfig.max,
            leaderMaxNum: leaderNumConfig.max,
            computeMinDiskSize: 0,
            leaderMinDiskSize: 0,
            baseDatasource: [],
            datasource: [],
            resizeSelectType: 'nodeCountResize',
            resizeSelectDatasource: [
                {
                    value: 'nodeCountResize',
                    text: '节点数量变更'
                },
                {
                    value: 'nodeDiskResize',
                    text: '节点磁盘变更'
                },
                {
                    value: 'nodeConfigResize',
                    text: '节点配置变更'
                }
            ],
            diskTypeSelectDatasource: [
                {
                    value: 'premium_ssd',
                    text: 'SSD云磁盘'
                },
                {
                    value: 'ssd',
                    text: '高性能云磁盘'
                }
            ],
            hasChanged: false,
            isRefund: false,
            nextStepLoading: false,
            pageLoading: true
        };
    }

    static computed = {
        productTypeText(): string {
            return PAYTYPE.getTextFromValue(this.data.get('detail.productType'));
        },
    };
    static filters = {
        getRealTime: function (expireTime: string | number | Date) {
            return expireTime ? getRealTime(expireTime) : '-'
        },
        noStudioType: function (datasource: any) {
            return datasource.filter((data: any) => data.type !== NODE_TYPE_STUDIO);
        }
    }

    attached() {
        this.refresh();
    }

    getDiskTypeName(value: any) {
        return this.data.get('diskTypeSelectDatasource')?.find((item: any) => item.value === value)?.text
    }

    refresh() {
        const parameter = this.data.get('param');
        if (!parameter.deployId) {
            Notification.error('缺少集群ID，请返回详情页重试');
            return;
        }
        Promise.all([this.$http.paloPost('paloDeployDetail', parameter), this.$http.paloPost('paloAppStock', {
            region: ContextService.getCurrentRegion().id,
            ...parameter
        }).catch(() => ({moduleStockList: []}))]).then((res: any) => {
            const [data, {moduleStockList}] = res;
            this.data.set('detail', data || {});
            const datasource = data.modules.map((module: any) => {
                const moduleStock = moduleStockList.find((item: any) => item.moduleName === module.type);
                const isAvailable = !moduleStock || moduleStock.inventoryQuantity > 0;
                return {
                    ...module,
                    isAvailable,
                }
            })
            this.data.set('baseDatasource', JSON.parse(JSON.stringify(datasource)))
            this.data.set('datasource', JSON.parse(JSON.stringify(datasource)));
            const currentComputeNum = data.modules.find((item: any) => item.type === NODE_TYPE_COMPUTE).desireInstanceNum;
            const currentLedaerNum = data.modules.find((item: any) => item.type === NODE_TYPE_LEADER).desireInstanceNum;
            const inventoryQuantityCompute = moduleStockList?.find((item: any) => item.moduleName === NODE_TYPE_COMPUTE)?.inventoryQuantity;
            const inventoryQuantityLeader = moduleStockList?.find((item: any) => item.moduleName === NODE_TYPE_LEADER)?.inventoryQuantity;
            this.data.set('computeMinNum', currentComputeNum);
            this.data.set('leaderMinNum', currentLedaerNum);
            this.data.set('computeMaxNum', inventoryQuantityCompute ? Math.min(
                inventoryQuantityCompute + currentComputeNum,
                computeNumConfig.max
            ) : computeNumConfig.max);
            this.data.set('leaderMaxNum', inventoryQuantityLeader ? Math.min(
                inventoryQuantityLeader + currentLedaerNum,
                leaderNumConfig.max
            ) : leaderNumConfig.max);
            this.data.set('computeMinDiskSize', data.modules.find((item: any) => item.type === NODE_TYPE_COMPUTE).diskSlotInfo.size)
            this.data.set('leaderMinDiskSize', data.modules.find((item: any) => item.type === NODE_TYPE_LEADER).diskSlotInfo.size)
            this.getPaloOrderPrice(data);
            this.data.set('pageLoading', false);
        });
    }

    handlePriceQuery() {
        this.getPaloOrderPrice({
            ...this.data.get('detail')
        });
    }

    async getPaloOrderPrice(detail: any) {
        this.data.set('pricing', true);
        let apiName = '';
        let parameter = {};
        // 预付费用户走接口paloOrderStretchPrepayPrice
        if (detail.productType === PAYTYPE.PREPAY) {
            apiName = 'paloOrderStretchPrepayPrice';
            parameter = this.getPriceParams(detail);
        } else {
            apiName = 'paloOrderGetNewPackagePrice';
            parameter = this.getPriceParamsNew(detail);
        }
        const data = await this.$http.paloPost(apiName, parameter);
        this.initOrderItems(detail, data.total || data.price, parameter, data.dayCount);
        const price = data.total || data.price
        // 是否是退款
        this.data.set('isRefund', price < 0)
        this.data.set('pricing', false);
    }

    nodeModuleChange = _.debounce((e: any, row: any, type: keyof typeof NodeModuleChangeEnum) => {
        this.data.set('hasChanged', true);
        let datasource = this.data.get('datasource');
        datasource.forEach((element: any) => {
            if (element.type === row.type) {
                switch (type) {
                    case NodeModuleChangeEnum.NODE_COUNT:
                        element.desireInstanceNum = e.value;
                        break
                    case NodeModuleChangeEnum.DISK_VOLUMN:
                        element.diskSlotInfo.size = e.value;
                        break;
                    case NodeModuleChangeEnum.DISK_TYPE:
                        element.diskSlotInfo.type = e.value;
                        break;
                    default:
                        break;
                }
            }
        });
        this.data.set('datasource', datasource);
        this.getPaloOrderPrice({ ...this.data.get('detail'), modules: datasource });
    }, 300)

    checkBeforeConfirm() {
        const datasource = this.data.get('datasource')
        return datasource.every((item: any) => {
            if (item.type === 'palobe') {
                return item.diskSlotInfo.size >= this.data.get('computeMinDiskSize')
            } else {
            }
            return item.diskSlotInfo.size >= this.data.get('leaderMinDiskSize')
        })
    }

    // 点击立即购买，显示确认订单
    async goToConfirm() {
        this.data.set('nextStepLoading', true);
        try {
            await new Promise((resolve) => {
                setTimeout(() => {
                    resolve('')
                }, 1000)
            })
            const checked = this.checkBeforeConfirm()
            if (!checked) {
                Notification.warning('不支持磁盘容量降配')
                return
            }
            const parameter = this.getCheckParams();
            const resizeSelectType = this.data.get('resizeSelectType');
            await this.$http.paloPost('paloResizeCheck', {
                ...parameter,
                resizeType: resizeSelectType === 'nodeCountResize' ? 0 : resizeSelectType === 'nodeConfigResize' ? 1 : 2
            });
            this.data.set('step', this.data.get('step') + 1);
            this.data.set('pageNav.step.index', this.data.get('pageNav.step.index') + 1);
        } finally {
            this.data.set('nextStepLoading', false)
        }
    }
    onReset() {
        window.location.reload();
    }
    onConfirm() {
        const XSFlag = $flag.PaloXS;
        this.data.set('confirming', true);
        const productType = this.data.get('detail.productType');
        const isRefund = this.data.get('isRefund');
        const resizeSelectType = this.data.get('resizeSelectType');
        const parameter = {
            items: [
                {
                    config: {
                        ...this.getCheckParams(),
                        resizeType: resizeSelectType === 'nodeCountResize' ? 0 : resizeSelectType === 'nodeConfigResize' ? 1 : 2
                    },
                    paymentMethod: productType === PAYTYPE.PREPAY && !isRefund ? this.getCoupons() : []
                },
            ],
            paymentMethod: [],
        };
        this.$http.paloPost('paloResizeStretch', parameter)
            .then((data: any) => {
                const sdk = this.data.get('sdk');
                return sdk
                    .checkPayInfo(data)
                    .then(({ url = null }) =>  url)
                    .catch(({ url = null }) => url);
            })
            .then((url: any) => {
                if (!XSFlag) {
                    redirect(url);
                }
            })
            .catch((e: any) => {
                this.data.set("confirming", false);
            });
    }

    /**
     * 获取优惠券列表，没有就返回[]
     * @returns [couponId]
     */
    getCoupons() {
        const items = this.data.get('items') || [];
        const couponId = items[0]?.couponId || '';
        // 不选择优惠券
        if (!couponId) {
            return [];
        }
        return [{
            type: 'coupon',
            values: [couponId]
        }];
    }

    backToOrder() {
        this.data.set('step', this.data.get('step') - 1);
        this.data.set('pageNav.step.index', this.data.get('pageNav.step.index') - 1);
    }

    getDescription(slotType: string) {
        const arr = slotType?.slice(3)?.split('m') || [];
        return `CPU ${arr[0]}核，内存${arr[1]}GB`;
    }

    initOrderItems(detail: any, price?: number | string, parameters?: any, dayCount?: number) {
        let localOrderItem = baseOrderItem;
        localOrderItem.region = detail.region;
        localOrderItem.type = OrderType.RESIZE;
        localOrderItem.productType = detail.productType;
        localOrderItem.timeUnit = detail.productType === PAYTYPE.POSTPAY ? 'MINUTE' : 'DAY',
        localOrderItem.price = price;
        const arr = [{ label: '地域', value: detail.region },
            { label: '可用区', value: '可用区' + detail.availableZone.slice(4) }];
        if (detail.productType === PAYTYPE.POSTPAY) {
            let computeNode = parameters.modules.filter(
                (item: any) => item.module_type === NODE_TYPE_COMPUTE
            )[0];
            let leaderNode = parameters.modules.filter(
                (item: any) => item.module_type === NODE_TYPE_LEADER
            )[0];
            
            localOrderItem.configDetail = [
                ...arr,
                {
                    label: 'Compute Node',
                    value: `机型：${computeNode?.slot_type}；${this.getDescription(computeNode?.slot_type)}：${this.getDiskTypeName(computeNode?.diskSlotInfo?.type)} ${computeNode?.diskSlotInfo?.size}G；
                    数量：${computeNode?.instance_num}`
                },
                {
                    label: 'Leader Node',
                    value: `机型：${leaderNode?.slot_type}；${this.getDescription(leaderNode?.slot_type)}： ${this.getDiskTypeName(leaderNode?.diskSlotInfo?.type)} ${leaderNode?.diskSlotInfo?.size}G；
                    数量：${leaderNode?.instance_num}`
                }
            ];
        }
        else {
            localOrderItem.duration = dayCount;
            let computeNode = parameters.modules.filter(
                (item: any) => item.type === NODE_TYPE_COMPUTE
            )[0];
            let leaderNode = parameters.modules.filter(
                (item: any) => item.type === NODE_TYPE_LEADER
            )[0];
            
            localOrderItem.configDetail = [
                ...arr,
                {
                    label: 'Compute Node',
                    value: `机型：${computeNode?.slot_type}；${this.getDescription(computeNode?.slot_type)}：${this.getDiskTypeName(computeNode?.diskSlotInfo?.type)} ${computeNode?.diskSlotInfo?.size}G；
                    数量：${computeNode?.desireInstanceNum}`
                },
                {
                    label: 'Leader Node',
                    value: `机型：${leaderNode?.slot_type}；${this.getDescription(leaderNode?.slot_type)}： ${this.getDiskTypeName(leaderNode?.diskSlotInfo?.type)} ${leaderNode?.diskSlotInfo?.size}G；
                    数量：${leaderNode?.desireInstanceNum}`
                }
            ];
        }
        this.data.set('items', []);
        const orderItem = new OrderItem(localOrderItem);
        this.data.set('items',[orderItem] )
    }

    getPriceParams(detail: any) {
        const params = {...this.getCheckParams()};
        delete params.resizeMode;
        return {
            ...params,
            isOldPackage: detail.isOldPackage
        };
    }

    getPriceParamsNew(detail: any) {
        const modules: any = [];
        const resizeSelectType = this.data.get('resizeSelectType');
        const params = {
            region: ContextService.getCurrentRegion().id,
            productType: detail.productType,
            isOldPackage: detail.isOldPackage
        };
        if (resizeSelectType !== 'nodeConfigResize') {
            detail.modules.forEach((item: any) => {
                const module: any = {
                    instance_num: item.desireInstanceNum,
                    module_type: item.type,
                    module_version: item.version,
                    slot_type: item.slotType,
                    product_name: item.type
                }
                if (item.diskSlotInfo) {
                    module.diskSlotInfo = item.diskSlotInfo;
                    module.diskSlotInfo.instanceNum = item.desireInstanceNum
                }
                modules.push(module);
            });
            return {
                ...params,
                modules,
            };
        }
        else {
            const nodeConfigParam = this.ref('node-config')?.getNodeConfigParam();
            const {nodeType, slotNode} = nodeConfigParam;
            const {
                slotType = null
            } = slotNode;

            const newNode = {
                slot_type: slotType,
            }

            const tempDetail = this.data.get('detail').modules.map((item: any) => ({
                instance_num: item.desireInstanceNum,
                module_type: item.type,
                module_version: item.version,
                slot_type: item.slotType,
                product_name: item.type,
                diskSlotInfo: {
                    ...item.diskSlotInfo,
                    instanceNum: item.desireInstanceNum
                }
            }));

            const computeNode = tempDetail.filter((item: any) => item.module_type === NODE_TYPE_COMPUTE)[0];
            const leaderNode = tempDetail.filter((item: any) => item.module_type === NODE_TYPE_LEADER)[0];

            return {
                ...params,
                modules: nodeType === 'leader' ? [computeNode, {
                    ...newNode,
                    module_type: leaderNode.module_type,
                    module_version: leaderNode.module_version,
                    product_name: leaderNode.product_name,
                    instance_num: leaderNode.instance_num,
                    diskSlotInfo: {
                        ...leaderNode.diskSlotInfo,
                    }
                }] : [{
                    ...newNode,
                    module_type: computeNode.module_type,
                    module_version: computeNode.module_version,
                    product_name: computeNode.product_name,
                    instance_num: computeNode.instance_num,
                    diskSlotInfo: {
                        ...computeNode.diskSlotInfo,
                    }
                }, leaderNode]
            }
        }
    }

    getCheckParams() {
        const datasource = this.data.get('datasource');
        const detail = this.data.get('detail');
        const nodeConfigParam = this.ref('node-config')?.getNodeConfigParam();
        const resizeSelectType = this.data.get('resizeSelectType');
        const params = {
            deployId: this.data.get('param').deployId,
            productType: detail.productType,
            region: detail.region,
        }
        if (resizeSelectType === 'nodeConfigResize') {
            const {nodeType, slotNode} = nodeConfigParam;
            const {
                slotType = null,
            } = slotNode;
            const computeDetail = detail.modules.filter((item: any) => item.type == NODE_TYPE_COMPUTE)[0];
            const leaderDetail = detail.modules.filter((item: any) => item.type == NODE_TYPE_LEADER)[0];
            const modules = nodeType === 'leader' ? [
                {
                    desireInstanceNum: computeDetail.desireInstanceNum,
                    slot_type: computeDetail.slotType,
                    type: computeDetail.type,
                    version: computeDetail.version,
                    diskSlotInfo: {
                        ...computeDetail.diskSlotInfo,
                        instanceNum: computeDetail.desireInstanceNum
                    }
                },
                {
                    desireInstanceNum: leaderDetail.desireInstanceNum,
                    slot_type: slotType,
                    type: leaderDetail.type,
                    version: leaderDetail.version,
                    diskSlotInfo: {
                        ...leaderDetail.diskSlotInfo,
                        instanceNum: leaderDetail.desireInstanceNum
                    }
                }
            ] : [
                {
                    desireInstanceNum: computeDetail.desireInstanceNum,
                    slot_type: slotType,
                    type: computeDetail.type,
                    version: computeDetail.version,
                    diskSlotInfo: {
                        ...computeDetail.diskSlotInfo,
                        instanceNum: computeDetail.desireInstanceNum
                    }
                },
                {
                    desireInstanceNum: leaderDetail.desireInstanceNum,
                    slot_type: leaderDetail. slotType,
                    type: leaderDetail.type,
                    version: leaderDetail.version,
                    diskSlotInfo: {
                        ...leaderDetail.diskSlotInfo,
                        instanceNum: leaderDetail.desireInstanceNum
                    }
                },
            ];
            return {
                ...params,
                modules,
                resizeMode: nodeConfigParam.resizeMode
            }
        }
        return {
            ...params,
            modules: datasource.map((item: any) => {
                const {
                    desireInstanceNum = null,
                    slotType = null,
                    type = null,
                    version = null,
                    diskSlotInfo = null
                } = item;
                return {
                    desireInstanceNum,
                    slot_type: slotType,
                    type,
                    version,
                    diskSlotInfo: { 
                        ...diskSlotInfo,
                        instanceNum: desireInstanceNum
                    }
                };
            })
        }
    }

    handleResizeSelectChange(e: any) {
        const resizeSelectType = this.data.get('resizeSelectType');
        if (!this.data.get('hasChanged') && resizeSelectType !== 'nodeConfigResize') {
            this.data.set('resizeSelectType', e.value);
            return;
        }
        else if (this.data.get('hasChanged') || resizeSelectType === 'nodeConfigResize') {
            Dialog.confirm({
                title: '注意',
                content: '调整配置变更类型后，当前信息不会保留，请确认调整变更类型',
                onOk: () => {
                    // 重置 datasource
                    this.nextTick(() => {
                        this.data.set('resizeSelectType', e.value);
                        this.data.set('datasource', JSON.parse(JSON.stringify(this.data.get('baseDatasource') || [])));
                        
                        this.data.set('hasChanged', false);
                    });
                    // 每次更改select后询价
                    this.getPaloOrderPrice({ ...this.data.get('detail'), modules: this.data.get('datasource') });
                },
                onCancel: () => {
                    this.nextTick(() => {
                        this.data.set('resizeSelectType', '');
                        this.data.set('resizeSelectType', resizeSelectType);
                    });
                }
            } as any)
        }
        else {
            this.data.set('resizeSelectType', e.value);
            this.data.set('datasource', JSON.parse(JSON.stringify(this.data.get('baseDatasource') || [])))
            this.data.set('hasChanged', false);
            this.getPaloOrderPrice({ ...this.data.get('detail'), modules: this.data.get('datasource') });
        }
    }
}