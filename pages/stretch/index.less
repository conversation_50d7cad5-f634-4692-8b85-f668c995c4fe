#bce-content:has(.palo-stretch) {
    overflow-x: auto;
}
.palorenewal-view .biz-app {
    margin-top: 0;
}

.palo-stretch{
    overflow: auto;
    .app-create-page-content {
        margin: 0 auto;
        min-width: 1040px;
        background-color: #f7f7f9;
        .s-step-content-title, .s-step-content-icon {
            background-color: #f7f7f9;
        }
        .billing-sdk-order-confirm-wrapper {
            width: 100%;
            .billing-sdk-order-legend {
                width: 100%;
                background-color: #fff;
                .legend-header {
                    background-color: #fff;
                }
            }
            // 隐藏订单右上角信息
            .charge-wrapper {
                display: none;
            }
        }
        .app-legend {
            background-color: #fff;
        }
        .content-card {
            background-color: #fff;
        }
    }

    .cell-value {
        color: #151b26;
    }

    .app-create-page-footer {
        width: 100%;
        height: 60px;
        left: 50%;
        transform: translate(-50%);
    }
    .page-footer-wrapper {
        width: 100%;
        padding: 10px 0;
    }

    .s-create-page-footer {
        width: 100%;
        height: 80px;
        left: 50%;
        transform: translate(-50%);
    }

    .broker-change-type {
        display: flex;
        align-items: center;
    }

    .resize-mode-desc {
        color: #84868c;
        font-size: 12px;
        line-height: 20px;
        display: block;
        position: relative;
        left: 78px;
    }

    .required-flag {
        color: #f33e3e;
        margin-right: 4px;
        margin-left: -8px;
    }

    .wrapper {
        display: flex;
        .shopping-cart {
            margin-left: 30px;
            .price-item {
                padding: 0;
            }
        }
    }

    .step-button {
        height: 40px;
        box-sizing: border-box;
        border-radius: 4px;
        margin: 0 10px;
        font-size: 12px;;
        &:first-of-type {
            margin: 0 10px 0 0;
        }
    }

    .page-title-nav {
        position: static;
        font-size: 14px;
    }

    .detail-form-row {
        margin: 10px 0;
    }
    .detail-form-label {
        font-size: 12px;
        color: #666;
    }
    .right-buttons {
        position: absolute;
        right: 20px;
        top: 25px;
    }

    .order-item-container {
        .content {
            display: block;
        }

        .item {
            display: block;
            width: 100%;
        }
    }

    .content-card {
        padding: 24px;
        margin: 16px;
        width: calc(~'100% - 32px');
        min-width: 1280px;
        margin-top: 0;
        border-radius: 6px;
        background-color: #fff;

        .title {
            margin-bottom: 24px;

            label {
                font-weight: 500;
                font-size: 16px;
            }
        }

        .detail-form-row {
            margin-top: 0;
            margin-bottom: 16px;

            .detail-form-label {
                .label {
                    width: 60px;
                }
            }
        }

        .bes-stretch-models {
            .s-radio {
                align-items: flex-start;
            }
        }
    }

    .broker-tabs-config {
        display: flex;
        align-items: flex-start;
        .node-config-tabs {
            width: calc(~'100% - 82px');
            margin: -10px 0 0 16px;

            .s-tabnav-nav {
                border-bottom: 0!important;
            }
        }
    }

    .node-config-table {
        width: 600px;
        margin-top: 15px;
        margin-left: 82px;

        .s-table-row-disabled {
            .s-table-cell-text {
                color: #84868c;
            }
        }

        .soldout-tag {
            padding: 0 8px;
            background: #FF9326;
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
            line-height: 20px;
            font-weight: 400;
        }

        .current-tag {
            padding: 0 8px;
            background: #2468f2;
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
            line-height: 20px;
            font-weight: 400;
        }
    }

    .no-store-tag {
        background: #FF9326;
        border-radius: 2px;
        padding: 2px 8px;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 20px;
        font-weight: 400;
    }

    .billing-confirm .order-legend .item:nth-child(3n+3)>label {
        width: 115px;
    }
    
    .billing-confirm .order-legend .item:nth-child(3n+2)>label {
        width: 115px;
    }

    .billing-confirm .order-legend .item:nth-child(3n+1)>label {
        width: 70px;
    }
}