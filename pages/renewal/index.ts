/**
 * 续费
*/

import { Component } from "san";
import { decorators, html, redirect, ServiceFactory } from '@baiducloud/runtime';
import { AppCreatePage } from '@baidu/sui-biz';
import { OrderItem, BillingSDK } from '@baiducloud/billing-sdk';
import { OrderConfirm, ShoppingCart } from '@baiducloud/billing-sdk/san';
import { Steps, Table, Button } from '@baidu/sui';
import {ContextService} from '@common/index';
import {RechargeMonth, PAYTYPE} from '@common/config';
import {RechargeYear, baseOrderItem} from '@common/config';
import { getCPUNum, getStoreNum } from '../create/components/create-utils';

import './index.less';
const $flag = ServiceFactory.resolve('$flag');
const { asPage, invokeComp } = decorators;
const template = html`
    <s-page
        class="palo-renewal"
        backTo="{{backTo}}"
        pageTitle="续费">
        <s-steps
            style="margin-top:20px"
            current="{{steps.current}}"
            type="simple"
            on-change="stepChange">
            <s-step
                s-for="step in steps.datasource"
                title="{{step.title}}"
                description="{{step.description}}"/>
        </s-steps>
        <div class="content-card" style="display:{{steps.current === 1 ? 'block' : 'none'}}; background: #fff; padding: 0 16px 20px; margin-top: 20px">
            <renewal-info
                san-ref="renewal-info"/>
        </div>
        <div s-if="steps.current === 2" class="content-card">
            <order-confirm
                s-if="$flag.PaloXS"
                sdk="{{sdk}}"
                hideFees="{{true}}"
            />
            <order-confirm
                s-else
                sdk="{{sdk}}"
                useCoupon="true"
                items="{{items}}"
                merge-by="orderServiceType"
                class="mt10 billing-confirm"
                theme="default"
            />
        </div>
        <div slot="pageFooter">
            <div class="wrapper">
                <s-button
                    s-if="steps.current === 2"
                    on-click="stepPre"
                    class="step-button"
                >
                    上一步
                </s-button>
                <s-button
                    skin="primary"
                    s-if="steps.current === 1"
                    on-click="stepNext"
                    class="step-button"
                >
                    下一步
                </s-button>
                <s-button
                    s-if="steps.current === 2"
                    skin="primary"
                    disabled="{{confirming}}"
                    on-click="onConfirm"
                    class="step-button"
                >
                    提交订单
                </s-button>
                <div class="shopping-cart shopping-cart-item" style="flex: 1;">
                    <div class="prices-wrapper">
                        <div class="price-content">
                                <div class="price-item price-item-num-0">
                                    <div><span class="grey-text">配置费用</span></div>
                                    <div>
                                            <span class="price" s-if="pricing">
                                            -
                                        </span>
                                        <span class="price" s-else>
                                                {{items[0].price}}
                                        </span>
                                            <span class="grey-text"></span>
                                    </div>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </s-page>
`;
@asPage('/palo/renewal')
@invokeComp('@renewal-info')

export default class Renewal extends Component {
    static pageName = 'palorenewal';
    static template = template;

    static components = {
        's-page': AppCreatePage,
        's-steps': Steps,
        's-step': Steps.Step,
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        's-table': Table,
        's-button': Button
    };

    initData() {
        return {
            backToLabel: '返回列表',
            backTo: '/palo/list',
            steps: {
                datasource: [{
                    title: '续费时长'
                }, {
                    title: '确认订单'
                }, {
                    title: '续费成功'
                }],
                current: 1
            },
            items: [],
            sdk: new BillingSDK({
                AllRegion: ContextService.getEnum('AllRegion'),
            }, window.$context),
            detailColumns: [
                { name: 'deployId', label: 'ID' },
                // {name: 'deployName',label: '产品名称'},
                // {name: 'expireTime',label: '到期时间'},
                { name: 'duration', label: '续费时长' },
                { name: 'newExpiredTime', label: '续费后到期时间' },
                // {name: 'totalExpense',label: '金额'},
            ],
            confirming: false
        };
    }


    static filters = {
        setDetailDataSource: function (items: any) {
            console.log(items);
            return items[0]?.options?.displayConfig || [];
        }
    }

    static messages = {
        'renewal-change': function (arg: any) {
            this.initOrder(arg.value);
        }
    }

    async initOrder(orderData: any) {
        const configDetail = this.getConfigDetail(orderData);
        const displayConfig = this.getDisplayConfig(orderData);
        let timeUnit = orderData.productType === PAYTYPE.POSTPAY ? 'MINUTE' : 'MONTH';
        const duration = orderData.duration.rechargeValue === 12 ? 1: orderData.duration.rechargeValue;
        if (orderData.duration.rechargeValue === 12) {
            timeUnit = 'YEAR';
        }
        const orderItem = new OrderItem({
            ...baseOrderItem,
            region: orderData.region,
            type: 'RENEW',
            productType: orderData.productType,
            price: orderData.totalExpense,
            duration,
            timeUnit,
            configDetail,
            displayConfig
        });
        this.data.set('items', [orderItem]);
    }

    getConfigDetail(orderData: any) {
        const configDetail = [
            // {label: '付费方式', value: PayType[orderData.productType]},
            { label: '地域', value: ContextService.getCurrentRegion().label },
            { label: '可用区', value: '可用区' + orderData.availableZone.slice(4) }
        ];
        orderData.modules.forEach((item: any) => {
            const slotType = item.slotType || '';
            const CPU = getCPUNum(item);
            const storage = getStoreNum(item);
            // const {numValue, diskSize} = nodeData;
            let detailInfo = `机型：${slotType}；CPU：${CPU}核；内存：${storage}GB`;
            configDetail.push({
                label: item.moduleDisplayName,
                value: detailInfo
            }, {
                label: '数量',
                value: item.desireInstanceNum
            });
        });
        configDetail.push({
            label: '续费时长',
            value: this.getDuration(orderData)
        });
        return configDetail;
    }

    getDisplayConfig(orderData: any) {
        return [
            {
                deployId: orderData.deployId,
                deployName: orderData.deployName,
                expireTime: orderData.expireTime,
                duration: this.getDuration(orderData),
                newExpiredTime: orderData.newExpiredTime,
                totalExpense: orderData.totalExpense
            }
        ];
    }

    getDuration(orderData: any) {
        const { rechargeValue } = orderData.duration;
        let res = RechargeMonth.find((item: any) => item.value === rechargeValue)
            || RechargeYear.find((item: any) => item.value === rechargeValue);
        return res.text;
    }

    stepNext() {
        this.data.set('steps.current', this.data.get('steps.current') + 1)
    }

    stepPre() {
        this.data.set('steps.current', this.data.get('steps.current') - 1)
    }

    /**
     * 提交订单
     */
    async onConfirm() {
        const XSFlag = $flag.PaloXS;
        this.data.set('confirming', true);
        const param = this.getOrderParams();
        try {
            const data = await this.$http.paloPost('paloRenewConfirm', param);
            const sdk = this.data.get('sdk');
            try {
                const { url } = await sdk.checkPayInfo(data);
                if (!XSFlag && url) {
                    redirect(url);
                }
            } catch (e: any) {
                const { url } = e;
                if (!XSFlag && url) {
                    redirect(url);
                }
            }
        } catch (e: any) {
        } finally {
            this.data.set('confirming', false);
        }
    }


    /**
     * 处理订单接口的参数
     * @returns 
     */
    getOrderParams() {
        const rowData = this.ref('renewal-info').getDataSource();
        return {
            items: [{
                config: {
                    time: rowData.duration.rechargeValue,
                    deployId: rowData.deployId,
                    serviceType: 'PALO'
                },
                paymentMethod: this.getCoupons()
            }],
            paymentMethod: []
        };
    }

    /**
     * 获取优惠券列表，没有就返回[]
     * @returns [couponId]
     */
    getCoupons() {
        const items = this.data.get('items') || [];
        const couponId = items[0]?.couponId || '';
        // 不选择优惠券
        if (!couponId) {
            return [];
        }
        return [{
            type: 'coupon',
            values: [couponId]
        }];
    }
}
