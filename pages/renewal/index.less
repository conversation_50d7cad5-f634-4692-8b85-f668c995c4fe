.palorenewal-view .biz-app {
    margin-top: 0;
}

.palo-renewal {
    overflow-y: hidden;
    .page-title-nav {
        position: static;
        font-size: 14px;
    }

    .app-create-page-content {
        height: 100%;
        margin: 0 auto;
        max-width: 1280px;
        min-width: 1040px;
        background-color: #f7f7f9;
        .s-step-content-title, .s-step-content-icon {
            background-color: #f7f7f9;
        }
        .billing-sdk-order-confirm-wrapper {
            width: 100%;
            padding: 0 16px;
            .billing-sdk-order-legend {
                width: 100%;
                background-color: #fff;
                .legend-header {
                    background-color: #fff;
                }
            }
            // 隐藏订单右上角信息
            .charge-wrapper {
                display: none;
            }
        }
    }

    .s-create-page-footer {
        width: 100%;
        height: 80px;
        left: 50%;
        transform: translate(-50%);
    }

    .page-footer-wrapper {
        width: 100%;
        padding: 10px 0;
    }

    .wrapper {
        display: flex;
        .shopping-cart {
            flex: initial;
            .shopping-cart-detail-container {
                flex: initial;
                padding-left: 10px;
            }
            .shopping-cart-item-wrapper {
                padding: 0 10px;
            }
        }
    }

    .step-button {
        height: 40px;
        box-sizing: border-box;
        border-radius: 4px;
        margin: 0 10px;
        &:first-of-type {
            margin: 0 10px 0 0;
        }
    }

    .right-buttons{
        width: 100%;
        display: flex;
        justify-content: flex-end;

        button + button {
            margin-left: 15px;
        }
    }

    .order-item-container {
        .content {
            display: block;
        }

        .item {
            display: block;
            width: 100%;
        }
    }
}

.renewal-info {
    margin-top: 20px;
}

.content-card {
    padding: 24px;
    margin: 16px;
    width: calc(~'100% - 32px');
    min-width: 1280px;
    overflow-x: auto;
    margin-top: 0;
    border-radius: 6px;
    background-color: #fff;

    .title {
        margin-bottom: 24px;

        label {
            font-weight: 500;
            font-size: 16px;
        }
    }

    .detail-form-row {
        margin-top: 0;
        margin-bottom: 16px;

        .detail-form-label {
            .label {
                width: 60px;
            }
        }
    }

    .bes-stretch-models {
        .s-radio {
            align-items: flex-start;
        }
    }
}