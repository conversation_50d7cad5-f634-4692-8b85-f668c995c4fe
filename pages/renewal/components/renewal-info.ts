/**
 * 续费
 */
import { Component } from "san";
import {decorators, html} from '@baiducloud/runtime';
import {Table, Notification, Select} from '@baidu/sui';
import {getDeployId, getRealTime, getAddTime} from '@common/utils/index';
import {ContextService} from '@common/index';
import {PAYTYPE, RechargeYear, RechargeMonth, RechargeType} from '@common/config';

const template = html`
    <div class="renewal-info">
        <s-table 
            columns="{{detailColumns}}"
            datasource="{{dataSource}}"
            loading="{{loading}}"
            >
            <div slot="c-duration">
                <s-select size="small" on-change="itemChange($event,'all')" value="{=row.duration.rechargeType=}">
                    <s-option
                        s-for="item,i in RechargeType"
                        value="{{item.value}}"
                        label="{{item.text}}"
                    ></s-option>
                </s-select>
                <br>
                <s-select size="small"
                    s-if="{{row.duration.rechargeType === 'year'}}"
                    on-change="itemChange($event,'item')"
                    value="{=row.duration.rechargeValue=}"
                >
                    <s-option
                        s-for="item,i in RechargeYear"
                        value="{{item.value}}"
                        label="{{item.text}}"
                    ></s-option>
                </s-select>
                <s-select
                    size="small"
                    s-if="{{row.duration.rechargeType === 'month'}}"
                    on-change="itemChange($event,'item')"
                    value="{=row.duration.rechargeValue=}">
                    <s-option
                        s-for="item,i in RechargeMonth"
                        value="{{item.value}}"
                        label="{{item.text}}"
                    ></s-option>
                </s-select>
            </div>
        </s-table>
    </div>
`;

@decorators.asComponent('@renewal-info')

export default class RenewalInfo extends Component {
    static pageName = 'palorenewalinfo';

    static template = template;

    static components = {
        's-table': Table,
        's-select': Select,
        's-option': Select.Option
    }

    initData () {
        return {
            RechargeYear,
            RechargeMonth,
            RechargeType,
            detailColumns: [
                {name: 'deployId', label: 'ID' },
                {name: 'deployName',label: '产品名称'},
                {name: 'expireTime',label: '到期时间'},
                {
                    name: 'duration',
                    label: '续费时长'
                },
                {name: 'newExpiredTime',label: '续费后到期时间'},
                {name: 'totalExpense',label: '金额'},
            ],
            deployId: getDeployId(),
            dataSource: []
        };
    }

    inited() {
        this.initDetail();
    }

    /**
     * 续费时长改变
     * @param e 
     * @param key 
     */
    itemChange(e: any, key: any) {
        const expireTime = this.data.get('dataSource[0].expireTime');
        if (key === 'all') {
            this.data.set('dataSource[0].duration.rechargeType', e.value);
            if (e.value === 'month') {
                this.data.set('dataSource[0].duration.rechargeValue', 1);
                this.data.set('dataSource[0].newExpiredTime', expireTime === '-' ? '-' : getAddTime(expireTime, 1));
            } else if (e.value === 'year') {
                this.data.set('dataSource[0].duration.rechargeValue', 12);
                this.data.set('dataSource[0].newExpiredTime', expireTime === '-' ? '-' : getAddTime(expireTime, 12));
            }
        } else if (key === 'item') {
            this.data.set('dataSource[0].duration.rechargeValue', e.value);
                this.data.set('dataSource[0].newExpiredTime', expireTime === '-' ? '-' : getAddTime(expireTime, e.value));
        }
        this.setPrice();
    }

    async initDetail() {
        const deployId = this.data.get('deployId');
        if (!deployId) {
            Notification.error('缺少集群ID，请返回列表页重试');
            return;
        }
        this.data.set('loading', true);
        const res = await this.$http.paloPost('paloDeployDetail', { deployId });
        this.setDataSource(res);
    }

    /**
     * 设置table 的datasource
     * @param data 
     */
    setDataSource(data: any) {
        const row = {
            ...data,
            deployId: data.deployId,
            deployName: data.deployName,
            expireTime: data.expireTime ? getRealTime(data.expireTime) : '-',
            duration: {
                rechargeType: 'month',
                rechargeValue: 1
            },
            newExpiredTime: data.newExpiredTime,
            totalExpense: ''
        };
        row.newExpiredTime = data.expireTime ? getAddTime(getRealTime(data.expireTime), row.duration.rechargeValue) : '-';
        this.data.set('dataSource', [row]);
        this.data.set('loading', false);
        this.setPrice();
    }

    async setPrice() {
        const totalExpense = await this.getTotalPrice();
        this.data.set('dataSource[0].totalExpense', totalExpense);
        const rowData = this.data.get('dataSource[0]');
        this.dispatch('renewal-change', rowData);
    }

    async getTotalPrice() {
        const dataRow = this.data.get('dataSource[0]');
        const params = this.getPriceParams(dataRow);
        const res = await this.$http.paloPost('paloOrderGetNewPackagePrice', params);
        return res.total;
    }

    /**
     * 
     * @param dataRow 
     * @returns 返回询价的参数
     */
    getPriceParams(dataRow: any) {
        const duration = dataRow.duration.rechargeValue;
        const modules: any = [];
        dataRow.modules.forEach((item: any) => {
            const module: any = {
                instance_num: item.desireInstanceNum,
                module_type: item.type,
                module_version: item.version,
                slot_type: item.slotType,
                product_name: item.type
            }
            if (item.diskSlotInfo) {
                module.diskSlotInfo = item.diskSlotInfo;
                module.diskSlotInfo.instanceNum = item.desireInstanceNum
            }
            modules.push(module)
        });
        return {
            region: ContextService.getCurrentRegion().id,
            productType: PAYTYPE.PREPAY,
            time: duration,
            modules,
            isOldPackage: dataRow.isOldPackage
        }
    }
    /**
     * 
     * @returns 返回table 的datasource
     */
    getDataSource() {
        return this.data.get('dataSource[0]');
    }
}