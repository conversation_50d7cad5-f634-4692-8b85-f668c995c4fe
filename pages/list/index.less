.palolistpage-view .biz-app {
    margin-top: 0;
}

.palo-list {

    .s-table-row {

        .instance-editor {
            display: none;
        }

        &:hover {

            .instance-editor {
                display: inline-block;
            }
        }
    }

    .s-list-content {
        height: calc(~'100% - 66px');

        .ellipsis {
            max-width: 95%;
            display: inline-block;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; //溢出不换行
        }
    
        .s-table-cell-action {
            a.s-link {
                margin-right: 12px;
            }
    
            .s-button {
                padding: 0;
            }
        }
    
        .s-badge-status-text {
            font-size: 12px;
        }
    
        .table-full-wrap {
            height: 100%;

            .s-table {
                min-height: 300px;
                .s-table-empty {
                    height: 300px;
                    padding-top: 100px;
                }
            }
        }
    }
}

.tag-edit-panel .inline-form .s-form-item:not(:last-child) {
    margin-right: 0;
}

.s-button.btn-refresh {
    width: 32px;
    height: 32px;
    box-sizing: border-box;
}