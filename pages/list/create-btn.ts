import {Component} from 'san';
import {html, decorators} from '@baiducloud/runtime';
import {OutlinedPlus} from '@baidu/sui-icon';
import {Button} from '@baidu/sui';

@decorators.asComponent('@create-btn')
export class CreateBtn extends Component {
    static template = html`
    <template>
        <s-button
            skin="{{skin}}"
            on-click="onClick"
            disabled="{{disabled}}"
            class="create-btn"
        >
            <s-icon-plus
                s-if="hasIcon"
                size="{{16}}"
                class="mr4 flex"
            />
            <slot />
        </s-button>
    </template>
    `;

    static components = {
        's-icon-plus': OutlinedPlus,
        's-button': Button,
    };

    initData() {
        return {
            text: '',
            hasIcon: true,
            skin: 'primary'
        };
    }

    onClick() {
        this.fire('click');
    }
};