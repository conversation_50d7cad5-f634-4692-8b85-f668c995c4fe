/**
 *
 * @file 存算分离列表页
 * 因为现在存算分离和存算分离两个页面对于集群筛选排序搜索操作等支持粒度不一样，暂时采用两套代码的形式
 * 等后续存算分离功能补齐后，改成一套代码
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component} from 'san';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {AppListPage, TipSelect, SearchBox, Empty} from '@baidu/sui-biz';
import {
    Button,
    Table,
    Input,
    Pagination,
    Badge,
    Link,
    Dialog,
    Notification,
    Tooltip,
    Loading,
    Tag
} from '@baidu/sui';
import {OutlinedWarning, OutlinedRefresh, OutlinedPlus} from '@baidu/sui-icon';
import {PAYTYPE, clusterStatus, PayType, computeGroupStatusEnum} from '@common/config';
import {ContextService} from '@common/index';
import {TagSDK} from '@baidu/bce-tag-sdk';
import {TagEditDialog} from '@baiducloud/tag-sdk/san'
import HttpClient from '@baidu/bce-httpclient';
import {isExpired, renderStatus} from '@common/utils/index';
import {CreateBtn} from './create-btn';
import {AllEnum, clusterStatusFilter} from '@common/config/index';
import {pickEmpty} from '@common/utils';
import InstantEditor from '@components/instant-editor';
import './index.less';

const {asPage, withSidebar} = decorators;
const allEnum = AllEnum.toArray();
const template = html`
     <app-list-page pageTitle="{{pageTitle}}" class="palo-list">
         <div slot="pageTitle">
             <h2>集群管理</h2>
         </div>
         <div slot="bulk">
             <create-btn skin="primary" on-click="createCluster"> 创建集群</create-btn>
             <s-button
                disabled="{{batchAddLabelContent}}"
                class="ml8"
                on-click="handleAction({value: 'batchAddLabel'})"
             >批量添加标签</s-button>
         </div>
         <div slot="filter">
            <s-searchbox
                class="cluster-search-box"
                placeholder="{{searchbox.placeholder}}"
                on-keywordTypeChange="onKeywordTypeChange"
                text-datasource="{{searchbox.textDataSource}}"
                value="{= searchbox.keyword =}"
                keyword-type="{= searchbox.keywordType =}"
                datasource="{{searchbox.keywordTypes}}"
                on-search="onSearch"
                width="170"
            />
            <s-button on-click="refresh" class="refresh-button ml8">
                <outlined-refresh />
            </s-button>
         </div>
         <s-table
             columns="{{table.schema}}"
             selection="{{table.selection}}"
             datasource="{{table.datasource}}"
             loading="{{tableLoading}}"
             on-selected-change="onTableRowSelected($event)"
             on-sort="onSort"
             on-filter="onFilter"
         >
            <div slot="c-deployName">
                <div>
                    <a href="{{'#/palo/decoupled-detail?deployId=' + row.deployId + '&version=' + row.version}}">{{row.deployName}}</a>
                    <instant-editor
                        value="{{row.deployName}}"
                        info="{{rowIndex}}"
                        request="{{editName}}"
                        disabled="{{row.desireStatus !== 'Running'}}"
                        check="{{check}}"
                        placeholder="请输入集群名称"
                        desc="支持字母(a-z及A-Z)、数字(0-9),长度小于20个字符"
                    />
                </div>
                <div class="ellipsis">
                    {{row.deployId}}
                </div>
            </div>
             <div slot="c-displayActualStatus">
                {{row.displayActualStatus | filterStatus(row.actualStatus) | raw}}
            </div>
            <div slot="c-productType">
                <span>{{PayType[row.productType]}}</span>
            </div>
            <div slot="c-tag">
                <div s-for="tag in row.tags">
                    {{tag.tagKey}}: {{tag.tagValue}}
                </div>
            </div>
             <div slot="c-action">
                <s-button skin="stringfy" on-click="handleShowTagEdit(row)" class="mr12">编辑标签</s-button>
                <s-button skin="stringfy" on-click="handleRenew(row)" class="mr12" disabled="{{row | formatRenewDisabled}}">续费</s-button>
                <s-button skin="stringfy" on-click="delete(row)" disabled="{{!row.canDelete}}">删除</s-button>
            </div>
            <div slot="empty">
                <s-empty vertical="{{true}}" title="" image="{{image}}">
                    <p slot="desc" s-if="{{isFilter}}">搜索结果为空，更换筛选条件试试</p>
                    <p slot="desc" s-else>当前地域还未创建集群，您可以切换其他地域查看。</p>
                    <s-button slot="action" skin="stringfy" on-click="createCluster" class="ml4">
                        <s-icon-plus class="button-icon mr4" />创建集群
                    </s-button>
                </s-empty>
            </div>
         </s-table>
         <s-pagination
             slot="pager"
             layout="{{'total, pageSize, pager'}}"
             total="{{pager.totalCount}}"
             page="{{pager.pageNo}}"
             pageSize="{=pager.pageSize=}"
             on-pagerChange="onPagerChange"
             on-pagerSizeChange="onPagerSizeChange"
         />
         <s-tag-edit-dialog
            s-if="{{tagEditing}}"
            instances="{{[{tags: currentCluster ? currentCluster.tags : []}]}}"
            sdk="{{tagSDK}}"
            on-cancel="handleCancelTag"
            submit-handler="{{handleSubmitTag()}}"
         />
     </app-list-page>
 `;
 
 // 注册侧边栏服务
 @withSidebar({active: 'decoupledlist'})
 // 注册路由服务
 @asPage('/palo/decoupledlist')
 export default class PaloStorageList extends Component {
    // 用于设置样式
    static pageName = 'palolistpage';

    static template = template;

    static filters ={
        filterStatus(displayActualStatus: string, status: string) {
            const item = clusterStatusFilter.fromValue(status);
            return `<span class="status ${item?.classname}">${displayActualStatus}</span>`;
        },
        formatRenewDisabled(row: any) {
            return !row.expireTime || [clusterStatusFilter.Initializing, clusterStatusFilter.partial_Initializing].includes(row.actualStatus);
        }
    }

    static components = {
        'app-list-page': AppListPage,
        's-button': Button,
        's-table': Table,
        's-textbox': Input,
        's-pagination': Pagination,
        's-input-search': Input.Search,
        's-select': TipSelect,
        's-option': TipSelect.Option,
        's-badge': Badge,
        's-link': Link,
        's-tooltip': Tooltip,
        's-warning': OutlinedWarning,
        's-loading': Loading,
        's-tag-edit-dialog':TagEditDialog,
        's-tag': Tag,
        'create-btn': CreateBtn,
        'outlined-refresh': OutlinedRefresh,
        's-searchbox': SearchBox,
        's-empty': Empty,
        's-icon-plus': OutlinedPlus,
        'instant-editor': InstantEditor
    }
 
     initData() {
         return {
             pageTitle: '集群管理',
             clusterStatus,
             PayType,
             PAYTYPE,
             actionName: '',
             tagEditing: false,
             tagSDK: new TagSDK({
                client: new HttpClient({}, ContextService),
                context: ContextService
             }),
             currentCluster: null,
             searchbox: {
                keyword: '',
                keywordType: ['deployName'],
                keywordTypes: [
                    {
                        value: 'deployName',
                        text: '集群名称'
                    },
                    {
                        value: 'deployId',
                        text: '集群ID'
                    }
                ],
                allTextDataSource: []
             },
             table: {
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex: []
                 },
                 schema: [
                     {
                         name: 'deployName',
                         label: '集群名称/ID',
                         width: '15%',
                         minWidth: 150,
                     },
                     {
                        name: 'displayActualStatus',
                        label: '状态',
                        width: 120,
                        filter: {
                            options: [
                                ...allEnum,
                                ...clusterStatusFilter.toArray()
                            ],
                            value: allEnum[0].value
                        },
                     },
                     {
                        name: 'cuNums',
                        label: '计算资源(CU)',
                        width: 120,
                        sortable: true,
                     },
                    //  {
                    //     name: 'productType',
                    //     label: '付费方式',
                    //     width: 120,
                    //     filter: {
                    //         options: [
                    //             ...allEnum,
                    //             ...PAYTYPE.toArray()
                    //         ],
                    //         value: allEnum[0].value
                    //     },
                    //  },
                     {
                        name: 'createTime',
                        label: '创建时间',
                        width: '10%',
                        sortable: true,
                     },
                     {
                         name: 'expireTime',
                         label: '到期时间',
                         width: '10%',
                         sortable: true,
                         render: this.renderExpireTime
                     },
                     {
                         name: 'tag',
                         label: '标签',
                         width: '15%'
                     },
                     {
                         name: 'action',
                         label: '操作',
                         width: '15%'
                     }
                 ],
                 datasource: [],
                 loading: false
             },
             pager: {
                 pageSize: 10,
                 pageNo: 1,
                 totalCount: 0,
             },
             changeContent: '请选择实例',
             cancelChangeContent: '请选择实例',
             batchAddLabelContent: '请选择实例',
             tableLoading: false,
             selectedItem: {},
             selectedItems: [],
             order: 'desc',
             orderBy: 'createTime',
             editName: this.editName.bind(this),
             check: this.check.bind(this),
             productType: '',
             displayActualStatus: ''
        };
    }

    static computed = {
        image() {
            const isFilter = this.data.get('isFilter');
            if (isFilter) {
                return require('@static/img/noResult.svg');
            }
            else {
                return require('@static/img/empty2.svg');
            }
        }
    }

    attached() {
        this.refresh();
        this.getPALOTags();
    }
     
    async onFilter(event: {field: any; filter: any}) {
        const {field, filter} = event;
        this.data.set(`${field.name}`, filter.value);
        await this.refresh();
    }
     
         // 获取标签列表
    async getPALOTags() {
        const result = await this.$http.paloPost('getSearchTagList', {
            serviceType: 'PALO',
            region: [this.$context.getCurrentRegionId()]
        });
        let allTextDataSource = [{text: '所有值', value: '所有值'}];
        result.forEach(item => {
            const data = {
                text: item.tagValue || '空值',
                value: item.tagValue|| ''
            };
            const index = _.findIndex(allTextDataSource, i => i.value === data.value);
            index === -1 && allTextDataSource.push(data);
        });

        this.data.set('searchbox.allTextDataSource', allTextDataSource.filter(j => j.text !== '空值'));

        let tags = _.groupBy(result, 'tagKey');
        _.each(tags, (tag, key) => {
            tags[key] = _.map(tag, item => {
                return {name: item.tagValue || '空值', value: item.tagValue};
            });
            // tags[key].unshift({name: '所有值', value: '@@@'});
        });
        let tagKeys = _.map(_.keys(tags), key => {
            if (key === '@@@') {
                return {text: '(全部)', value: '@@@'};
            }
            else if (key === '') {
                return {text: '(无标签)', value: ''};
            }
            return {
                text: key,
                value: key,
                textDataSource: tags[key].map(i => ({
                    text: i.name,
                    value: i.name
                }))
            };
        });
        // tagKeys.unshift({text: '(无标签)', value: '', textDataSource: result.map(i => ({
        //     text: i.name,
        //     value: i.name
        // }))});
        const allTagChildren = result.map(i => ({
            text: i.name,
            value: i.name
        })).filter(j => j.name !== '空值');


        tagKeys.unshift({text: '(全部)', value: '@@@', textDataSource: allTagChildren});
        this.data.push('searchbox.keywordTypes', {
            text: '标签',
            value: 'tag',
            children: tagKeys
        });
        return tags;
    }
     
         // search-box 的 keyword type改变时调用
    onKeywordTypeChange({value}) {
        let textDataSource = [];
        if (Array.isArray(value) && value.indexOf('tag') > -1) {
            let keywordTypes = this.data.get('searchbox.keywordTypes');
            let tags = _.find(keywordTypes, item => item.value === 'tag').children;
            if (value[1] === '@@@') {
                textDataSource = this.data.get('searchbox.allTextDataSource');
            }
            else if (value[1] === '') {
                textDataSource = [{text: '空值', value: ''}];
            }
            else {
                textDataSource = _.find(tags, tag => tag.text === value[1]).textDataSource;
            }
            this.data.set('searchbox.textDataSource', textDataSource);
        }
        else if (value.indexOf('tag') === -1) {
            this.data.merge('searchbox', {
                keywordType: [value[0]],
                keyword: ''
            });
            this.data.set('searchbox.textDataSource', textDataSource);
            this.refresh();
        }
    }

    // 生成搜索关键字
    createKeywordParams() {
        let {keyword, keywordType} = this.data.get('searchbox');
        if (keywordType.length > 1) {
            keyword = keyword === '所有值' ? '@@@' : keyword === '空值' ? '' : keyword;
        }
        keywordType = keywordType[1];
        return {
            keywordType,
            keyword
        };
    }

    renderExpireTime (item: { expireTime: any; }, key: any, col: any, rowIndex: any, colIndex: any, data: any) {
        return `${item.expireTime || '-'}`;
    }

    onTableRowSelected (e: { value: { selectedItems: any[]; selectedIndex: string | any[]; }; }) {
        this.data.set('actionName', '');
        const item =  e.value.selectedItems[0];
        this.data.set('selectedItems', e.value.selectedItems)
        this.data.set('selectedItem', item);
        this.data.set('changeContent', '');
        this.data.set('cancelChangeContent', '');
        this.data.set('batchAddLabelContent', '')
        if (e.value.selectedIndex.length > 1) {
            this.data.set('changeContent', '暂不支持批量操作');
            this.data.set('cancelChangeContent', '暂不支持批量操作');
            return;
        } else if (!item) {
            this.data.set('changeContent', '请选择实例');
            this.data.set('cancelChangeContent', '请选择实例');
            this.data.set('batchAddLabelContent', '请选择实例')
            return;
        }

        if (item.orderStatus) {
            this.data.set('changeContent', '实例存在其他订单，请处理后重试');
        } else if (item.actualStatus !== 'Running') {
            this.data.set('changeContent', '该实例当前状态无法计费变更');
        }

        if (item.actualStatus === 'Running'
            && (item.orderStatus === 'to_postpay' || item.orderStatus === 'shift_charge')
        ) {
            this.data.set('cancelChangeContent', '');
        } else {
            this.data.set('cancelChangeContent', '该实例当前状态无法进行取消计费变更');
        }
    };

    onPagerChange(e: { value: { pageSize: number; page: number; } | undefined; }) {
        this.refresh(e.value);
    };

    onPagerSizeChange(e: { value: { pageSize: number; page: number; } | undefined; }) {
        this.refresh(e.value);
    };

    createCluster() {
        redirect('/palo/#/palo/decoupled-create');
    }
     
    /**
     * 搜索，重写是为了支持搜索后自动刷新定时器重置
    */
    onSearch(args?: {value: string}): void {
        this.data.set('pager.page', 1);
        this.refresh();
    }

    async refresh(e?: { pageSize: number; page: number; }) {
        const {pager, searchbox, displayActualStatus, productType, orderBy, order} = this.data.get('');
        this.data.set('isFilter', searchbox.keyword !== '' || productType !== '' || displayActualStatus !== '');

        let params = {
            pageSize: e?.pageSize || pager.pageSize,
            pageNo: e?.page || pager.pageNo,
            region: ServiceFactory.resolve('$context').getCurrentRegion().id,
            orderBy,
            order,
            productType,
            displayActualStatus,
        };
        if (searchbox.keywordType[0] !== 'tag') {
            params = pickEmpty({
                ...params,
                searchKey: searchbox.keywordType[0],
                searchValue: searchbox.keyword,
            })
        }
        else {
            const {keywordType, keyword} = this.createKeywordParams();
            params = pickEmpty({
                ...params,
                keywordType: 'tag',
                subKeywordType: keywordType,
                keyword,
            })
        }
        try {
             this.data.set('tableLoading', true);
             const {result, totalCount} = await this.$http.paloPost('paloSeperateList', params);
            this.data.set('table.datasource', result.map(item => ({...item, canDelete: [
                clusterStatusFilter.Running,
                clusterStatusFilter.partial_Running,
                clusterStatusFilter.Audit_stopped,
                clusterStatusFilter.partial_Audit_stopped].includes(item.actualStatus)  && item.productType === 'postpay'})) || []);
            this.data.set('pager.totalCount', totalCount || 0);
         }
         catch (e) {
             this.data.set('table.datasource', []);
             this.data.set('pager.totalCount', 0);
         } finally {
             this.data.set('tableLoading', false);
             // 刷新列表之后清除选中并设置操作actionName
            this.nextTick(() => {
                this.data.set('table.selection.selectedIndex', []);
                this.data.set('actionName', '');
                this.data.set('changeContent', '请选择实例');
                this.data.set('cancelChangeContent', '请选择实例');
                this.data.set('batchAddLabelContent', '请选择实例')
                this.data.set('selectedItems', [])
            });
         }
     }

     handleShowTagEdit(row: any[]) {
        this.data.set('tagEditing', true)
        this.data.set('currentCluster', row)
     }
     handleCancelTag() {
        this.data.set('tagEditing', false)
        this.data.set('currentCluster', null)
        this.data.set('actionName', '')
     }
     handleSubmitTag() {
        return async (tags: any[]) => {
            const currentCluster = this.data.get('currentCluster')
            const selectedItems = this.data.get('selectedItems')
            if (currentCluster) {
                try {
                    const resourceAccountIdTag = currentCluster.tags?.find((item: any) => item.tagKey === '资源账户ID')
                    if(resourceAccountIdTag) {
                        const resourceAccountIdTags = tags.filter((item: any) => item.tagKey === '资源账户ID')
                        if(resourceAccountIdTags.length === 0) return Promise.reject('不允许删除『资源账户ID』标签')
                        if(resourceAccountIdTags.length > 1) return Promise.reject('不允许新增『资源账户ID』标签')
                        if(resourceAccountIdTags[0].tagValue !== resourceAccountIdTag.tagValue) return Promise.reject('不允许修改『资源账户ID』标签')
                    }else {
                        const resourceAccountIdTags = tags.filter((item: any) => item.tagKey === '资源账户ID') 
                        if(resourceAccountIdTags.length > 0) return Promise.reject('不允许添加『资源账户ID』标签')
                    }
                    const params = {
                        region: ServiceFactory.resolve('$context').getCurrentRegion().id,
                        deployId: currentCluster.deployId,
                        tags
                    }
                    await this.$http.paloPost('paloUpdateTags', params)
                    Notification.success('编辑标签成功');
                    this.handleCancelTag()
                    this.refresh()
                } catch(e) {
                    Notification.error('编辑标签失败');
                }
            }
            else {
                try {
                    if(tags.length === 0) return Promise.reject('请至少输入一组标签')
                    const resourceAccountIdTags = tags.filter((item: any) => item.tagKey === '资源账户ID')
                    if(resourceAccountIdTags.length > 0) return Promise.reject('不允许添加『资源账户ID』标签')
                    const params = {
                        region: ServiceFactory.resolve('$context').getCurrentRegion().id,
                        deployIdList: selectedItems.map((item: any) => item.deployId),
                        insertTags: tags
                    }
                    await this.$http.paloPost('paloBatchInsertTags', params)
                    Notification.success('添加标签成功')
                    this.handleCancelTag()
                    this.refresh()
                } catch(e) {
                    Notification.error('添加标签失败')
                }
            }
        }
       
     }

     async handleAction(e: any) {
        const value = e.value;
        if (value === 'batchAddLabel') {
            this.data.set('tagEditing', true) 
            this.data.set('currentCluster', null)
        }
    }
     
    // 名称输入校验
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length >= 20) {
            return callback('不能超过20个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }
     
    // 名称编辑
    async editName(name: string, rowIndex: number) {
        const item = this.data.get(`table.datasource[${rowIndex}]`);
        await this.$http.paloPost('paloSeperateEditName', {
            deployId: item.deployId,
            editNewName: name
        })
        Notification.success('修改成功');
        this.data.set(`table.datasource[${rowIndex}].deployName`, name);
    }
    
    async onSort(event: {value: {order: string; orderBy: string}}) {
        const {orderBy, order} = event.value;
        this.data.set('order', order);
        this.data.set('orderBy', orderBy);
        this.refresh();
    }
     
     async delete(deploy: any) {
        try {
            const {computeGroupItems} = await this.$http.paloPost('paloComputeGroupList', {
                deployId: deploy.deployId,
                pageNo: 1,
                pageSize: 1000,
            });
            const canDelete = computeGroupItems.every((item: any) => [computeGroupStatusEnum.Running, computeGroupStatusEnum.Audit_stopped].includes(item.actualStatus));
            if (!canDelete) {
                Notification.error('存在无法删除的计算组');
                return;
            }
            await Dialog.confirm({
                content: `删除后不可恢复，您确定删除集群${deploy.deployName}吗`,
                title: '删除集群',
                onOk: async () => {
                    try {
                        await this.$http.paloPost('paloSeperateDeployDelete', {
                            deployId: deploy.deployId,
                            region: this.$context.getCurrentRegionId()
                        });
                        Notification.success('操作成功');
                        this.refresh();
                    } catch (e) {
                        Notification.error('操作失败，请重试！');
                    }
                }
            });
        } catch (e) {}
     }
     
     // region 切换
    onRegionChange(target: {id: string}) {
        if (target.id === 'bj') {
            redirect('#/palo/decoupledlist');
        } else {
            window.location.href = '#/palo/list';
            window.location.reload();
        }
    }

    // 续费
    handleRenew(row: any) {
        window.open(`/billing/renew?serviceType=PALO&region=${
            this.$context.getCurrentRegionId()
        }&instanceIds=${
            row.deployId + '-Cluster'
        }&confirmV2Url=%2Fapi%2Fpalo%2Frenew%2Fconfirm&backUrl=%2Fpalo%2F%23%2Fpalo%2Fdecoupledlist`, '_blank');
    }
}
 