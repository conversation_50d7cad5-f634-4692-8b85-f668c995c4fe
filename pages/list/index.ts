/**
 * palo list
 *
 * @file 集群列表页
 * <AUTHOR>
 */

import _ from 'lodash';
import {Component, defineComponent} from 'san';
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {AppListPage, TipSelect} from '@baidu/sui-biz';
import {
    Button,
    Table,
    Input,
    Pagination,
    Badge,
    Link,
    Dialog,
    Notification,
    Tooltip,
    Loading,
    Tag
} from '@baidu/sui';
import {OutlinedWarning, OutlinedRefresh} from '@baidu/sui-icon';
import {PAYTYPE, clusterStatus, PayType} from '@common/config';
import {BOXER1_MAX_ID} from '@common/config/constant';
import {ContextService} from '@common/index';
import {TagSDK} from '@baidu/bce-tag-sdk'
import {TagEditDialog} from '@baiducloud/tag-sdk/san'
import HttpClient from '@baidu/bce-httpclient';
import {renderStatus} from '@common/utils/index';
import {CreateBtn} from './create-btn';

import './index.less';

const {asPage, withSidebar} = decorators;

const tempalte = html`
     <app-list-page pageTitle="{{pageTitle}}" class="{{serviceActivated ? 'palo-list' : 'palo-list-unactivated palo-list'}}">
         <div slot="pageTitle">
             <h2>集群管理</h2>
         </div>
         <div slot="bulk"  style="font-size: 0;">
             <create-btn skin="primary" on-click="createCluster" style="margin-right: 8px;"> 创建集群</create-btn>
             <s-button
                disabled="{{changeContent.length}}"
                class="ml8"
                on-click="handleAction({value: 'alter'})"
            >计费变更</s-button>
            <s-button
                disabled="{{cancelChangeContent.length}}"
                class="ml8"
                on-click="handleAction({value: 'cancelAlter'})"
            >取消计费变更</s-button>
            <s-button
                disabled="{{batchAddLabelContent}}"
                class="ml8"
                on-click="handleAction({value: 'batchAddLabel'})"
            >批量添加标签</s-button>
         </div>
         <div slot="filter">
            <s-button on-click="refresh" class="mr8">
                <outlined-refresh />
            </s-button>
         </div>
         <s-table
             columns="{{table.schema}}"
             datasource="{{table.datasource}}"
             selection="{{table.selection}}"
             loading="{{tableLoading}}"
             on-selected-change="onTableRowSelected($event)">
            <div slot="c-deployName">
                <div>
                    <a data-command="TagEdit"
                        href="{{'#/palo/detail?deployId='+ row.deployId + '&version=' + row.version}}}">{{row.deployName}}</a>
                    <s-tag skin="danger" s-if="!row.highAvailability">非高可用</s-tag>
                </div>
                <div class="ellipsis">
                    {{row.deployId}}
                </div>
            </div>
             <div slot="c-displayActualStatus">
                {{row.displayActualStatus | filterStatus(row.actualStatus) | raw}}
            </div>
            <div slot="c-productType">
                <span>{{PayType[row.productType]}}</span>
                <s-tooltip
                    s-if="{{row.productType === PAYTYPE.PREPAY && row.orderStatus === 'to_postpay'}}"
                    placement="top"
                >
                    <s-warning width="20" color="#999" />
                    <div slot="content" style="width: 440px;">
                        该实例已开通计费变更-包年包月转按量付费，将会在到期后转为按量付费资源，请关注！
                        <br>
                        如需进行续费、升配等操作，请先取消计费变更，谢谢！
                    </div>
                </s-tooltip>
            </div>
            <div slot="c-tag">
                <div s-for="tag in row.tags">
                    {{tag.tagKey}}: {{tag.tagValue}}
                </div>
            </div>
             <div slot="c-action" style="font-size: 0;">
                <template s-if="row.actualStatus !== 'Running' || ((item.deployId - 0) <= BOXER1_MAX_ID)">
                    <s-link
                        skin="primary"
                        disabled>配置变更</s-link>
                </template>
                <template s-else>
                    <s-link
                        skin="primary"
                        href="#/palo/stretch?deployId={{row.deployId}}">配置变更</s-link>
                </template>

                <template s-if="row.productType === PAYTYPE.POSTPAY">
                    <s-link
                        skin="primary"
                        disabled>续费</s-link>
                </template>
                <template s-else>
                    <s-link
                        href="#/palo/renewal?deployId={{row.deployId}}"
                        skin="primary">续费</s-link>
                </template>
                <s-button skin="stringfy" on-click="handleShowTagEdit(row)">编辑标签</s-button>
            </div>
         </s-table>
         <s-pagination
             slot="pager"
             layout="{{'total, pageSize, pager'}}"
             total="{{pager.totalCount}}"
             page="{{pager.pageNo}}"
             pageSize="{=pager.pageSize=}"
             on-pagerChange="onPagerChange"
             on-pagerSizeChange="onPagerSizeChange"
         />
         <s-tag-edit-dialog
            s-if="{{tagEditing}}"
            instances="{{[{tags: currentCluster ? currentCluster.tags : []}]}}"
            sdk="{{tagSDK}}"
            on-cancel="handleCancelTag"
            submit-handler="{{handleSubmitTag()}}"
         />
     </app-list-page>
 `;


// 注册侧边栏服务
@withSidebar({active: 'list'})
// 注册路由服务
@asPage('/palo/list')
export default class PaloList extends Component {
    // 用于设置样式
    static pageName = 'palolistpage';

    static template = tempalte;

    static filters = {
        filterStatus(displayActualStatus: string, status: string) {
            return renderStatus(displayActualStatus, status);
        }
    }

    static components = {
        'app-list-page': AppListPage,
        's-page': AppListPage,
        's-button': Button,
        's-table': Table,
        's-textbox': Input,
        's-pagination': Pagination,
        's-input-search': Input.Search,
        's-select': TipSelect,
        's-option': TipSelect.Option,
        's-badge': Badge,
        's-link': Link,
        's-tooltip': Tooltip,
        's-warning': OutlinedWarning,
        's-loading': Loading,
        's-tag-edit-dialog': TagEditDialog,
        's-tag': Tag,
        'create-btn': CreateBtn,
        'outlined-refresh': OutlinedRefresh
    }

    initData() {
        return {
            BOXER1_MAX_ID,
            pageTitle: '集群管理',
            clusterStatus,
            PayType,
            PAYTYPE,
            actionName: '',
            tagEditing: false,
            tagSDK: new TagSDK({
                client: new HttpClient({}, ContextService),
                context: ContextService
            }),
            currentCluster: null,
            table: {
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex: []
                },
                schema: [
                    {
                        name: 'deployName',
                        label: '集群名称/ID',
                        width: '15%',
                        minWidth: 150,
                    },
                    {
                        name: 'createTime',
                        label: '创建时间',
                        width: '10%'
                    },
                    {
                        name: 'displayActualStatus',
                        label: '状态',
                        width: 120
                    },
                    {
                        name: 'version',
                        label: '内核版本',
                        width: 90
                    },
                    {
                        name: 'productType',
                        label: '付费方式',
                        width: '8%'
                    },
                    {
                        name: 'runningTime',
                        label: '运行时间',
                        width: '10%'
                    },
                    {
                        name: 'expireTime',
                        label: '到期时间',
                        width: '10%',
                        render: this.renderExpireTime
                    },
                    {
                        name: 'tag',
                        label: '标签',
                        width: '15%'
                    },
                    {
                        name: 'action',
                        label: '操作',
                        width: '15%'
                    }
                ],
                datasource: [],
                loading: false
            },
            selectedItem: {},
            selectedItems: [],
            pager: {
                pageSize: 10,
                pageNo: 1,
                totalCount: 0,
            },
            changeContent: '请选择实例',
            cancelChangeContent: '请选择实例',
            batchAddLabelContent: '请选择实例',
            tableLoading: false
        };
    }

    attached() {
        this.refresh();
    }

    renderDeployName(item: {deployId: any; deployName: any;}, key: any, col: any, rowIndex: any, colIndex: any, data: any) {
        return `<a data-command="TagEdit"
            href="#/palo/detail?deployId=${item.deployId}">${item.deployName}</a>
            <s-tag skin="danger">非高可用</s-tag>
        <br>
        <span class="ellipsis">${item.deployId}</span>
        `;
    }

    renderExpireTime(item: {expireTime: any;}, key: any, col: any, rowIndex: any, colIndex: any, data: any) {
        return `${item.expireTime || '-'}`;
    }

    onTableRowSelected(e: {value: {selectedItems: any[]; selectedIndex: string | any[];};}) {
        this.data.set('actionName', '');
        const item = e.value.selectedItems[0];
        this.data.set('selectedItems', e.value.selectedItems)
        this.data.set('selectedItem', item);
        this.data.set('changeContent', '');
        this.data.set('cancelChangeContent', '');
        this.data.set('batchAddLabelContent', '')
        if (e.value.selectedIndex.length > 1) {
            this.data.set('changeContent', '暂不支持批量操作');
            this.data.set('cancelChangeContent', '暂不支持批量操作');
            return;
        } else if (!item) {
            this.data.set('changeContent', '请选择实例');
            this.data.set('cancelChangeContent', '请选择实例');
            this.data.set('batchAddLabelContent', '请选择实例')
            return;
        }

        if (item.orderStatus) {
            this.data.set('changeContent', '实例存在其他订单，请处理后重试');
        } else if (item.actualStatus !== 'Running') {
            this.data.set('changeContent', '该实例当前状态无法计费变更');
        }

        if (item.actualStatus === 'Running'
            && (item.orderStatus === 'to_postpay' || item.orderStatus === 'shift_charge')
        ) {
            this.data.set('cancelChangeContent', '');
        } else {
            this.data.set('cancelChangeContent', '该实例当前状态无法进行取消计费变更');
        }
    };

    onPagerChange(e: {value: {pageSize: number; page: number;} | undefined;}) {
        this.refresh(e.value);
    };

    onPagerSizeChange(e: {value: {pageSize: number; page: number;} | undefined;}) {
        this.refresh(e.value);
    };

    async handleAction(e: any) {
        const value = e.value;
        const selectedItem = this.data.get('selectedItem');
        if (value === 'alter') {
            let base = '/billing/#/billing/alter/productTypeList~serviceType=PALO&';
            let instanceIds = `instanceIds=${selectedItem.deployId}`;
            let type = `type=${selectedItem.productType === PAYTYPE.PREPAY ? 'TO_POSTPAY' : 'TO_PREPAY'}`;
            let region = `region=${ContextService.getCurrentRegion().id}`;
            let api = 'confirmV2Url=/api/palo/change/confirm';
            let url = `${base}${instanceIds}&${type}&${region}&${api}`;
            redirect(url);
        } else if (value === 'cancelAlter') {
            Dialog.confirm({
                showIcon: false,
                content: defineComponent({
                    template: `<div>
                            确认取消计费变更？<br/>
                            您已开通计费变更-包年包月转按量付费功能。<br/>
                            实例将会在到期后自动转换为按量付费的计费方式。
                        </div>`}),
                title: '取消计费变更',
                onOk: async () => {
                    const params = {
                        serviceType: 'PALO',
                        deployId: selectedItem.deployId,
                        orderId: selectedItem.orderId
                    };
                    try {
                        await this.$http.paloPost('cancelAlterProductType', params);
                        Notification.success('取消计费变更成功');
                    } catch (e) {
                        Notification.error('取消计费变更失败');
                    } finally {
                        this.refresh();
                    }
                }
            });
        } else if (value === 'batchAddLabel') {
            this.data.set('tagEditing', true)
            this.data.set('currentCluster', null)
        }
    }


    createCluster() {
        redirect('/palo/#/palo/create');
    }

    async refresh(e?: {pageSize: number; page: number;}) {
        const pager = this.data.get('pager');
        const params = {
            pageSize: e?.pageSize || pager.pageSize,
            pageNo: e?.page || pager.pageNo,
            region: ServiceFactory.resolve('$context').getCurrentRegion().id,
            orderBy: 'deployId',
            order: 'desc',
        }
        try {
            this.data.set('tableLoading', true);
            const {result, totalCount} = await this.$http.paloPost('paloDeployList', params);
            this.data.set('table.datasource', result || []);
            this.data.set('pager.totalCount', totalCount);
        }
        catch (e) {
            this.data.set('table.datasource', []);
            this.data.set('pager.totalCount', 0);
        } finally {
            this.data.set('tableLoading', false);
            // 刷新列表之后清除选中并设置操作actionName
            this.nextTick(() => {
                this.data.set('table.selection.selectedIndex', []);
                this.data.set('actionName', '');
                this.data.set('changeContent', '请选择实例');
                this.data.set('cancelChangeContent', '请选择实例');
                this.data.set('batchAddLabelContent', '请选择实例')
                this.data.set('selectedItems', [])
            });
        }
    }

    handleShowTagEdit(row: any[]) {
        this.data.set('tagEditing', true)
        this.data.set('currentCluster', row)
    }
    handleCancelTag() {
        this.data.set('tagEditing', false)
        this.data.set('currentCluster', null)
        this.data.set('actionName', '')
    }
    handleSubmitTag() {
        return async (tags: any[]) => {
            const currentCluster = this.data.get('currentCluster')
            const selectedItems = this.data.get('selectedItems')
            if (currentCluster) {
                try {
                    const resourceAccountIdTag = currentCluster.tags?.find((item: any) => item.tagKey === '资源账户ID')
                    if (resourceAccountIdTag) {
                        const resourceAccountIdTags = tags.filter((item: any) => item.tagKey === '资源账户ID')
                        if (resourceAccountIdTags.length === 0) return Promise.reject('不允许删除『资源账户ID』标签')
                        if (resourceAccountIdTags.length > 1) return Promise.reject('不允许新增『资源账户ID』标签')
                        if (resourceAccountIdTags[0].tagValue !== resourceAccountIdTag.tagValue) return Promise.reject('不允许修改『资源账户ID』标签')
                    } else {
                        const resourceAccountIdTags = tags.filter((item: any) => item.tagKey === '资源账户ID')
                        if (resourceAccountIdTags.length > 0) return Promise.reject('不允许添加『资源账户ID』标签')
                    }
                    const params = {
                        region: ServiceFactory.resolve('$context').getCurrentRegion().id,
                        deployId: currentCluster.deployId,
                        tags
                    }
                    await this.$http.paloPost('paloUpdateTags', params)
                    Notification.success('编辑标签成功');
                    this.handleCancelTag()
                    this.refresh()
                } catch (e) {
                    Notification.error('编辑标签失败');
                }
            }
            else {
                try {
                    if (tags.length === 0) return Promise.reject('请至少输入一组标签')
                    const resourceAccountIdTags = tags.filter((item: any) => item.tagKey === '资源账户ID')
                    if (resourceAccountIdTags.length > 0) return Promise.reject('不允许添加『资源账户ID』标签')
                    const params = {
                        region: ServiceFactory.resolve('$context').getCurrentRegion().id,
                        deployIdList: selectedItems.map((item: any) => item.deployId),
                        insertTags: tags
                    }
                    await this.$http.paloPost('paloBatchInsertTags', params)
                    Notification.success('添加标签成功')
                    this.handleCancelTag()
                    this.refresh()
                } catch (e) {
                    Notification.error('添加标签失败')
                }
            }
        }

    }

    // region 切换
    onRegionChange() {
        window.location.reload();
    }
}
