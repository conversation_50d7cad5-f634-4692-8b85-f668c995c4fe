Global:
  version: 2.0

Default:
  profile: [master]

Profiles:
  - profile:
    name: master
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
    build:
      command: sh webbuild_new.sh
    artifacts:
      release: true

  - profile:
    name: change
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.10.0
        - jdk: 17.0.6
        - maven: 3.3.9
    build:
      command: sh webbuild_new.sh
    artifacts:
      release: true

  - profile:
    name: xs_master
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.10.0
        - jdk: 17.0.6
        - maven: 3.3.9
    build:
      command: sh webbuild_new.sh
    artifacts:
      release: true
