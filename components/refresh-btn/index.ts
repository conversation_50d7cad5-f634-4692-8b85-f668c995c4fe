/**
 * 带有动态的刷新按钮
 *
 * @file refresh-btn
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Loading} from '@baidu/sui';
import {OutlinedRefresh} from '@baidu/sui-icon';

export default class extends Component {
    static template = html`
    <template>
        <s-button on-click="refresh" class="ml8">
            <outlined-refresh s-if="{{!refreshing}}"/>
            <s-loading
                s-else
                class="mt3"
                size="small"
                loading="{{true}}"
            />
        </s-button>
    </template>
    `;

    static components = {
        's-button': Button,
        's-loading': Loading,
        'outlined-refresh': OutlinedRefresh
    };

    initData() {
        return {
            refreshing: false
        };
    }

    refresh(e: Event) {
        this.fire('refresh', e);
    }
}