/**
* 监控表格
* index.less
*/

@titleColor: #151b26;

.palo-chart {
    background: #FFFFFF;
    border: 1px solid rgba(232,233,235,1);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;

    .bcm-chart-panel {
        width: 100%;
        min-width: 300px;
        padding: 0;
        margin-bottom: 0;
        border: none;

        h1 {
            font-size: 14px;
            color: @titleColor;
            line-height: 22px;
            font-weight: 500;
        }

        .detail-title-slot {
            display: none;
        }
    }
}

.chart-1-per-line {
    width: calc(~"100% - 20px");
    max-width: 1000px;
}

.chart-2-per-line {
    width: calc(~"(100% - 16px) / 2");
}

.chart-3-per-line {
    width: calc(~"(100% - 32px) / 3");
}

.chart-4-per-line {
    width: calc(~"(100% - 48px) / 4");
    // min-width: 300px;
}

.bcm-detail {
    background: rgba(7, 12, 20, .5);
    
    .bcm-detail-main {
        background: #FFFFFF;
        border-radius: 6px;
    }

    .bcm-detail-main-header {
        height: auto;
        padding: 24px 24px 0 24px;
    }

    .bcm-detail-main-header-title {
        font-size: 16px;
        font-weight: bold;
        line-height: auto;
    }

    .bcm-detail-main-header-close {
        line-height: 20px;
    }

    .title-select {
        display: none;
    }

    .bcm-detail-main-func-item-right .s-checkbox-input {
        vertical-align: -7px !important;
    }

    .bcm-detail-main-func {
        padding: 20px 24px 16px;
    }

    .bcm-detail-main-header {
        border-bottom: none;
    }

    .bcm-detail-main-chart {
        padding: 0 24px;
    }

    .bcm-detail-main-table {
        border-top: 1px solid #e8e9eb;
        padding: 24px 0;
        margin: 0 24px;
    }

    .filter-item {
        clear: both;
        padding: 8px 0;
    }
}

.bcm-detail-main {
    max-width: 1040px;
    max-height: 660px;
    background-color: #fff;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    overflow-y: auto;
    left: 20%;
}
