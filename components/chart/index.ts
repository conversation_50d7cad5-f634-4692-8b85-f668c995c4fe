/**
 * 监控的表格
 *
 * @file index.ts
 * <AUTHOR>
 */
import _ from 'lodash';
import m from 'moment';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {BcmChartPanel} from '@baidu/bce-bcm-sdk-san';
import {BcmSDK} from '@baidu/bce-bcm-sdk';
import HttpClient from '@baiducloud/httpclient';
import {SCOPE} from '@common/config/constant';
import {formatUtcTime} from '@common/utils';
import {DETAIL_TYPE} from '@pages/detail/components/config';
import './index.less';

// 监控趋势图小图默认配置 类型
export type TIME_FILTER_TYPE = '1h' | '6h' | '1d' | '7d' | '14d' | '40d';

// 监控趋势图小图默认配置
export const MONITOR_DEFAULT_PERIOD = {
    '1h': 60,
    '3h': 180,
    '6h': 300,
    '1d': 1200,
    '7d': 8400,
    '30d': 36000
};

export default class Chart extends Component {
    static template = html`
    <div class="palo-chart chart-{{numPerLine}}-per-line">
        <bcm-chart
            s-ref="chart"
            title="{{item.name}}"
            api-type="metricName"
            showbigable="{{true}}"
            apiType="dimensions"
            scope="{{scope}}"
            height="{{300}}"
            statistics="{{item.statistics}}"
            dimensions="{{dimensions}}"
            metrics="{{item.metrics}}"
            startTime="{{startTime}}"
            endTime="{{endTime}}"
            period="{{period}}"
            unit="{{item.unit}}"
            sdk="{{bcmSdk}}"
            options="{{options}}"
            seriesOption="{{seriesOption}}"
            legend="{{legend}}"
            proccessor="{{proccessor}}"
            connect-nulls="{{true}}"
            error="{{error}}"
        >
            <span slot="extra-title" class="title-select">
                <slot name="filter-title"/>
            </span>
            <div slot="detail-extra-filter-item" class="filter-item">
                <slot name="filter-item" />
            </div>
        </bcm-chart>
    </div>
    `;
    initData() {
        return {
            bcmSdk: new BcmSDK({client: new HttpClient({}, {
                    getCsrfToken() {
                        return this.$cookie.get('bce-user-info');
                    },
                    getCurrentRegion() {
                        return window.$context.getCurrentRegion();
                    }
            }), context: window.$context}),
            seriesOption: {
                type: 'line',
                chart: {
                    symbol: 'none'
                }
            },
            monitorDefaultPeriod: MONITOR_DEFAULT_PERIOD,
            queryProccessor: this.queryProccessor.bind(this),
            proccessor: this.proccessor.bind(this),
            scope: SCOPE,
            shortcutDateRange: [],
            options: {
                grid: {
                    left: '70px',  // 左侧内边距
                    right: '24px'  // 右侧内边距
                },
                yAxis: {
                    axisLabel: {
                        fontSize: 10  // 调整字体大小
                    }
                },
                dataZoom: {
                    type: 'slider',
                    handleIcon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAICAIAAABPmPnhAAAABmJLR0QA/wD/AP+gvaeTAAAAIUlEQVQImWNUyfjEgBsw4ZFDkb49nReNQYpucqQZKXI5AM9uBgn5QGRyAAAAAElFTkSuQmCC',
                    handleSize: '120%',
                    height: '8px',
                    backgroundColor: '#f4f4f2',
                    fillerColor: '#E6F0FF',
                    borderColor: 'transparent',
                    bottom: 10,
                    moveHandleStyle: {
                        opacity: 0,
                    },
                    dataBackground: {
                        lineStyle: {
                            opacity: 0,
                        },
                        areaStyle: {
                            opacity: 0,
                        },
                    },
                    selectedDataBackground: {
                        lineStyle: {
                            opacity: 0,
                        },
                        areaStyle: {
                            opacity: 0,
                        },
                    },
                },
                legend: {
                    type: 'scroll',
                    left: '40%',
                    itemHeight: 2,
                    itemWidth: 12,
                    scrollDataIndex: 0,
                    orient: 'horizontal',
                    align: 'left'
                },
                tooltip: {
                    trigger: 'axis',
                    confine: true,
                    backgroundColor: 'rgba(255,255,255)',
                    axisPointer: {
                        type: 'line',
                        lineStyle: {
                            color: '#B8BABF',
                            width: 1,
                            type: 'dashed'
                        }
                    },
                    extraCssText:
                        'box-shadow: 0 1px 10px 0 rgba(21,27,38,0.10);border-radius: 2px;',
                    formatter: (params) => {
                        const item = this.data.get('item');
                        let template = `<span style="font-size: 14px;color: #5C5F66;margin-right: 11px">${item.name} ${params[0].name}</span><br/>`;
                        
                        params.forEach(i => {
                            template += `<div style="width:12px;height:2px;background-color:${i.color};display:inline-block;margin-bottom:3px;margin-right:4px;"></div>
                                    <span style="font-size: 12px;color: #666666;margin-right: 11px">${i.seriesName}</span>
                                    <span style="font-size: 14px;color: #151B26;font-weight: 500;">${i.value}${this.getUnit()}</span>
                                </br>`;
                        });
                        return template;
                    }
                },
            },
            error: '',
            src: require("../../static/img/empty2.svg"),
            numPerLine: 2
        };
    }

    static components = {
        'bcm-chart': BcmChartPanel
    };

    static computed: SanComputedProps = {
        legend(): NormalObject {
            const hide = this.data.get('hideLegend');
            return {
                show: !hide,
                type: 'scroll',
                left: '40%',
                itemHeight: 2,
                itemWidth: 12,
                scrollDataIndex: 0,
                orient: 'horizontal',
                align: 'left'
            };
        }
    };

    inited() {
        this.watch('item', item => item && this.refresh());
        this.watch('dimensions', item => item && this.refresh());
    }

    getUnit() {
        return this.data.get('item.unit');
    }

    refresh() {
        try {
            this.nextTick(() => {
                const detailDialog = this.ref('chart')?.dialog;
                if (detailDialog && detailDialog.el) {
                    detailDialog.data.raw.detailConf = _.clone(this.ref('chart').data.raw.conf);
                    this.nextTick(() => detailDialog.handleRefresh());
                }
                this.ref('chart') && (this.ref('chart') as unknown as BcmChartPanel)?.loadMetrics();
            });
        } catch (error) {}
    }

    // 处理时间
    queryProccessor(data: {startTime: string, endTime: string, [x: string]: string | number}) {
        return {
            ...data,
            endTime: formatUtcTime(m(data.endTime)),
            startTime: formatUtcTime(m(data.startTime))
        };
    }

    proccessor(data) {
        let newData = { ...data };
        const item = this.data.get('item');
        const {src, deployId, type, hasNotInstallAuditPlugin} = this.data.get('');

        if (item.name === '慢查询数' && hasNotInstallAuditPlugin !== '') {
            const error = type === DETAIL_TYPE.united ? `<div style="width: 100%;height: 300px;display: flex;flex-direction: column;justify-content: center">
                <img src="${src}" style="margin-bottom: 24px;margin: 0 auto;width: 93px;height: 90px;">
                <p
                    style="font-size: 14px;color: #151B26;text-align: center;line-height: 22px;font-weight: 500;"
                >
                    ${hasNotInstallAuditPlugin}
                </p>
                <p
                    style="font-size: 12px;color: #84868C;text-align: center;line-height: 20px;font-weight: 400;"
                >
                    请先到查询分析页面开启审计日志插件。<a href="#/palo/detail?deployId=${deployId}&page=4" target="_blank">前往开启</a>
                </p>
            </div>` : `<div style="width: 100%;height: 300px;display: flex;flex-direction: column;justify-content: center">
                <img src="${src}" style="margin-bottom: 24px;margin: 0 auto;width: 93px;height: 90px;">
                <p
                    style="font-size: 14px;color: #151B26;text-align: center;line-height: 22px;font-weight: 500;"
                >
                    ${hasNotInstallAuditPlugin}
                </p>
                <p
                    style="font-size: 12px;color: #84868C;text-align: center;line-height: 20px;font-weight: 400;"
                >
                    请通过以下SQL命令开启审计日志 set global enable_audit_plugin = true
                </p>
            </div>`;
            this.data.set('error', error);
        }
        newData.series = _.map(data.series, seriesItem => ({
            data: this.handleData(seriesItem.data),
            name: seriesItem.name.split(',')[1],
        }));
        return newData;
    }

    // 将返回数据的数字转化精确到两位
    handleData(data: Array<NormalObject>) {
        return _.map(data, item => {
            Object.keys(item).forEach((key: string) => {
                const itemKey = item[key];
                if (typeof itemKey === 'number') {
                    item[key] = Number(itemKey.toFixed(2));
                }
            });
            return item;
        });
    }
}
  
  