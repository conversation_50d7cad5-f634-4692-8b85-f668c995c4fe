/**
 * 创建按钮
 *
 * @file create-btn.ts
 * <AUTHOR>
 */
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Button, Popover} from '@baidu/sui';
import {OutlinedPlus} from '@baidu/sui-icon';

import './index.less';

export default class extends Component {
    static template = html`
    <span class="create-btn">
        <s-popover s-if="{{disabled}}" placement="top" content="{{disableText}}">
            <s-button
                on-click="onClick"
                skin="{{skin}}"
                disabled
            >
                <outlined-plus class="create-icon" />
                {{text}}
            </s-button>
        </s-popover>
        <template s-else>
            <s-button
                on-click="onClick"
                skin="{{skin}}"
            >
                <outlined-plus class="create-icon" />
                {{text}}
            </s-button>
        </template>
    </span>`;

    static components = {
        's-button': Button,
        'outlined-plus': OutlinedPlus,
        's-popover': Popover,
    };

    initData() {
        return {
            skin: 'primary'
        };
    }

    onClick(e: Event) {
        this.fire('click', e);
    }
}