import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {Input, Tooltip} from '@baidu/sui';
import {CheckCircle2, PaymentFailed, Conceal, Display} from '@baidu/xicon-san';
import {isValidPassword} from '@common/utils/rules';

const tempalte = html`
    <template>
        <s-tooltip placement="right">
            <s-input
                width="{{inputWidth}}"
                type="{{passwordType}}"
                autocomplete="new-password"
                value="{= value =}"
                on-change="onChange"
                on-input="onInput"
                on-focus="onFocus"
                on-blur="onBlur"
                on-enter="onEnter"
                >
                <xicon-display
                    s-if="{{passwordEye === 'eye'}}"
                    slot="suffix"
                    theme="line"
                    color="#000"
                    size="{{16}}"
                    strokeLinejoin="round"
                    on-click="handlePasswordEyeClick"
                />
                <xicon-conceal
                    s-else
                    theme="line"
                    slot="suffix"
                    color="#000"
                    size="{{16}}"
                    strokeLinejoin="round"
                    on-click="handlePasswordEyeClick"
                />
            </s-input>
            <div slot="content" class="new-pwd-layer">
                <p class="new-pwd-layer-title">出于安全考虑，密码需满足以下条件：</p>
                <div s-for="item,index in passwordRule">
                    <check-icon
                        s-if="item.status"
                        class="mr4"
                        theme="filled"
                        color="#30bf13"
                        size="{{14}}"
                        strokeLinejoin="round"
                    />
                    <payment-failed
                        s-else
                        class="mr4"
                        theme="filled"
                        color="#F33E3E"
                        size="{{14}}"
                        strokeLinejoin="round"
                    />
                    {{item.text}}
                </div>
            </div>
        </s-tooltip>
    </template>
`;

export class PasswordInput extends Component {
    static template = tempalte;

    static components = {
        's-input': Input,
        'check-icon': CheckCircle2,
        'payment-failed': PaymentFailed,
        'xicon-conceal': Conceal,
        'xicon-display': Display,
        's-tooltip': Tooltip
    }


    static computed = {
        passwordRule() {
            const password = this.data.get('value');
            const reg2 = /^[A-Za-z\d!@#$%^&*()_+\-=\[\]{};:,<.>/?|]+$/;

            let passwordRule = [
                {
                    text: '8-16位字符',
                    status: false
                },
                {
                    text: '大小写英文、数字、特殊符号，至少包含其中三种',
                    status: false
                },
                {
                    text: '符号仅限!@#$%^&*()_+-=[]{};:,<.>/?|',
                    status: false
                },
            ];

            if (password.length > 7 && password.length < 17) {
                passwordRule[0].status = true;
            }
            if (isValidPassword(password)) {
                passwordRule[1].status = true;
            }
            if (reg2.test(password)) {
                passwordRule[2].status = true;
            }

            return passwordRule;
        },
        inputWidth() : number{
            const width: number | string = this.data.get('width');
            return width ? (+width - 20) : 180;
        },
        passwordType () : string {
            const eyeToType: any = {
                eye: 'text',
                eyeclose: 'password'
            }
            return eyeToType[this.data.get('passwordEye')];
        }
    }

    initData() {
        return {
            passwordEye: 'eyeclose'
        }
    }
    /**
     * 隐藏/显示密码
     */
     handlePasswordEyeClick() {
        let eye = this.data.get('passwordEye');
        this.data.set('passwordEye', eye === 'eyeclose' ? 'eye' : 'eyeclose');
    }

    onChange(e: any) {
        this.fire('change', e);
    }

    onInput(e: any) {
        this.fire('input', e);
    }

    onBlur(e: any) {
        this.fire('blur', e);
    }

    onFocus(e: any) {
        this.fire('focus', e);
    }

    onEnter(e: any) {
        this.fire('enter', e);
    }
}